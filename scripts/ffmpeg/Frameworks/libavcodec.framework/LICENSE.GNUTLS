LICENSING
=========

Since GnuTLS version 3.1.10, the core library is released under
the GNU Lesser General Public License (LGPL) version 2.1 or later
(see doc/COPYING.LESSER for the license terms).

The GNU LGPL applies to the main GnuTLS library, while the
included applications as well as gnutls-openssl 
library are under the GNU GPL version 3.  The gnutls library is 
located in the lib/ and libdane/ directories, while the applications
in src/ and, the gnutls-openssl library is at extra/.

The documentation in doc/ is under the GNU FDL license 1.3.


Note, however, that the nettle and the gmp libraries which are
GnuTLS dependencies, they are distributed under a LGPLv3+ or GPLv2+ dual
license. As such binaries linking to them need to adhere to either LGPLv3+
or the GPLv2+ license.

For any copyright year range specified as YYYY-ZZZZ in this package
note that the range specifies every single year in that closed interval.

