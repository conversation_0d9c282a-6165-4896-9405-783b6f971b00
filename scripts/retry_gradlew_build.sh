#!/bin/bash

# 进入脚本所在目录的上一级（项目根目录）
cd "$(dirname "$0")/.."

cd android || { echo "找不到android目录"; exit 1; }

max_retries=1000
count=0

while [ $count -lt $max_retries ]; do
    echo "第$((count+1))次尝试: ./gradlew clean build"
    ./gradlew clean build && break
    count=$((count+1))
    echo "构建失败，准备重试..."
    sleep 2
    if [ $count -eq $max_retries ]; then
        echo "已达到最大重试次数($max_retries)，仍然失败。"
        exit 1
    fi
    # 可选：每10次长等一会
    if (( count % 10 == 0 )); then
        echo "多次失败，休息10秒..."
        sleep 10
    fi
done

echo "构建成功！" 