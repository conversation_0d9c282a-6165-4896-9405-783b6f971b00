#!/bin/bash

# 开始时间
start_time=$(date +%s)

# 分割视频
ffmpeg -i /Users/<USER>/Documents/Emily.in.Paris.2020.S01E03.HDR.2160p.WEBRip.x265-iNSPiRiT.mkv -c copy -map 0 -segment_time 00:05:00 -f segment -reset_timestamps 1 output_segment_%03d.mkv

# 压缩每个片段
for f in output_segment_*.mkv; do
  ffmpeg -i "$f" -vf "scale=-1:1080" -c:v libx264 -preset ultrafast -crf 28 -c:a copy "compressed_$f"
done

# 生成合并文件列表
for f in compressed_output_segment_*.mkv; do echo "file '$f'" >> filelist.txt; done

# 合并压缩后的片段
ffmpeg -f concat -safe 0 -i filelist.txt -c copy output_1080p_video.mkv

# 清理临时文件
rm output_segment_*.mkv
rm compressed_output_segment_*.mkv
rm filelist.txt

# 结束时间
end_time=$(date +%s)

# 计算总耗时
elapsed_time=$((end_time - start_time))
echo "总耗时: $elapsed_time 秒"