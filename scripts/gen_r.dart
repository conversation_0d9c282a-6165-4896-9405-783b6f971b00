import 'dart:io';

class ResourceGenerator {
  final String assetsRootDir;
  final String outputFilePath;
  final String relativePathPrefix;

  ResourceGenerator({
    this.assetsRootDir = '../assets/images',
    this.outputFilePath = '../lib/r.dart',
    this.relativePathPrefix = 'assets/images',
  });

  void generate() {
    final buffer = StringBuffer();
    buffer.writeln('// GENERATED CODE - DO NOT MODIFY BY HAND');
    buffer.writeln('// *********************************************************\n');
    buffer.writeln('class R {');

    _generateAssetsCode(assetsRootDir, '', buffer);

    buffer.writeln('}');
    buffer.writeln('// *********************************************************');

    File(outputFilePath).writeAsStringSync(buffer.toString());
    print('Assets code generated in $outputFilePath');
  }

  bool _isImageFile(FileSystemEntity entity) {
    const List<String> imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp','.svg'];
    return entity is File && imageExtensions.any((ext) => entity.path.endsWith(ext));
  }

  void _generateAssetsCode(String directoryPath, String pathForClass, StringBuffer buffer) {
  final dir = Directory(directoryPath);
  if (!dir.existsSync()) {
    print('The specified directory does not exist');
    return;
  }

  List<FileSystemEntity> entities = dir.listSync(recursive: false);
  for (var entity in entities) {
    String entityPath = entity.path.replaceAll('\\', '/');
    if (_isImageFile(entity)) {
      String key = _generateKeyFromPath(entityPath, pathForClass);
          // Format the asset path according to Flutter's pubspec.yaml asset directory structure
      String path = entityPath.replaceFirst(RegExp(r'\.\./assets/images/'), 'assets/images/');
      buffer.writeln("  static const String $key = '$path';");
    } else if (entity is Directory) {
      String folderName = entityPath.split('/').last;
      String newPathForClass = pathForClass.isEmpty ? folderName : '${pathForClass}_$folderName';
      _generateAssetsCode(entityPath, newPathForClass, buffer);
    }
  }
}

  String _generateKeyFromPath(String path, String pathForClass) {
    // 使用正则表达式来移除文件的扩展名
    // 这个正则表达式匹配字符串最后的一个点和之后的所有字符
    RegExp regExp = RegExp(r'(\.)(?!.*\1)[^.]*$');
    String fileName = path.split('/').last.replaceAll(regExp, '');
    if(pathForClass.contains("images")){
      pathForClass = pathForClass.split("\\").last;
    }
    return pathForClass.isEmpty ? fileName : '${pathForClass}_$fileName';
  }
}

void main() {
  ResourceGenerator generator = ResourceGenerator();
  generator.generate();
}
