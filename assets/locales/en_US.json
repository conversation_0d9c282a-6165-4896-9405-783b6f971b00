{"net": {"connectionTimeout": "Connection Timeout", "sendTimeout": "Server exception, please try again later!", "receiveTimeout": "Network connection timeout, please check network settings", "badResponse": "Network connection timed out, please check network settings", "cancel": "The request has been canceled, please request again", "connectionError": "Network abnormality, please try again later!", "400": "Request syntax error", "401": "Unauthorized, please log in", "403": "Access Denied", "404": "Request address 404", "408": "Request timed out", "500": "Server exception", "501": "Service not implemented", "502": "Gateway error", "503": "Service is not available", "504": "Gateway timeout", "505": "HTTP version is not supported", "default_code_error": "Request failed, error code"}, "toast": {"submitSuccess": "SubmitSuccess", "submitFail": "SubmitFail", "saveSuccess": "SaveSuccess", "noData": "No Data"}, "setting": {"title": "Setting", "switchLanguage": "Switch Language", "switchTheme": "Switch Theme", "logout": "Logout", "uploadLog": "Upload Log", "clearCache": "<PERSON>ache", "about": "About", "orderList": "Order List", "voiceTransList": "VoiceTransList", "videoCompressList": "VideoCompressList"}}