{"applicationId": "1752032229000537", "dtLastResponse": "2025-07-10 20:13:28:546", "eof": 1, "params": {"app": {"applicationId": "1752032229000537", "sig": "2952d2c60f2484d8f983d522d5981eb1083855a6", "timestamp": "1752149601", "userId": "1941834967734571008"}, "audio": {"audioType": "opus", "channel": 1, "compress": "speex", "sampleBytes": 2, "sampleRate": 16000}, "coreProvideType": "cloud", "request": {"coreType": "para.eval", "paragraph_need_word_score": 1, "refText": "Android dependency should be downloaded from Maven", "tokenId": "686fae61332793736d000004"}}, "recordId": "686fae629abd8b1f0000bbaf", "refText": "Android dependency should be downloaded from Maven", "result": {"duration": "6.039", "fluency": 94, "integrity": 86, "kernel_version": "7.5.4", "numeric_duration": 6.039, "overall": 57, "pronunciation": 60, "resource_version": "5.2.0", "rhythm": 80, "sentences": [{"beginIndex": 0, "details": [{"charType": 0, "end": 198, "overall": 1, "prominence": 0, "start": 148, "word": "Android", "word_parts": [{"beginIndex": 0, "charType": 0, "endIndex": 6, "part": "Android"}]}, {"charType": 0, "end": 302, "overall": 81, "prominence": 0, "start": 198, "word": "dependency", "word_parts": [{"beginIndex": 8, "charType": 0, "endIndex": 17, "part": "dependency"}]}, {"charType": 0, "end": 343, "overall": 70, "prominence": 0, "start": 305, "word": "should", "word_parts": [{"beginIndex": 19, "charType": 0, "endIndex": 24, "part": "should"}]}, {"charType": 0, "end": 361, "overall": 55, "prominence": 0, "start": 346, "word": "be", "word_parts": [{"beginIndex": 26, "charType": 0, "endIndex": 27, "part": "be"}]}, {"charType": 0, "end": 424, "overall": 62, "prominence": 0, "start": 361, "word": "downloaded", "word_parts": [{"beginIndex": 29, "charType": 0, "endIndex": 38, "part": "downloaded"}]}, {"charType": 0, "end": 461, "overall": 76, "prominence": 0, "start": 439, "word": "from", "word_parts": [{"beginIndex": 40, "charType": 0, "endIndex": 43, "part": "from"}]}, {"charType": 0, "end": 504, "overall": 77, "prominence": 0, "start": 461, "word": "<PERSON><PERSON>", "word_parts": [{"beginIndex": 45, "charType": 0, "endIndex": 49, "part": "<PERSON><PERSON>"}]}], "end": 504, "endIndex": 49, "overall": 60, "sentence": "Android dependency should be downloaded from Maven", "start": 148}], "speed": 117, "warning": [{"code": 1004, "message": "Audio noisy!"}]}, "tokenId": "686fae61332793736d000004"}