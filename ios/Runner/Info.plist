<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>AppGroupId</key>
		<string>$(CUSTOM_GROUP_ID)</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>ShareMedia-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				</array>
			</dict>
		</array>
		<key>CFBundleDocumentTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeName</key>
				<string>Video Files</string>
				<key>LSHandlerRank</key>
				<string>Owner</string>
				<key>LSItemContentTypes</key>
				<array>
					<string>public.movie</string>
					<string>public.video</string>
				</array>
			</dict>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>LS100</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>lsenglish</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true />
		<key>NSLocalNetworkUsageDescription</key>
		<string>需要你的网络使用权限</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>我们需要您的同意来使用麦克风</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>需要你的相册使用权限</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>需要语音识别权限来转换您的语音</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UIBackgroundModes</key>
		<array>
			<string>audio</string>
		</array>
		<key>UIFileSharingEnabled</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen.storyboard</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
	</dict>
</plist>