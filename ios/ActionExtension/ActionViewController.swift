import receive_sharing_intent

import UIKit
import MobileCoreServices
import Photos

@available(swift, introduced: 5.0)
open class ActionViewController: UIViewController {
    var hostAppBundleIdentifier = ""
    var appGroupId = ""

    open override func viewDidLoad() {
        super.viewDidLoad()
        
        loadIds()
    }
    
    open override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        guard
            let content = extensionContext!.inputItems[0] as? NSExtensionItem,
            let attachments = content.attachments,
            let attachment = attachments.first
            else { return }
    
        if !attachment.hasItemConformingToTypeIdentifier(UTType.fileURL.identifier) { return }
        attachment.loadItem(forTypeIdentifier: UTType.fileURL.identifier) { [weak self] (data, error) in
            guard let s = self, let url = data as? URL else { return }
            s.handleMedia(forFile: url)
        }
    }
    
    private func loadIds() {
        let shareExtensionAppBundleIdentifier = Bundle.main.bundleIdentifier!

        let lastIndexOfPoint = shareExtensionAppBundleIdentifier.lastIndex(of: ".")
        hostAppBundleIdentifier = String(shareExtensionAppBundleIdentifier[..<lastIndexOfPoint!])
        let defaultAppGroupId = "group.\(hostAppBundleIdentifier)"
        
        let customAppGroupId = Bundle.main.object(forInfoDictionaryKey: kAppGroupIdKey) as? String
        
        appGroupId = customAppGroupId ?? defaultAppGroupId
    }
    
    private func handleMedia(forFile url: URL) {
        let fileName = url.lastPathComponent
        let newPath = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupId)!.appendingPathComponent(fileName)
        
        if !copyFile(at: url, to: newPath) { return }

        let newPathDecoded = newPath.absoluteString.removingPercentEncoding!;
        saveAndRedirect(data: SharedMediaFile(
            path: newPathDecoded,
            mimeType: url.mimeType(),
            type: .file
        ))
    }
    
    private func saveAndRedirect(data: SharedMediaFile) {
        let userDefaults = UserDefaults(suiteName: appGroupId)
        userDefaults?.set(toData(data: [data]), forKey: kUserDefaultsKey)
        userDefaults?.set(nil, forKey: kUserDefaultsMessageKey)
        userDefaults?.synchronize()
        redirectToHostApp()
    }
    
    private func redirectToHostApp() {
        loadIds()
        let url = URL(string: "\(kSchemePrefix)-\(hostAppBundleIdentifier):share")
        var responder = self as UIResponder?
        
        if #available(iOS 18.0, *) {
            while responder != nil {
                if let application = responder as? UIApplication {
                    application.open(url!, options: [:], completionHandler: nil)
                }
                responder = responder?.next
            }
        } else {
            let selectorOpenURL = sel_registerName("openURL:")
            
            while (responder != nil) {
                if (responder?.responds(to: selectorOpenURL))! {
                    _ = responder?.perform(selectorOpenURL, with: url)
                }
                responder = responder!.next
            }
        }
        extensionContext!.completeRequest(returningItems: [], completionHandler: nil)
    }
    
    private func copyFile(at srcURL: URL, to dstURL: URL) -> Bool {
        do {
            if FileManager.default.fileExists(atPath: dstURL.path) {
                try FileManager.default.removeItem(at: dstURL)
            }
            try FileManager.default.copyItem(at: srcURL, to: dstURL)
        } catch (let error) {
            print("Cannot copy item at \(srcURL) to \(dstURL): \(error)")
            return false
        }
        return true
    }
    
    private func toData(data: [SharedMediaFile]) -> Data {
        let encodedData = try? JSONEncoder().encode(data)
        return encodedData!
    }
}

extension URL {
    public func mimeType() -> String {
        if #available(iOS 14.0, *) {
            if let mimeType = UTType(filenameExtension: self.pathExtension)?.preferredMIMEType {
                return mimeType
            }
        } else {
            if let uti = UTTypeCreatePreferredIdentifierForTag(kUTTagClassFilenameExtension, self.pathExtension as NSString, nil)?.takeRetainedValue() {
                if let mimetype = UTTypeCopyPreferredTagWithClass(uti, kUTTagClassMIMEType)?.takeRetainedValue() {
                    return mimetype as String
                }
            }
        }
        
        return "application/octet-stream"
    }
}
