import 'dart:io';
import 'package:lsenglish/utils/subtitle/src/utils/subtitle_controller.dart';
import 'package:lsenglish/utils/subtitle/src/utils/subtitle_provider.dart';

void main() async {
  var subtitleController = SubtitleController(
    provider: SubtitleProvider.fromFile(File("Emily.in.Parismerge.srt")),
  );
  await subtitleController.initial();
  // subtitleController.mergeSubtitles(0, 2);
  // print(subtitleController.getIndexChanges());
  subtitleController.mergeSubtitles(1, 3);
  subtitleController.mergeSubtitles(0, 1);
  // print(subtitleController.getIndexChanges());
  // subtitleController.mergeSubtitles(0, 1);

// 0 1 2 3 4 5 6
// 0 3 4 5 6
// 0 3 5 6

// 服务端对应一个句子一个 index，DataEpisodeSentence中有多个DataEpisodeSentenceJson，后端查询对应的SubtitleId返回
// 比如有 3 个句子 对应的index 为 2,5,7
// 如果原来的这三个有修改，
// 
// 好像现在mergeSubtitles可以 再测试测试
// 方案：考虑只从后端取哪些 index 有数据，有的再判断
// 方案：考虑将字幕存储在后端？加一个 id？
// 方案：考虑subtitle 加个时间戳？
}
