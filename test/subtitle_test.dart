import 'dart:io';

import 'package:lsenglish/utils/subtitle/src/utils/subtitle_controller.dart';
import 'package:lsenglish/utils/subtitle/src/utils/subtitle_provider.dart';

void main() async {
  var subtitleController = SubtitleController(
    provider: SubtitleProvider.fromFile(File("Emily.in.Parismerge.srt")),
  );
  await subtitleController.initial();

  // var subtitle = subtitleController.findClosestSubtitleForward(Duration(seconds: 180));
  // print(subtitle);

  subtitleController.mergeSubtitles(1, 2);
  subtitleController.mergeSubtitles(0, 1);
  // print(subtitleController.generateIndexMap());
  // subtitleController.deleteSubtitle(6);
  // print(subtitleController.generateIndexMap());
  // subtitleController.mergeSubtitles(5,7);
  // print(subtitleController.generateIndexMap());
  // subtitleController.undoDelete();
  // subtitleController.deleteSubtitle(1);
  // subtitleController.deleteSubtitles(0,1);
  // await subtitleController.writeToSrtFile("MergedSubtitles.srt");
}
