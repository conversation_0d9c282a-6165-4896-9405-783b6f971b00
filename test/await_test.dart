import 'dart:async';

final Completer<void> completer1 = Completer<void>();
final Completer<void> completer2 = Completer<void>();

void main() async {
  // Complete completer1 first
  completer1.complete();

  // Call getResult and complete completer2 later
  getResult().then((_) => print("successsssssssssssssss"));

  // Simulate a delay before completing completer2
  Future.delayed(Duration(seconds: 1), () {
    completer2.complete();
  });
}

Future<void> getResult() async {
  await Future.wait([
    completer1.future,
    completer2.future,
  ]);
}