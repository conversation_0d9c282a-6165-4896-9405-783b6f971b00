import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'dart:convert';

double calculateFrameRate(String rFrameRate) {
  List<String> parts = rFrameRate.split('/');
  double numerator = double.parse(parts[0]);
  double denominator = double.parse(parts[1]);
  return numerator / denominator;
}

Future<Map<String, dynamic>> getVideoInfo(String filePath) async {
  final String command = '-v error -select_streams v:0 -show_entries stream=width,height,r_frame_rate -of json $filePath';

  final session = await FFmpegKit.execute(command);
  final returnCode = await session.getReturnCode();
  if (ReturnCode.isSuccess(returnCode)) {
    final output = await session.getOutput();
    final jsonOutput = json.decode(output ?? "");
    final stream = jsonOutput['streams'][0];
    final width = stream['width'];
    final height = stream['height'];
    final rFrameRate = stream['r_frame_rate'];

    return {
      'width': width,
      'height': height,
      'frameRate': calculateFrameRate(rFrameRate),
    };
  } else {
    throw Exception('Failed to get video info. Return code: $returnCode');
  }
}

void main() async {
  // 替换为你的视频文件路径
  String filePath = '/Users/<USER>/Documents/Emily.in.Paris.2020.S01E03.HDR.2160p.WEBRip.x265-iNSPiRiT.mkv';

  final videoInfo = await getVideoInfo(filePath);

  const int maxWidth = 3840; // 4K 分辨率宽度
  const int maxHeight = 2160; // 4K 分辨率高度
  const double maxFrameRate = 30.0;
  videoInfo['width'];
  videoInfo['height'];
  videoInfo['frameRate'];
}
