import 'package:lsenglish/utils/string.dart';

void main() {
  // 示例文本
  List<String> examples = [
    '''
你可以帮我们选
我“Usine de Insolence”
you ssこんにちは
You can help us choose.
I like Usine d'Insolence.
    '''
  ];

  // 处理每个示例
  for (var text in examples) {
    var splitList = splitText(text);
    var englishText = splitList.first;
    var otherText = splitList.second;
    print("englishText=\n$englishText \n\notherText=\n$otherText");
  }
}
