# Flutter项目编码规范文档

## 目录
1. [项目概述](#项目概述)
2. [架构模式](#架构模式)
3. [文件组织结构](#文件组织结构)
4. [命名规范](#命名规范)
5. [代码风格](#代码风格)
6. [数据处理规范](#数据处理规范)
7. [状态管理规范](#状态管理规范)
8. [网络层规范](#网络层规范)
9. [错误处理规范](#错误处理规范)
10. [特定功能模块规范](#特定功能模块规范)
11. [代码质量检查清单](#代码质量检查清单)
12. [重构建议](#重构建议)

## 项目概述

本项目是基于Flutter框架开发的英语学习应用，采用GetX状态管理框架，实现了语音评估、视频学习、数据统计等核心功能。

### 技术栈
- **框架**: Flutter 3.2.3+
- **状态管理**: GetX 4.6.6
- **网络请求**: Dio 5.3.3 + Retrofit 4.6.0
- **数据序列化**: JsonSerializable 6.7.1
- **本地存储**: GetStorage 2.1.1
- **日志**: Logger 2.6.0

## 架构模式

### GetX MVC架构
项目严格遵循GetX的MVC架构模式：

```
lib/app/modules/[module_name]/
├── bindings/           # 依赖注入绑定
│   └── [module]_binding.dart
├── controllers/        # 业务逻辑控制器
│   └── [module]_controller.dart
└── views/             # UI视图层
    └── [module]_view.dart
```

### 核心原则
1. **单一职责**: 每个Controller只负责一个功能模块
2. **依赖注入**: 通过Binding管理依赖关系
3. **响应式编程**: 使用Rx变量实现数据绑定
4. **生命周期管理**: 合理使用onInit、onReady、onClose

## 文件组织结构

### 标准目录结构
```
lib/
├── app/                    # 应用核心模块
│   ├── modules/           # 功能模块
│   └── routes/            # 路由配置
├── base/                  # 基础类
├── config/                # 配置文件
├── model/                 # 数据模型
├── net/                   # 网络层
├── utils/                 # 工具类
├── widgets/               # 通用组件
└── main.dart             # 应用入口
```

### 模块内部结构
每个功能模块应包含：
- `bindings/`: 依赖注入配置
- `controllers/`: 业务逻辑控制器
- `views/`: UI视图文件
- `widgets/`: 模块专用组件（可选）

## 命名规范

### 文件命名
- **文件名**: 使用小写字母和下划线，如 `home_controller.dart`
- **文件夹名**: 使用小写字母和下划线，如 `speech_evaluation/`
- **模块名**: 使用小写字母，如 `home`, `detail`, `datacenter`

### 类命名
```dart
// ✅ 正确示例
class HomeController extends LoadingGetxcontroller {}
class SpeechEvaluationResult {}
class UserLoginResp {}

// ❌ 错误示例
class homeController {}
class speechevaluationresult {}
```

### 变量和方法命名
```dart
// ✅ 正确示例
var watchHistorys = <WatchHistoryRespWrap>[].obs;
void getWatchHistory() {}
final String? tokenId;

// ❌ 错误示例
var WatchHistorys = <WatchHistoryRespWrap>[].obs;
void GetWatchHistory() {}
final String? TokenId;
```

### 常量命名
```dart
// ✅ 正确示例
class PlayerMenuId {
  static const int note = 1;
  static const int ai = 2;
}

// 配置常量
static const Duration _backupInterval = Duration(seconds: 30);
```

## 代码风格

### 导入顺序
```dart
// 1. Dart核心库
import 'dart:convert';
import 'dart:async';

// 2. Flutter框架库
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

// 3. 第三方包
import 'package:get/get.dart';
import 'package:dio/dio.dart';

// 4. 项目内部导入
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/log.dart';

// 5. 相对导入
import '../controllers/home_controller.dart';
```

### 代码格式化
- 使用2个空格缩进
- 行长度限制为80字符（可适当放宽到100字符）
- 使用trailing comma提高可读性

```dart
// ✅ 正确示例
Widget build(BuildContext context) {
  return GetMaterialApp(
    debugShowCheckedModeBanner: false,
    title: "LS100",
    theme: lightThemeData,
    darkTheme: darkThemeData,
    initialBinding: SplashBinding(),
    initialRoute: Routes.SPLASH,
  );
}
```

## 数据处理规范

### JsonSerializable使用规范

#### 标准模型类结构
```dart
import 'package:json_annotation/json_annotation.dart';

part 'model_name.g.dart';

@JsonSerializable(explicitToJson: true)
class ModelName {
  final String? field1;
  @JsonKey(name: 'custom_field_name')
  final int? field2;
  
  ModelName({
    this.field1,
    this.field2,
  });
  
  factory ModelName.fromJson(Map<String, dynamic> json) => 
      _$ModelNameFromJson(json);
  Map<String, dynamic> toJson() => _$ModelNameToJson(this);
}
```

#### 复杂类型处理
```dart
// 自定义转换器
class ResultConverter implements JsonConverter<dynamic, dynamic> {
  const ResultConverter();
  
  @override
  dynamic fromJson(dynamic json) => json;
  
  @override
  dynamic toJson(dynamic object) => object;
}

// 使用转换器
@JsonSerializable(explicitToJson: true)
class SpeechEvaluationResult {
  @ResultConverter()
  final dynamic result;
  
  // 自定义fromJson处理复杂逻辑
  factory SpeechEvaluationResult.fromJson(Map<String, dynamic> json) {
    final instance = _$SpeechEvaluationResultFromJson(json);
    final coreType = json['params']?['request']?['coreType'] ?? '';
    dynamic resultObj;
    
    if (json['result'] != null) {
      if (coreType == 'sent.eval') {
        resultObj = SentEvalResult.fromJson(json['result']);
      } else if (coreType == 'para.eval') {
        resultObj = ParaEvalResult.fromJson(json['result']);
      }
    }
    
    return SpeechEvaluationResult(
      result: resultObj,
      // ... 其他字段
    );
  }
}
```

### 类型安全处理
```dart
// ✅ 使用可空类型
final String? optionalField;
final int? nullableNumber;

// ✅ 提供默认值
final String name = json['name'] ?? '';
final int count = json['count'] ?? 0;

// ✅ 安全的类型转换
final int? value = (json['value'] as num?)?.toInt();
```

## 状态管理规范

### GetX响应式变量使用

#### 基本响应式变量
```dart
class HomeController extends LoadingGetxcontroller {
  // ✅ 列表类型
  var watchHistorys = <WatchHistoryRespWrap>[].obs;
  
  // ✅ 对象类型
  var user = User().obs;
  
  // ✅ 基本类型
  var currentStage = 0.obs;
  var isLoading = false.obs;
}
```

#### 复杂状态管理
```dart
class GuideController extends GetxController {
  // 使用Rx包装复杂对象
  Rx<LearningPlanResp?> learningPlanResp = Rx<LearningPlanResp?>(null);
  
  // 多选状态管理
  final selectedMotivationIndices = <int>[].obs;
  final selectedAbilityIndices = <int>[].obs;
}
```

### 状态更新模式
```dart
// ✅ 直接赋值更新
watchHistorys.assignAll(data.data);

// ✅ 单个元素更新
user.value = newUser;
user.refresh(); // 手动触发更新

// ✅ 列表操作
selectedIndices.add(index);
selectedIndices.remove(index);
```

### 生命周期管理
```dart
class BaseStreamController extends GetxController {
  final List<StreamSubscription?> subscriptions = [];
  
  @override
  void onInit() {
    super.onInit();
    subscriptions.clear();
    subscriptions.addAll([
      // 添加订阅
    ]);
  }
  
  @override
  void onClose() {
    for (final subscription in subscriptions) {
      subscription?.cancel();
    }
    super.onClose();
  }
}
```

## 网络层规范

### RestClient接口定义
```dart
@RestApi(baseUrl: '/api/v1/')
abstract class RestClient {
  factory RestClient(Dio dio) = _RestClient;
  
  @GET('endpoint')
  Future<ApiResult<ResponseType>> getMethod();
  
  @POST('endpoint')
  Future<ApiResult<ResponseType>> postMethod(
    @Body() Map<String, dynamic> data,
  );
  
  @MultiPart()
  @POST('upload')
  Future<ApiResult<List<String>?>> uploadFiles({
    @Part() required List<MultipartFile> files,
  });
}
```

### 网络请求处理
```dart
// ✅ 标准请求处理
void loadData() {
  Net.getRestClient().getData().then((response) {
    data.assignAll(response.data);
    setSuccess();
  }).catchError((error) {
    setError(message: error.toString());
  });
}

// ✅ 异步请求处理
Future<void> uploadData() async {
  try {
    final response = await Net.getRestClient().uploadData(data);
    // 处理成功响应
  } catch (e) {
    logger("上传失败: $e");
    // 处理错误
  }
}
```

### 错误处理和拦截器
```dart
class ApiInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.statusCode == 200) {
      final responseBody = response.data;
      if (responseBody is Map) {
        final status = responseBody['code'];
        if (status != null && status != 200) {
          if (status == 401) {
            _handleUnauthorizedError();
          } else {
            handler.reject(
              DioException(
                requestOptions: response.requestOptions,
                response: response,
                error: ApiException(responseBody['msg'], status),
              ),
            );
          }
        }
      }
    }
    super.onResponse(response, handler);
  }
}
```

## 错误处理规范

### 异常类型定义
```dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, [this.statusCode]);

  @override
  String toString() => 'ApiException: $message (Status code: $statusCode)';
}
```

### 统一错误处理模式
```dart
// ✅ Controller中的错误处理
void loadData() {
  setLoading();
  Net.getRestClient().getData().then((response) {
    data.assignAll(response.data);
    setSuccess();
  }).catchError((error) {
    logger("加载数据失败: $error");
    setError(message: "加载失败，请重试");
  });
}

// ✅ 异步方法错误处理
Future<void> uploadData() async {
  try {
    setLoading(message: "上传中...");
    final response = await Net.getRestClient().uploadData(data);
    setSuccess();
    toastInfo("上传成功");
  } catch (e, stackTrace) {
    logger("上传失败: $e");
    logger("StackTrace: $stackTrace");
    setError(message: "上传失败: ${e.toString()}");
    toastError("上传失败，请重试");
  }
}
```

### 日志记录规范
```dart
// ✅ 使用统一的logger函数
logger("DataCenterTimeManager initialize start");
logger("语音评测结果: ${result.overall}");
logger("上传失败: $e");

// ✅ 调试信息
debugPrint("文件上传成功: ${response.data}");

// ✅ 错误日志包含堆栈信息
catch (e, stackTrace) {
  logger("操作失败: $e");
  logger("StackTrace: $stackTrace");
}
```

## 特定功能模块规范

### 语音评估模块

#### 单例模式实现
```dart
class SpeechEvaluation {
  SpeechEvaluation._internal();
  static final SpeechEvaluation _instance = SpeechEvaluation._internal();
  static SpeechEvaluation get instance => _instance;

  // 状态管理
  final isEngineInited = false.obs;
  final isRecording = false.obs;
  final errorNotifier = ''.obs;
}
```

#### 流式结果处理
```dart
// 中间结果监听器
final List<Function(SpeechEvaluationResult)> _intermediateListeners = [];
final List<Function(SpeechEvaluationResult)> _finalListeners = [];

void _handleStreamingResult(SpeechEvaluationResult result, String rawJson) {
  if (result.eof == 0) {
    // 中间结果
    for (final listener in _intermediateListeners) {
      listener(result);
    }
  } else if (result.eof == 1) {
    // 最终结果
    for (final listener in _finalListeners) {
      listener(result);
    }
    _enableRealtimeFeedback = false;
  }
}
```

#### 评测参数配置
```dart
Map<String, dynamic> requestParams = {
  "refText": refText,
  "coreType": coreType.coreTypeStr,
  "attachAudioUrl": 1,
};

// 段落评测特殊参数
if (coreType == SpeechEvalCoreType.paraEval) {
  requestParams["paragraph_need_word_score"] = 1;
}

// 流式反馈参数
if (_enableRealtimeFeedback) {
  requestParams["realtime_feedback"] = "1";
}
```

### 数据中心时间管理器

#### 数据结构设计
```dart
class SentenceLearningRecord {
  final int subtitleStartTime;
  final int subtitleEndTime;
  final int subtitleDuration;

  int playCount = 0;
  int playTotalDuration = 0;
  int recordCount = 0;
  int recordTotalDuration = 0;
  int lastInteractionTime = 0;

  // 计算属性
  double get playAvgDuration => playCount > 0 ? playTotalDuration / playCount : 0.0;
  double get recordAvgDuration => recordCount > 0 ? recordTotalDuration / recordCount : 0.0;
}
```

#### 定时备份机制
```dart
static const Duration _backupInterval = Duration(seconds: 30);
Timer? _backupTimer;

void _startBackupTimer() {
  _backupTimer?.cancel();
  _backupTimer = Timer.periodic(_backupInterval, (timer) {
    if (_hasUnsavedChanges && !_isPaused) {
      _saveToLocal();
    }
  });
}
```

#### 数据上传策略
```dart
Future<void> uploadLearningData() async {
  if (_sentenceRecords.isEmpty) return;

  final uploadData = EpisodeLsDataResp(
    resourceId: _resourceId,
    resourceType: _resourceType,
    lsTimes: _lsTimes,
    totalLearnDuration: duration,
    sentences: _sentenceRecords.values.toList(),
  );

  try {
    await Net.getRestClient().addDataEpisode(uploadData.toJson());
    await clear(); // 上传成功后清理本地数据
    ObsUtil().updateDataCenter.value = DateTime.now().millisecond;
  } catch (e) {
    logger("上传失败: $e");
    rethrow; // 重新抛出异常，让调用方处理
  }
}
```

### OSS文件上传

#### 签名生成
```dart
static Future<String?> _upload({
  required File file,
  String rootDir = '',
}) async {
  DateTime currentTime = DateTime.now().toUtc();
  DateTime expirationTime = currentTime.add(const Duration(days: 1));
  String expiration = expirationTime.toIso8601String();

  var bucket = Config().isDev ? devBucket : prodBucket;
  String policyText = '{"expiration": "$expiration","conditions": [{"bucket": "$bucket" },["content-length-range", 0, 1048576000]]}';
  String signature = getSignature(policyText);

  // 构建上传参数...
}
```

## 代码质量检查清单

### 架构层面
- [ ] 是否遵循GetX MVC架构模式
- [ ] Controller是否继承正确的基类（GetxController/LoadingGetxcontroller）
- [ ] 是否正确使用Binding进行依赖注入
- [ ] 路由配置是否规范

### 命名规范
- [ ] 文件名是否使用小写字母和下划线
- [ ] 类名是否使用PascalCase
- [ ] 变量和方法名是否使用camelCase
- [ ] 常量是否使用正确的命名方式

### 状态管理
- [ ] 是否正确使用obs变量
- [ ] 是否在onClose中取消订阅
- [ ] 状态更新是否使用正确的方法
- [ ] 是否避免不必要的状态更新

### 数据处理
- [ ] 模型类是否使用JsonSerializable
- [ ] 是否正确处理可空类型
- [ ] 是否提供合理的默认值
- [ ] 复杂类型转换是否安全

### 网络层
- [ ] API接口是否使用Retrofit注解
- [ ] 错误处理是否完整
- [ ] 是否正确使用拦截器
- [ ] 请求参数是否类型安全

### 错误处理
- [ ] 是否捕获所有可能的异常
- [ ] 错误信息是否用户友好
- [ ] 是否记录详细的错误日志
- [ ] 是否提供重试机制

### 性能优化
- [ ] 是否避免不必要的Widget重建
- [ ] 列表是否使用懒加载
- [ ] 图片是否正确缓存
- [ ] 是否及时释放资源

## 重构建议

### 优先级高的重构项

#### 1. 统一错误处理机制
**现状**: 错误处理分散在各个Controller中，缺乏统一性
**建议**: 创建统一的错误处理服务
```dart
class ErrorHandlerService {
  static void handleApiError(dynamic error, {String? customMessage}) {
    String message = customMessage ?? _getErrorMessage(error);
    logger("API错误: $error");
    toastError(message);
  }

  static String _getErrorMessage(dynamic error) {
    if (error is ApiException) {
      return error.message;
    } else if (error is DioException) {
      return "网络请求失败";
    }
    return "未知错误";
  }
}
```

#### 2. 优化状态管理基类
**现状**: LoadingGetxcontroller功能单一
**建议**: 扩展基类功能
```dart
abstract class BaseController extends GetxController {
  final List<StreamSubscription?> subscriptions = [];

  // 统一的加载状态管理
  var isLoading = false.obs;
  var errorMessage = ''.obs;

  // 统一的网络请求处理
  Future<T> safeRequest<T>(Future<T> Function() request) async {
    try {
      isLoading.value = true;
      return await request();
    } catch (e) {
      ErrorHandlerService.handleApiError(e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }
}
```

#### 3. 改进日志系统
**现状**: 使用简单的logger函数
**建议**: 实现结构化日志
```dart
class LogService {
  static void info(String message, {Map<String, dynamic>? extra}) {
    _log('INFO', message, extra);
  }

  static void error(String message, {dynamic error, StackTrace? stackTrace}) {
    _log('ERROR', message, {'error': error?.toString(), 'stackTrace': stackTrace?.toString()});
  }

  static void _log(String level, String message, Map<String, dynamic>? extra) {
    final timestamp = DateTime.now().toIso8601String();
    final logData = {
      'timestamp': timestamp,
      'level': level,
      'message': message,
      if (extra != null) ...extra,
    };
    print(jsonEncode(logData));
  }
}
```

### 优先级中等的重构项

#### 1. 模型类优化
- 为复杂模型添加copyWith方法
- 实现模型的相等性比较
- 添加模型验证逻辑

#### 2. 网络层增强
- 实现请求重试机制
- 添加请求缓存策略
- 优化文件上传进度显示

#### 3. 工具类整理
- 合并功能相似的工具类
- 为工具类添加单元测试
- 优化工具类的API设计

### 优先级低的重构项

#### 1. UI组件抽象
- 提取通用的UI组件
- 实现主题系统
- 优化响应式布局

#### 2. 国际化完善
- 完善多语言支持
- 实现动态语言切换
- 优化文本资源管理

## 总结

本编码规范文档基于lsenglish项目的实际代码分析制定，旨在：

1. **提高代码质量**: 通过统一的编码标准确保代码的可读性和可维护性
2. **降低维护成本**: 规范的架构和命名使新团队成员能快速上手
3. **提升开发效率**: 标准化的开发流程减少重复工作
4. **保证项目稳定性**: 完善的错误处理和测试机制

建议团队成员：
- 在开发新功能时严格遵循本规范
- 定期review现有代码，逐步重构不符合规范的部分
- 持续更新和完善本规范文档
- 建立代码review机制确保规范的执行

通过持续的规范化改进，项目将具备更好的可扩展性和可维护性，为长期发展奠定坚实基础。

---

**文档版本**: v1.0
**最后更新**: 2025-07-16
**适用项目**: lsenglish Flutter应用
**维护者**: 开发团队
