#!/bin/bash

# 配置参数
MAX_RETRIES=100000     # 最大重试次数
RETRY_DELAY=3      # 重试间隔（秒）
POD_COMMAND="pod install"  # 可替换为其他命令，如 "pod install --repo-update"
cd ios
# 执行逻辑
attempt=1
while [[ $attempt -le $MAX_RETRIES ]]; do
  echo "尝试执行 $POD_COMMAND (第 $attempt 次，共 $MAX_RETRIES 次)..."
  
  $POD_COMMAND
  exit_code=$?

  if [[ $exit_code -eq 0 ]]; then
    echo "✅ pod install 成功"
    exit 0
  else
    echo "❌ pod install 失败，错误码: $exit_code"
    echo "等待 $RETRY_DELAY 秒后重试..."
    sleep $RETRY_DELAY
    ((attempt++))
  fi
done

echo "⚠️  已达到最大重试次数 $MAX_RETRIES，仍然失败"
exit 1