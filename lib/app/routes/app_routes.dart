part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const MAIN = _Paths.MAIN;
  static const DETAIL = _Paths.DETAIL;
  static const SUBTITLE = _Paths.SUBTITLE;
  static const SUBTITLE_ADD = _Paths.SUBTITLE + _Paths.SUBTITLE_ADD;
  static const SUBTITLE_SEARCH = _Paths.SUBTITLE + _Paths.SUBTITLE_SEARCH;
  static const SUBTITLE_PREVIEW = _Paths.SUBTITLE + _Paths.SUBTITLE_PREVIEW;
  static const SUBTITLE_SKIP = _Paths.SUBTITLE + _Paths.SUBTITLE_SKIP;
  static const LOGIN = _Paths.LOGIN;
  static const SETTING = _Paths.SETTING;
  static const HISTORY = _Paths.HISTORY;
  static const NOTELIST = _Paths.NOTELIST;
  static const SUBTITLE_EDIT = _Paths.SUBTITLE + _Paths.SUBTITLE_EDIT;
  static const TAB = _Paths.TAB;
  static const DATACENTER = _Paths.DATACENTER;
  static const MINE = _Paths.MINE;
  static const AUDIO_CONVERT = _Paths.MINE + _Paths.AUDIO_CONVERT;
  static const VIDEO_COMPRESS = _Paths.MINE + _Paths.VIDEO_COMPRESS;
  static const EPISODEDATA = _Paths.DATACENTER + _Paths.EPISODEDATA;
  static const EPISODELIST = _Paths.DATACENTER + _Paths.EPISODELIST;
  static const RESOURCELIB = _Paths.RESOURCELIB;
  static const SERIES = _Paths.RESOURCELIB + _Paths.SERIES;
  static const PAY = _Paths.PAY;
  static const ORDERLIST = _Paths.ORDERLIST;
  static const WEB = _Paths.WEB;
  static const ALIYUNPAN = _Paths.ALIYUNPAN;
  static const GUIDE = _Paths.GUIDE;
  static const SPLASH = _Paths.SPLASH;
  static const LSDESC = _Paths.LSDESC;
  static const PLAN_GEN_DETAIL = _Paths.PLAN_GEN_DETAIL;
  static const LANGUAGE_SETTINGS = _Paths.LANGUAGE_SETTINGS;
  static const PLAN = _Paths.PLAN;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const MAIN = '/main';
  static const DETAIL = '/detail';
  static const SUBTITLE = '/subtitle';
  static const SUBTITLE_ADD = '/subtitle-add';
  static const SUBTITLE_SEARCH = '/subtitle-search';
  static const SUBTITLE_PREVIEW = '/subtitle-preview';
  static const SUBTITLE_SKIP = '/subtitle-skip';
  static const LOGIN = '/login';
  static const SETTING = '/setting';
  static const HISTORY = '/history';
  static const NOTELIST = '/notelist';
  static const SUBTITLE_EDIT = '/subtitle-edit';
  static const TAB = '/tab';
  static const DATACENTER = '/datacenter';
  static const MINE = '/mine';
  static const AUDIO_CONVERT = '/audio-convert';
  static const VIDEO_COMPRESS = '/video-compress';
  static const EPISODEDATA = '/episodedata';
  static const EPISODELIST = '/episodelist';
  static const RESOURCELIB = '/resourcelib';
  static const SERIES = '/series';
  static const PAY = '/pay';
  static const ORDERLIST = '/orderlist';
  static const WEB = '/web';
  static const ALIYUNPAN = '/aliyunpan';
  static const GUIDE = '/guide';
  static const SPLASH = '/splash';
  static const LSDESC = '/lsdesc';
  static const PLAN_GEN_DETAIL = '/plan-gen-detail';
  static const LANGUAGE_SETTINGS = '/language-settings';
  static const PLAN = '/plan';
}
