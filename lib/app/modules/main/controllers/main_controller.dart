import 'package:get/get.dart';
import 'package:lsenglish/app/modules/datacenter/controllers/datacenter_controller.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/datacenter_time_manager.dart';
import 'package:lsenglish/utils/login.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';

class MainController extends GetxController {
  var selectedIndex = 2.obs;
  final PersistentTabController persistentTabController = PersistentTabController(initialIndex: 2);

  @override
  void onInit() {
    super.onInit();
    DataCenterTimeManager().initialize();
    Config().themeMode.listen((p0) {
      update();
    });
  }

  void onItemTapped(int index) {
    selectedIndex.value = index;
    if (index == 1) {
      Get.find<DatacenterController>().onTabClick();
    }
  }

  @override
  void onReady() {
    super.onReady();
    if (!isLogin()) {
      Get.toNamed(Routes.LOGIN);
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
