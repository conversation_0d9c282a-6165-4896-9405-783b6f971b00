import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:back_button_interceptor/back_button_interceptor.dart';
import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';

class GuideController extends GetxController {
  final currentStage = 0.obs;
  final maxStages = 9; // 现在有9个阶段：欢迎、动力来源、提升能力、当前级别、目标级别、学习时间选择、加载页面、预测结果
  final selectedChoices = <String, dynamic>{}.obs; // Changed to dynamic to support both int? and List<int>
  final showNextButton = true.obs;

  // 学习计划响应数据
  Rx<LearningPlanResp?> learningPlanResp = Rx<LearningPlanResp?>(null);

  // Multi-selection maps for motivation and ability stages
  final selectedMotivationIndices = <int>[].obs;
  final selectedAbilityIndices = <int>[].obs;

  // 学习时间选择
  final selectedStudyTimeIndex = 1.obs; // 默认选择30分钟

  late PageController pageController;

  @override
  void onInit() {
    super.onInit();
    pageController = PageController(initialPage: currentStage.value);
    // 添加返回按钮拦截器
    BackButtonInterceptor.add(_backButtonInterceptor);
  }

  @override
  void onClose() {
    pageController.dispose();
    // 移除返回按钮拦截器
    BackButtonInterceptor.remove(_backButtonInterceptor);
    super.onClose();
  }

  // 返回按钮拦截器
  bool _backButtonInterceptor(bool stopDefaultButtonEvent, RouteInfo info) {
    // 调用已有的 onBack 方法
    onBack();
    return true; // 总是拦截返回按钮事件
  }

  final List<String> motivationChoices = [
    '升职或外派机会',
    '申请理想学校',
    '日常旅行',
    '融入国际社群',
    '自我成长',
    '其他',
  ];

  final List<String> abilityChoices = [
    '听',
    '说',
    '读',
    '写',
  ];

  final List<String> timeChoices = [
    '每天30分钟',
    '每天1小时',
    '每天2小时',
    '其他',
  ];

  final List<String> studyTimeOptions = [
    '15分钟',
    '30分钟',
    '45分钟',
    '1小时',
    '1.5小时',
    '2小时',
    '3小时',
    '4小时',
    '5小时',
  ];

  void nextStage() {
    if (currentStage.value < maxStages - 1) {
      currentStage.value++;
      // 添加调试信息
      _updateNextButtonVisibility();

      pageController.animateToPage(
        currentStage.value,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  // For single-choice stages
  void selectChoice(int stage, int index) {
    final stageKey = _getStageKey(stage);
    selectedChoices[stageKey] = index;

    update();

    Future.delayed(const Duration(milliseconds: 500), () {
      nextStage();
    });
  }

  // For multi-choice stages - toggle selection
  void toggleMultiChoice(int stage, int index) {
    final stageKey = _getStageKey(stage);

    if (stageKey == 'motivation') {
      if (selectedMotivationIndices.contains(index)) {
        selectedMotivationIndices.remove(index);
      } else {
        selectedMotivationIndices.add(index);
      }
      selectedChoices[stageKey] = selectedMotivationIndices.toList();

      // 同时保存选择的文本值
      List<String> values =
          selectedMotivationIndices.map((i) => i < motivationChoices.length ? motivationChoices[i] : '').where((s) => s.isNotEmpty).toList();
      selectedChoices['motivation_values'] = values;
    } else if (stageKey == 'ability') {
      if (selectedAbilityIndices.contains(index)) {
        selectedAbilityIndices.remove(index);
      } else {
        selectedAbilityIndices.add(index);
      }
      selectedChoices[stageKey] = selectedAbilityIndices.toList();

      // 同时保存选择的文本值
      List<String> values = selectedAbilityIndices.map((i) => i < abilityChoices.length ? abilityChoices[i] : '').where((s) => s.isNotEmpty).toList();
      selectedChoices['ability_values'] = values;
    }

    // 通知 GetBuilder 更新 UI
    update();
  }

  // For multi-choice stages - confirm selections and move to next stage
  void confirmMultiChoices(int stage) {
    update();
    nextStage();
  }

  String _getStageKey(int stage) {
    switch (stage) {
      case 0:
        return 'motivation';
      case 1:
        return 'ability';
      case 2:
        return 'time';
      case 3:
        return 'level';
      case 4:
        return 'study_time';
      default:
        return 'stage_$stage';
    }
  }

  // 为时间选择阶段添加的方法
  void selectTimeChoice(int index) {
    selectedStudyTimeIndex.value = index;
    selectedChoices['study_time'] = index;

    // 保存文本值
    if (index >= 0 && index < studyTimeOptions.length) {
      selectedChoices['study_time_value'] = studyTimeOptions[index];
    }

    update();

    // 直接设置当前阶段为加载页面阶段
    currentStage.value = 6; // 加载页面阶段的索引

    // 使用 animateToPage 而不是 nextStage
    pageController.animateToPage(
      currentStage.value,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  // 为当前级别选择阶段添加的方法
  void selectCurrentLevel(int index) {
    selectedChoices['current_level'] = index;

    // 保存文本值
    final levels = ['A0', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
    if (index >= 0 && index < levels.length) {
      selectedChoices['current_level_value'] = levels[index];
    }

    update();
    nextStage();
  }

  // 为目标级别选择阶段添加的方法
  void selectTargetLevel(int index) {
    selectedChoices['target_level'] = index;

    // 保存文本值
    final levels = ['A0', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
    if (index >= 0 && index < levels.length) {
      selectedChoices['target_level_value'] = levels[index];
    }

    update();

    // 直接设置当前阶段为时间选择阶段
    currentStage.value = 5; // 时间选择阶段的索引

    // 使用 animateToPage 而不是 nextStage
    pageController.animateToPage(
      currentStage.value,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  // For single-choice stages
  int? getSelectedIndexForStage(String stageKey) {
    final index = selectedChoices[stageKey];
    if (index is int?) {
      return index;
    }
    return null;
  }

  // For multi-choice stages
  List<int> getSelectedIndicesForStage(String stageKey) {
    if (stageKey == 'motivation') {
      return selectedMotivationIndices;
    } else if (stageKey == 'ability') {
      return selectedAbilityIndices;
    }
    return [];
  }

  void _updateNextButtonVisibility() {
    // 只在欢迎阶段显示下一步按钮
    showNextButton.value = currentStage.value == 0;
  }

  void onBack() {
    if (currentStage.value > 0) {
      currentStage.value--;
      _updateNextButtonVisibility();

      pageController.animateToPage(
        currentStage.value,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    } else {
      Get.back();
    }
  }
}
