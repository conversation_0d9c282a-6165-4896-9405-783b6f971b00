import 'package:flutter/material.dart';
import 'dart:ui' as ui;

class ChartPainter extends CustomPainter {
  final String startLevel;
  final String targetLevel;
  final List<String> stageEndDates;
  final List<String> stageDescriptions;
  final int? highlightedIndex; // 新增参数，用于指定哪个点应该高亮显示

  // 新增可配置参数
  final Color curveColor; // 曲线颜色
  final double curveWidth; // 曲线宽度
  final double tipsBorderRadius; // 气泡提示的圆角大小

  ChartPainter({
    required this.startLevel,
    required this.targetLevel,
    required this.stageEndDates,
    required this.stageDescriptions,
    this.highlightedIndex, // 可选参数，默认为null表示不高亮任何点
    this.curveColor = const Color(0xFFB4A5FF), // 默认曲线颜色
    this.curveWidth = 2.0, // 默认曲线宽度
    this.tipsBorderRadius = 10.0, // 默认气泡圆角大小
  });

  double _getYForProgress(double progress, Size size, double padding) {
    final height = size.height;
    final startY = height - padding;
    final endY = padding;
    final heightDiff = startY - endY;

    // 调整起点位置，让 A0 从较高位置开始
    final initialHeight = heightDiff * 0.2; // A0 开始位置在总高度差的 40% 处
    final p0y = startY - initialHeight;

    // 调整控制点，使曲线更平滑
    final p1y = startY - heightDiff * 0.3; // 第一个控制点
    final p2y = startY - heightDiff * 0.4; // 第二个控制点
    final p3y = endY; // 终点

    final t = progress;
    return (1 - t) * (1 - t) * (1 - t) * p0y + 3 * (1 - t) * (1 - t) * t * p1y + 3 * (1 - t) * t * t * p2y + t * t * t * p3y;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final padding = height * 0.1; // 使用相对高度的padding，而不是固定值
    final xAxisY = height - padding;
    final segmentWidth = width / (stageDescriptions.length - 1);

    // 绘制坐标轴
    final axisPaint = Paint()
      ..color = curveColor.withAlpha(77) // 使用可配置的曲线颜色
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // 绘制底部x轴实线
    canvas.drawLine(
      Offset(0, xAxisY),
      Offset(width, xAxisY),
      axisPaint,
    );

    // 绘制顶部x轴实线
    canvas.drawLine(
      Offset(0, padding),
      Offset(width, padding),
      axisPaint,
    );

    // 绘制左侧y轴实线
    canvas.drawLine(
      Offset(0, padding),
      Offset(0, xAxisY),
      axisPaint,
    );

    // 绘制右侧y轴实线
    canvas.drawLine(
      Offset(width, padding),
      Offset(width, xAxisY),
      axisPaint,
    );

    // 绘制标签
    for (int i = 0; i < stageEndDates.length; i++) {
      final labelX = i * segmentWidth;

      // 绘制标签
      final textPainter = TextPainter(
        text: TextSpan(
          text: stageEndDates[i],
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
        ),
        textDirection: ui.TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(labelX - textPainter.width / 2, xAxisY + 8),
      );
    }

    // 绘制曲线（实线）
    final curvePaint = Paint()
      ..color = curveColor // 使用可配置的曲线颜色
      ..strokeWidth = curveWidth // 使用可配置的曲线宽度
      ..style = PaintingStyle.stroke;

    final path = Path();
    final startY = height - padding;
    final endY = padding;
    final heightDiff = startY - endY;
    final initialHeight = heightDiff * 0.2;

    // 绘制曲线起点
    double pathX = 0;
    double pathY = startY - initialHeight;
    path.moveTo(pathX, pathY);

    const totalPoints = 200;
    for (int i = 1; i <= totalPoints; i++) {
      final t = i / totalPoints;
      final curveX = width * t;
      final curveY = _getYForProgress(t, size, padding);

      // 使用lineTo绘制连续的实线
      path.lineTo(curveX, curveY);
    }

    canvas.drawPath(path, curvePaint);

    // 存储每个x轴标签对应的曲线上的点
    final List<Offset> curvePoints = [];

    // 为每个x轴标签位置计算曲线上的点
    for (int i = 0; i < stageDescriptions.length; i++) {
      final t = i / (stageDescriptions.length - 1);
      final curveX = i * segmentWidth;
      final curveY = _getYForProgress(t, size, padding);
      curvePoints.add(Offset(curveX, curveY));

      // 绘制从曲线上的点到x轴的虚线
      // 只为中间的点绘制虚线，不为两侧的点绘制
      if (i > 0 && i < stageDescriptions.length - 1) {
        const dashHeight = 5.0;
        const dashSpace = 5.0;
        double startY = curveY;

        // 只绘制从点到x轴的虚线
        while (startY < xAxisY) {
          final endY = startY + dashHeight < xAxisY ? startY + dashHeight : xAxisY;
          canvas.drawLine(
            Offset(curveX, startY),
            Offset(curveX, endY),
            Paint()
              ..color = curveColor // 使用可配置的曲线颜色
              ..strokeWidth = 1
              ..style = PaintingStyle.stroke,
          );
          startY = endY + dashSpace;
        }
      }

      // 判断当前点是否是高亮点（仅用于标签样式）
      final bool isHighlighted = highlightedIndex != null && i == highlightedIndex;

      // 所有点使用相同的样式
      // 外圈
      canvas.drawCircle(
        Offset(curveX, curveY),
        8,
        Paint()
          ..color = curveColor.withAlpha(50) // 使用可配置的曲线颜色
          ..style = PaintingStyle.fill,
      );

      // 中圈
      canvas.drawCircle(
        Offset(curveX, curveY),
        6,
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill,
      );

      // 内圈
      canvas.drawCircle(
        Offset(curveX, curveY),
        4,
        Paint()
          ..color = curveColor // 使用可配置的曲线颜色
          ..style = PaintingStyle.fill,
      );

      // 为每个点添加级别标签
      if (i < stageDescriptions.length) {
        final levelText = stageDescriptions[i];

        // 根据是否高亮选择背景颜色
        final labelBgPaint = Paint()
          ..color = isHighlighted ? const Color(0xFF7C69FF) : Colors.white
          ..style = PaintingStyle.fill;

        // 根据是否高亮选择文本颜色
        final textColor = isHighlighted ? Colors.white : const Color(0xFF7C69FF);

        final labelTextPainter = TextPainter(
          text: TextSpan(
            text: levelText,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          textDirection: ui.TextDirection.ltr,
        );
        labelTextPainter.layout();

        const padding = 6.0;
        final labelWidth = labelTextPainter.width + padding * 2;
        final labelHeight = labelTextPainter.height + padding;
        // 使用曲线上的点的x坐标
        final labelX = curveX - labelWidth / 2;
        final labelY = curveY - 40; // 标签位于点上方，增加距离以适应三角形

        // 绘制背景
        final labelRect = RRect.fromRectAndRadius(
          Rect.fromLTWH(labelX, labelY, labelWidth, labelHeight),
          Radius.circular(tipsBorderRadius), // 使用可配置的圆角大小
        );

        // 定义三角形
        const arrowHeight = 6.0;
        const arrowWidth = 12.0;

        final trianglePath = Path()
          ..moveTo(curveX, labelY + labelHeight + arrowHeight)
          ..lineTo(curveX - arrowWidth / 2, labelY + labelHeight)
          ..lineTo(curveX + arrowWidth / 2, labelY + labelHeight)
          ..close();

        // 绘制阴影（包括矩形和三角形）
        // 先绘制矩形阴影
        canvas.drawRRect(
          labelRect,
          Paint()
            ..color = Colors.black.withAlpha(20)
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3),
        );

        // 再绘制三角形阴影
        canvas.drawPath(
          trianglePath,
          Paint()
            ..color = Colors.black.withAlpha(20)
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3),
        );

        // 绘制矩形背景
        canvas.drawRRect(labelRect, labelBgPaint);

        // 绘制气泡三角形

        canvas.drawPath(
          trianglePath,
          labelBgPaint, // 使用与气泡相同的颜色
        );

        // 绘制文本
        labelTextPainter.paint(
          canvas,
          Offset(labelX + padding, labelY + padding / 2),
        );

        // 绘制连接线（所有点使用相同样式）
        final dashPaint = Paint()
          ..color = curveColor.withAlpha(100) // 使用可配置的曲线颜色
          ..strokeWidth = 1
          ..style = PaintingStyle.stroke;

        final dashPath = Path()
          ..moveTo(curveX, labelY + labelHeight + arrowHeight)
          ..lineTo(curveX, curveY - 8);

        canvas.drawPath(dashPath, dashPaint);
      }
    }

    // 我们已经为每个点添加了标签，不再需要额外的气泡
  }

  @override
  bool shouldRepaint(ChartPainter oldDelegate) {
    return oldDelegate.startLevel != startLevel ||
        oldDelegate.targetLevel != targetLevel ||
        oldDelegate.stageEndDates != stageEndDates ||
        oldDelegate.stageDescriptions != stageDescriptions ||
        oldDelegate.highlightedIndex != highlightedIndex ||
        oldDelegate.curveColor != curveColor ||
        oldDelegate.curveWidth != curveWidth ||
        oldDelegate.tipsBorderRadius != tipsBorderRadius;
  }
}
