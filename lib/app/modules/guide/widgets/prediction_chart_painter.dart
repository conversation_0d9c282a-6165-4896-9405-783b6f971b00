// 预测图表绘制器
import 'package:flutter/material.dart';

import 'dart:ui' as ui;
import 'dart:math';

class PredictionChartPainter extends CustomPainter {
  final String startLevel;
  final String targetLevel;
  final List<String> stageEndDates;
  final List<String> stageDescriptions;
  final int? highlightedIndex; // 新增参数，用于指定哪个点应该高亮显示

  PredictionChartPainter({
    required this.startLevel,
    required this.targetLevel,
    required this.stageEndDates,
    required this.stageDescriptions,
    this.highlightedIndex, // 可选参数，默认为null表示不高亮任何点
  });

  // 计算贝塞尔曲线上的点，与 LevelChartPainter 保持一致
  double _getYForProgress(double progress, Size size, double padding) {
    final height = size.height;
    final startY = height - padding;
    final endY = padding;
    final heightDiff = startY - endY;

    // 修改：让起点从原点出发，不再有初始间距
    final p0y = startY; // 起点直接从 x 轴开始

    // 调整控制点，使曲线更平滑
    final p1y = startY - heightDiff * 0.3; // 第一个控制点
    final p2y = startY - heightDiff * 0.7; // 第二个控制点
    final p3y = endY; // 终点

    final t = progress;
    return (1 - t) * (1 - t) * (1 - t) * p0y + 3 * (1 - t) * (1 - t) * t * p1y + 3 * (1 - t) * t * t * p2y + t * t * t * p3y;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final padding = height * 0.1;
    final xAxisY = height - padding;

    // 绘制坐标轴
    final axisPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // 绘制x轴
    canvas.drawLine(
      Offset(padding, xAxisY),
      Offset(width - padding, xAxisY),
      axisPaint,
    );

    // 绘制y轴
    canvas.drawLine(
      Offset(padding, xAxisY),
      Offset(padding, padding),
      axisPaint,
    );

    // 添加y轴右侧边框
    canvas.drawLine(
      Offset(width - padding, xAxisY),
      Offset(width - padding, padding),
      axisPaint,
    );

    // 添加顶部边框
    canvas.drawLine(
      Offset(padding, padding),
      Offset(width - padding, padding),
      axisPaint,
    );

    // 计算阶段点的位置
    final numStages = stageEndDates.length;

    // 准备绘制曲线

    var path = Path();
    final startX = padding;
    final startY = xAxisY;
    path.moveTo(startX, startY);

    // 计算每个阶段的点
    final points = <Offset>[];
    points.add(Offset(startX, startY)); // 起点

    // 计算阶段点的 x 坐标（平均分布）
    final List<double> stageXCoordinates = [];
    final segmentWidth = (width - padding * 2) / numStages;

    // 计算每个阶段的 x 坐标
    for (int i = 0; i <= numStages; i++) {
      stageXCoordinates.add(padding + segmentWidth * i);
    }

    // 计算阶段点的位置 - x轴和y轴都平均分布
    final List<Offset> stagePoints = [];

    // 计算每个阶段点的位置
    for (int i = 0; i <= numStages; i++) {
      final x = stageXCoordinates[i];

      // y轴也平均分布
      double y;
      if (i == 0) {
        // 起点
        y = xAxisY;
      } else {
        // 线性分布y坐标
        final progress = i / numStages;
        y = xAxisY - (xAxisY - padding) * progress;
      }

      stagePoints.add(Offset(x, y));
    }

    // 这部分代码已经不再需要，因为我们在绘制贝塞尔曲线时会重新计算点

    // 重新计算曲线和点
    // 清除之前的点
    points.clear();
    stagePoints.clear();

    // 绘制曲线，与 LevelChartPainter 保持一致
    final mainCurvePaint = Paint()
      ..color = const Color(0xFF7C69FF)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final mainPath = Path();
    final curveStartY = height - padding;

    // 绘制曲线起点 - 从原点开始
    double pathX = padding;
    double pathY = curveStartY; // 直接从 x 轴开始，不再有初始间距
    mainPath.moveTo(pathX, pathY);

    // 绘制曲线
    const totalPoints = 200;
    for (int i = 1; i <= totalPoints; i++) {
      final t = i / totalPoints;
      final curveX = padding + (width - padding * 2) * t;
      final curveY = _getYForProgress(t, size, padding);

      // 使用连续的线段绘制曲线
      mainPath.lineTo(curveX, curveY);
    }

    // 绘制主曲线
    canvas.drawPath(mainPath, mainCurvePaint);

    // 计算每个阶段点在曲线上的位置
    for (int i = 0; i <= numStages; i++) {
      final progress = i / numStages;
      final x = padding + (width - padding * 2) * progress;
      final y = _getYForProgress(progress, size, padding);

      stagePoints.add(Offset(x, y));
    }

    // 创建填充区域的路径
    final mainFillPath = Path();
    mainFillPath.moveTo(padding, xAxisY);

    // 添加曲线上的所有点
    for (int i = 0; i <= totalPoints; i++) {
      final t = i / totalPoints;
      final curveX = padding + (width - padding * 2) * t;
      final curveY = _getYForProgress(t, size, padding);

      mainFillPath.lineTo(curveX, curveY);
    }

    // 闭合填充区域
    mainFillPath.lineTo(width - padding, xAxisY);
    mainFillPath.close();

    // 绘制填充区域
    final fillPaint = Paint()
      ..shader = ui.Gradient.linear(
        Offset(width / 2, 0),
        Offset(width / 2, xAxisY),
        [
          const Color(0xFF7C69FF).withAlpha(50),
          const Color(0xFF7C69FF).withAlpha(10),
        ],
      )
      ..style = PaintingStyle.fill;

    canvas.drawPath(mainFillPath, fillPaint);

    // 绘制阶段点和标签
    for (int i = 0; i <= numStages; i++) {
      // 使用曲线上的点
      final point = stagePoints[i];

      // 绘制从点到x轴的虚线
      final dashPaint = Paint()
        ..color = Colors.grey[300]!
        ..strokeWidth = 1
        ..style = PaintingStyle.stroke;

      // 手动绘制虚线效果
      const double dashHeight = 3;
      const double dashSpace = 3;
      double startY = point.dy;

      while (startY < xAxisY) {
        final endY = startY + dashHeight < xAxisY ? startY + dashHeight : xAxisY;
        canvas.drawLine(
          Offset(point.dx, startY),
          Offset(point.dx, endY),
          dashPaint,
        );
        startY = endY + dashSpace;
      }

      // 绘制点
      // 根据是否是高亮点来决定绘制样式
      final bool isHighlighted = highlightedIndex != null && i == highlightedIndex;

      // 外圈
      canvas.drawCircle(
        point,
        8,
        Paint()
          ..color = isHighlighted
              ? const Color(0xFF7C69FF).withAlpha(50)
              : const Color(0xFF7C69FF).withAlpha(30)
          ..style = PaintingStyle.fill,
      );

      // 中圈
      canvas.drawCircle(
        point,
        6,
        Paint()
          ..color = isHighlighted ? const Color(0xFF7C69FF).withAlpha(20) : Colors.white
          ..style = PaintingStyle.fill,
      );

      // 内圈
      canvas.drawCircle(
        point,
        4,
        Paint()
          ..color = isHighlighted ? const Color(0xFF7C69FF) : Colors.white
          ..style = PaintingStyle.fill,
      );

      // 如果是高亮点，添加一个小点在中心
      if (isHighlighted) {
        canvas.drawCircle(
          point,
          2,
          Paint()
            ..color = Colors.white
            ..style = PaintingStyle.fill,
        );
      }

      // 绘制日期标签
      // 确保日期标签与阶段点对齐
      final double labelX = point.dx;
      final dateText = i == 0 ? '今天' : stageEndDates[i - 1];
      final dateTextPainter = TextPainter(
        text: TextSpan(
          text: dateText,
          style: const TextStyle(
            color: Colors.black54,
            fontSize: 12,
          ),
        ),
        textDirection: ui.TextDirection.ltr,
      );
      dateTextPainter.layout();
      dateTextPainter.paint(
        canvas,
        Offset(labelX - dateTextPainter.width / 2, xAxisY + 10),
      );

      // 绘制级别标签 - 使用白色背景的气泡
      final levelText = i == 0 ? startLevel : (i == numStages ? targetLevel : stageDescriptions[i - 1]);

      // 如果是最后一个点（目标级别），绘制特殊的气泡
      if (i == numStages) {
        // 绘制目标级别的特殊气泡
        const bubbleWidth = 90.0; // 增加宽度以确保文本能够完全显示
        const bubbleHeight = 40.0;
        const bubbleRadius = 20.0;

        // 使用曲线上的点的x坐标，确保不会超出右侧边界
        final bubbleX = min(width - padding - bubbleWidth / 2, max(padding + bubbleWidth / 2, point.dx));
        // 确保气泡不会超出顶部边界，至少保留5个像素的间距
        final bubbleTop = max(padding + 5, point.dy - 60);
        final bubbleRect = RRect.fromRectAndRadius(
          Rect.fromLTWH(
            bubbleX - bubbleWidth / 2,
            bubbleTop,
            bubbleWidth,
            bubbleHeight,
          ),
          const Radius.circular(bubbleRadius),
        );

        // 绘制气泡阴影
        canvas.drawRRect(
          bubbleRect,
          Paint()
            ..color = Colors.black.withAlpha(20)
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4),
        );

        // 绘制气泡
        canvas.drawRRect(
          bubbleRect,
          Paint()..color = const Color(0xFF7C69FF),
        );

        // 绘制气泡文本
        final bubbleTextPainter = TextPainter(
          text: TextSpan(
            text: '$levelText级别',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: ui.TextDirection.ltr,
        );
        bubbleTextPainter.layout(maxWidth: bubbleWidth - 10); // 限制文本宽度，留出边距
        bubbleTextPainter.paint(
          canvas,
          Offset(
            bubbleX - bubbleTextPainter.width / 2, // 使用气泡的X坐标，而不是点的X坐标
            bubbleTop + (bubbleHeight - bubbleTextPainter.height) / 2,
          ),
        );

        // 绘制连接线
        final linePath = Path()
          ..moveTo(bubbleX, bubbleTop + bubbleHeight)
          ..lineTo(point.dx, point.dy - 8); // 确保连接到点的位置

        canvas.drawPath(
          linePath,
          Paint()
            ..color = const Color(0xFF7C69FF).withAlpha(100)
            ..strokeWidth = 1
            ..style = PaintingStyle.stroke,
        );
      } else {
        // 为其他点绘制标签，根据是否高亮决定样式
        final bool isHighlighted = highlightedIndex != null && i == highlightedIndex;

        // 根据是否高亮选择背景颜色
        final labelBgPaint = Paint()
          ..color = isHighlighted ? const Color(0xFF7C69FF) : Colors.white
          ..style = PaintingStyle.fill;

        // 根据是否高亮选择文本颜色
        final textColor = isHighlighted ? Colors.white : const Color(0xFF7C69FF);

        final labelTextPainter = TextPainter(
          text: TextSpan(
            text: levelText,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: ui.TextDirection.ltr,
        );
        labelTextPainter.layout();

        const padding = 8.0;
        final labelWidth = labelTextPainter.width + padding * 2;
        final labelHeight = labelTextPainter.height + padding;
        // 使用曲线上的点的x坐标
        final labelX = point.dx - labelWidth / 2;
        final labelY = point.dy - 40;

        // 绘制背景
        final labelRect = RRect.fromRectAndRadius(
          Rect.fromLTWH(labelX, labelY, labelWidth, labelHeight),
          const Radius.circular(12),
        );

        // 绘制阴影
        canvas.drawRRect(
          labelRect,
          Paint()
            ..color = Colors.black.withAlpha(10)
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2),
        );

        canvas.drawRRect(labelRect, labelBgPaint);

        // 绘制文本
        labelTextPainter.paint(
          canvas,
          Offset(labelX + padding, labelY + padding / 2),
        );

        // 绘制连接线
        final dashPaint = Paint()
          ..color = isHighlighted
              ? const Color(0xFF7C69FF)
              : const Color(0xFF7C69FF).withAlpha(100)
          ..strokeWidth = isHighlighted ? 1.5 : 1
          ..style = PaintingStyle.stroke;

        final dashPath = Path()
          ..moveTo(point.dx, labelY + labelHeight)
          ..lineTo(point.dx, point.dy - 8);

        canvas.drawPath(dashPath, dashPaint);
      }

      // 我们已经在上面为每个点添加了更好的标签，不需要再添加气泡
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is PredictionChartPainter) {
      return oldDelegate.startLevel != startLevel ||
          oldDelegate.targetLevel != targetLevel ||
          oldDelegate.stageEndDates != stageEndDates ||
          oldDelegate.stageDescriptions != stageDescriptions ||
          oldDelegate.highlightedIndex != highlightedIndex;
    }
    return true;
  }
}
