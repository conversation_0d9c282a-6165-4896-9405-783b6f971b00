import 'dart:async';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/guide/controllers/guide_controller.dart';
import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';
import 'package:lsenglish/net/net.dart';

// 加载页面
class GeningPlanWidget extends StatefulWidget {
  /// 用于生成计划的数据
  final Map<String, dynamic> planData;

  /// 完成时的回调函数，返回生成的学习计划
  final Function(LearningPlanResp) onComplete;

  /// 动画持续时间（秒）
  final int animationDurationSeconds;

  const GeningPlanWidget({
    super.key,
    required this.planData,
    required this.onComplete,
    this.animationDurationSeconds = 100,
  });

  @override
  State<GeningPlanWidget> createState() => _GeningPlanWidgetState();
}

class _GeningPlanWidgetState extends State<GeningPlanWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  double _progress = 0.0;
  Timer? _planCheckTimer; // 用于定期检查计划生成状态
  DateTime? _checkStartTime; // 开始检查的时间
  bool _isGenerationFailed = false; // 是否生成失败

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: widget.animationDurationSeconds),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 0.99).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.addListener(() {
      setState(() {
        _progress = _progressAnimation.value;
      });
    });

    // 启动动画
    _animationController.forward();

    // 立即调用API
    _callGeneratePlanApi();
  }

  void _callGeneratePlanApi() async {
    // 准备请求数据
    final Map<String, dynamic> requestData = {
      'currentLevel': widget.planData['current_level_value'] ?? 'A0',
      'targetLevel': widget.planData['target_level_value'] ?? 'A1',
      'dailyStudyMinutes': 30,
      'motivationSource': (widget.planData['motivation_values'] as List<dynamic>?)?.join(',') ?? '自我成长',
      'desiredAbility': (widget.planData['ability_values'] as List<dynamic>?)?.join(',') ?? '听说读写',
      'forceRegenerate': true
    };

    debugPrint('调用生成计划API: $requestData');

    // 在后台调用API，同时显示进度条
    Net.getRestClient().generatePlan(requestData).then((response) {
      // 等待2秒后开始定期检查计划状态
      if (mounted) {
        Future.delayed(const Duration(seconds: 10), () {
          _startCheckingCurrentPlan();
        });
      }

      return null;
    }).catchError((e) {
      if (e is DioException) {
        debugPrint('错误详情: ${e.response?.data}');
      }

      // 即使API失败，也要完成动画
      if (mounted) {
        _completeAnimation();
      }
    });
  }

  // 开始定期检查当前计划状态
  void _startCheckingCurrentPlan() {
    // 取消已有的定时器（如果存在）
    _planCheckTimer?.cancel();

    // 重置状态
    setState(() {
      _isGenerationFailed = false;
      _checkStartTime = DateTime.now(); // 记录开始检查的时间
    });

    // 立即检查一次
    _checkCurrentPlan();

    // 设置定时器，每2秒检查一次
    _planCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkCurrentPlan();
    });
  }

  // 检查当前计划状态
  void _checkCurrentPlan() {
    if (!mounted) return;

    // 检查是否超时（5分钟）
    if (_checkStartTime != null) {
      final now = DateTime.now();
      final difference = now.difference(_checkStartTime!);

      // 如果超过5分钟（300秒）仍未获取到结果，则认为生成失败
      if (difference.inSeconds > 300 && !_isGenerationFailed) {
        debugPrint('计划生成超时: ${difference.inSeconds}秒');

        // 取消定时器
        _planCheckTimer?.cancel();
        _planCheckTimer = null;

        // 设置为生成失败状态
        setState(() {
          _isGenerationFailed = true;
          _progress = 0.0;
        });

        return;
      }
    }

    Net.getRestClient().currentPlan().then((response) {
      if (!mounted) return;

      // 如果有数据，说明计划已生成完成
      if (response.data?.stages != null) {
        // 取消定时器
        _planCheckTimer?.cancel();
        _planCheckTimer = null;

        // 将数据保存到控制器中
        final controller = Get.find<GuideController>();
        controller.learningPlanResp.value = response.data;

        // 完成动画
        _completeAnimation();
      }
    }).catchError((e) {
      debugPrint('检查当前计划状态失败: $e');
    });
  }

  void _completeAnimation() {
    // 设置进度为100%
    setState(() {
      _progress = 1.0;
    });

    // 延迟后调用完成回调，传递学习计划数据
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final controller = Get.find<GuideController>();
        if (controller.learningPlanResp.value != null) {
          widget.onComplete(controller.learningPlanResp.value!);
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    // 取消定时器
    _planCheckTimer?.cancel();
    _planCheckTimer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Spacer(),
        Text(
          'SeedTu就是为像你这样\n的人而设计的！',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 20),
        // 根据生成状态显示不同的文本
        Text(
          _isGenerationFailed ? '学习计划生成失败，请重试' : '正在为你生成个性化学习计划...',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const Spacer(),
        // 进度条或重试按钮
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: _isGenerationFailed
              // 显示重新生成按钮
              ? ElevatedButton(
                  onPressed: () {
                    // 重新开始生成计划
                    setState(() {
                      _progress = 0.0;
                      _isGenerationFailed = false;
                    });
                    _callGeneratePlanApi();
                  },
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('重新生成'),
                )
              // 显示进度条
              : Column(
                  children: [
                    LinearProgressIndicator(
                      value: _progress,
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
                      minHeight: 8,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${(_progress * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
        ),
        const Spacer(),
      ],
    );
  }
}
