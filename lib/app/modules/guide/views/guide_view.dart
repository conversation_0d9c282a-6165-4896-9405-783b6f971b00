import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/guide/widgets/gening_plan.dart';
import 'package:lsenglish/utils/size_extension.dart';
import '../controllers/guide_controller.dart';
import '../widgets/progress_header.dart';
import '../widgets/guide_stages.dart';

class GuideView extends GetView<GuideController> {
  const GuideView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Obx(() => ProgressHeader(
                  current: controller.currentStage.value + 1,
                  max: controller.maxStages,
                  onBack: controller.onBack,
                )),
            Gap(16.whs),
            Expanded(
              child: PageView(
                physics: const NeverScrollableScrollPhysics(), // 禁用滑动
                controller: controller.pageController,
                onPageChanged: (index) {
                  // 当页面变化时，更新当前阶段
                  if (index != controller.currentStage.value) {
                    controller.currentStage.value = index;
                  }
                },
                children: [
                  const WelcomeStage(),
                  // 多选：最大动力来源
                  GetBuilder<GuideController>(
                    builder: (_) => MultiChoiceStage(
                      title: '你最大的动力来源是什么',
                      choices: controller.motivationChoices,
                      selectedIndices: controller.getSelectedIndicesForStage('motivation'),
                      onToggle: (index) => controller.toggleMultiChoice(0, index),
                      onConfirm: () => controller.confirmMultiChoices(0),
                      stageKey: 'motivation',
                    ),
                  ),
                  // 多选：提升能力
                  GetBuilder<GuideController>(
                    builder: (_) => MultiChoiceStage(
                      title: '你最想提升什么能力',
                      choices: controller.abilityChoices,
                      selectedIndices: controller.getSelectedIndicesForStage('ability'),
                      onToggle: (index) => controller.toggleMultiChoice(1, index),
                      onConfirm: () => controller.confirmMultiChoices(1),
                      stageKey: 'ability',
                    ),
                  ),
                  // 当前级别选择
                  LevelChartStage(
                    title: '选择你当前的英语水平',
                    onConfirm: controller.selectCurrentLevel,
                    buttonText: '确认',
                  ),
                  // 目标级别选择
                  LevelChartStage(
                    title: '选择你的目标水平',
                    onConfirm: controller.selectTargetLevel,
                    buttonText: '确认',
                  ),
                  const TimeSelectionStage(),
                  // 加载页面
                  GeningPlanWidget(
                    planData: controller.selectedChoices,
                    onComplete: (plan) {
                      // 完成后进入预测结果页面
                      controller.currentStage.value = 7;
                      controller.learningPlanResp.value = plan;
                      controller.pageController.animateToPage(
                        controller.currentStage.value,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeInOut,
                      );
                    },
                  ),
                  // 预测结果页面
                  const PredictionResultStage(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
