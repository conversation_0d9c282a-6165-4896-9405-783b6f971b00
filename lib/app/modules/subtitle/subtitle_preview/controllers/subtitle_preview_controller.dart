import 'package:get/get.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/video.dart';

class SubtitlePreviewController extends GetxController {
  var subtitlePath = "";
  var subtitles = <Subtitle>[].obs;
  @override
  void onInit() async {
    super.onInit();
    subtitlePath = Get.arguments['subtitlePath'];
    var data = await loadSubtitlesInternal(subtitlePath);
    subtitles.value = data.item2;
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void complete() {
    Get.back(result: {'offAllFilemanager': true, 'subtitlePath': subtitlePath});
  }
}
