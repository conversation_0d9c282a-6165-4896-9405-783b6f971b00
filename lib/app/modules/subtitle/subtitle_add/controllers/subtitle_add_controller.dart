import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:lsenglish/utils/zip.dart';
import 'package:lsenglish/widgets/file_manager.dart';
import 'package:path/path.dart';

class SubtitleAddController extends GetxController {
  TextEditingController textEditingController = TextEditingController();
  var videoPath = "";
  @override
  void onInit() {
    super.onInit();
    videoPath = Get.arguments['videoPath'];
    textEditingController.text = basenameWithoutExtension(videoPath);
  }

  void downloadFromWebsite() async {
    var subtitleSearchResult = await Get.toNamed(Routes.SUBTITLE_SEARCH, arguments: {'searchName': textEditingController.text});
    if (subtitleSearchResult == null) {
      return;
    }
    SmartDialog.showLoading(msg: '正在下载和解压字幕文件...');
    var requestUrl = subtitleSearchResult['requestUrl'];
    var zipDirName = await handleZipDownloadAndUnpack(requestUrl, basename(videoPath));
    logger("downloadFromWebsite zipDirName =$zipDirName");
    SmartDialog.dismiss();
    if (zipDirName.isNotEmpty) {
      var result = await Get.to(() => FileManager(targetDirPath: zipDirName), preventDuplicates: false);
      if (result != null && result['subtitlePath'] != null) {
        SPUtil().saveHistory(
          videoPath,
          subtitleLocalPath: result['subtitlePath'],
        );
        Get.back(result: {'subtitlePath': result['subtitlePath']});
      }
    }
  }

  void pickSubtitleFromFile() async {
    FilePickerResult? filePickerResult = await FilePicker.platform.pickFiles(
      initialDirectory: (await FileUtils().getSaveDir()).path,
    );
    var filePath = await FileUtils().moveFileToDocuments(filePickerResult?.files.single.path ?? "");
    Get.back(result: {'subtitlePath': filePath});
  }

  void pickLocalSubtitle() async {
    var result = await Get.to(() => const FileManager(), preventDuplicates: false);
    if (result != null && result['subtitlePath'] != null) {
      Get.back(result: {'subtitlePath': result['subtitlePath']});
    }
  }
}
