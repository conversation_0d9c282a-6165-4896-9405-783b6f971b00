import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../controllers/subtitle_add_controller.dart';

class SubtitleAddView extends GetView<SubtitleAddController> {
  const SubtitleAddView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SubtitleAddView'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text("你可以重新输入需要搜索的字幕名称\n若发现搜索无结果，请尝试删除一些多余的文字再搜索"),
            Gap(16.whs),
            TextField(
              controller: controller.textEditingController,
            ),
            FilledButton(
              onPressed: () {
                controller.downloadFromWebsite();
              },
              child: const Text("从字幕网站下载"),
            ),
            FilledButton(
              onPressed: () {
                controller.pickLocalSubtitle();
              },
              child: const Text("本地导入"),
            ),
            FilledButton(
              onPressed: () {
                controller.pickSubtitleFromFile();
              },
              child: const Text("本地文件夹导入"),
            ),
          ],
        ),
      ),
    );
  }
}
