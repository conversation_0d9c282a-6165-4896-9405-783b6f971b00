import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/subtitle_search_controller.dart';

class SubtitleSearchView extends GetView<SubtitleSearchController> {
  const SubtitleSearchView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('字幕搜索'),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Obx(
            () => Visibility(
              visible: controller.webViewLoadingProgress.value != 1,
              child: LinearProgressIndicator(
                  value: controller.webViewLoadingProgress.value),
            ),
          ),
          Expanded(
            child: WebViewWidget(controller: controller.webviewController),
          ),
        ],
      ),
    );
  }
}
