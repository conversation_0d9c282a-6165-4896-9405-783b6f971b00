import 'package:get/get.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:webview_flutter/webview_flutter.dart';
// #docregion platform_imports
// Import for Android features.
import 'package:webview_flutter_android/webview_flutter_android.dart';
// Import for iOS features.
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class SubtitleSearchController extends GetxController {
  late final WebViewController webviewController;
  var webViewLoadingProgress = 0.0.obs;
  @override
  void onInit() {
    super.onInit();
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller = WebViewController.fromPlatformCreationParams(params);

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            logger('WebView is loading (progress : $progress%)');
            webViewLoadingProgress.value = progress / 100;
          },
          onPageStarted: (String url) {
            logger('Page started loading: $url');
          },
          onPageFinished: (String url) {
            logger('Page finished loading: $url');
            if (url.contains("zimuku.org/search")) {
              _focusInput();
            }
          },
          onWebResourceError: (WebResourceError error) {
            logger('''
                Page resource error:
                code: ${error.errorCode}
                description: ${error.description}
                errorType: ${error.errorType}
                isForMainFrame: ${error.isForMainFrame}
          ''');
          },
          onNavigationRequest: (NavigationRequest request) async {
            // https://srtku.com/download/MTY1ODQ3fDhmYTEyZWZjNTk5NTYzMTNiZWFlNTlkYXwxNzEyNDU3ODM5fDE1YjQzZTdhfHJlbW90ZQ%3D%3D/svr/d0
            // if (request.url.contains('file0.assrt.net/download')) {
            if (request.url.contains('download')) {
              Get.back(result: {'requestUrl': request.url});
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onUrlChange: (UrlChange change) {
            logger('url change to ${change.url}');
          },
          onHttpAuthRequest: (HttpAuthRequest request) {},
        ),
      )
      // ignore: prefer_interpolation_to_compose_strings
      // ..loadRequest(Uri.parse("https://secure.assrt.net/sub/?searchword=" + Get.arguments['searchName']));
      ..loadRequest(Uri.parse("https://zimuku.org/search?q=${Get.arguments['searchName']}&chost=zimuku.org"));
    // ..loadRequest(Uri.parse("https://zimuku.org/search?q=test&chost=zimuku.org"));

    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController).setMediaPlaybackRequiresUserGesture(false);
    }

    webviewController = controller;
  }

  void _focusInput() {
    webviewController.runJavaScript("document.querySelector('input').focus();");
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
