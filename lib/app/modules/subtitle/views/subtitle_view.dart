import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/subtitle_controller.dart';

class SubtitleView extends GetView<SubtitlesController> {
  const SubtitleView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SubtitleView'),
        centerTitle: true,
      ),
      body: const Center(
        child: Text(
          'SubtitleView is working',
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}
