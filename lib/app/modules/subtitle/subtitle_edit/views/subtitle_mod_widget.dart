import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/toast.dart';

typedef SubtitleSaveCallback = void Function(String);

class SubtitleModWidget extends StatefulWidget {
  final Subtitle subtitle;
  final SubtitleSaveCallback saveCallback;

  const SubtitleModWidget({
    super.key,
    required this.subtitle,
    required this.saveCallback,
  });

  @override
  State<SubtitleModWidget> createState() => _SubtitleModWidgetState();
}

class _SubtitleModWidgetState extends State<SubtitleModWidget> {
  final FocusNode _focusNode = FocusNode();
  TextEditingController textEditingController = TextEditingController();
  void save() {
    if (textEditingController.text.isEmpty) {
      toastInfoError("内容不能为空");
      return;
    }
    widget.saveCallback(textEditingController.text);
    Get.back();
  }

  @override
  void initState() {
    super.initState();
    textEditingController.text = widget.subtitle.targetData;
  }

  @override
  void dispose() {
    super.dispose();
    textEditingController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 70.whs),
      child: DraggableScrollableSheet(
          initialChildSize: 1,
          expand: true,
          snap: true,
          builder: (BuildContext context, ScrollController scrollController) {
            return NotificationListener<DraggableScrollableNotification>(
              onNotification: (notification) {
                if (notification.extent <= 0.3) {
                  if (Get.isBottomSheetOpen ?? false) {
                    Get.back();
                  }
                }
                return true;
              },
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.9,
                  color: Get.theme.scaffoldBackgroundColor,
                  padding: EdgeInsets.only(bottom: 34.whs),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 12.whs, horizontal: 14.whs),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "编辑",
                              style: TextStyle(fontSize: 18.whs, fontWeight: FontWeight.w600),
                            ),
                            const Spacer(),
                            GestureDetector(
                              onTap: () => Get.back(),
                              child: Align(
                                alignment: Alignment.centerRight,
                                child: Container(
                                  decoration: ShapeDecoration(
                                    color: const Color(0x14747480),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(32),
                                    ),
                                  ),
                                  child: Padding(
                                      padding: EdgeInsets.all(4.whs),
                                      child: Icon(
                                        Icons.close,
                                        size: 24.whs,
                                      )),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.whs),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: TextField(
                                focusNode: _focusNode,
                                decoration: const InputDecoration(
                                  hintText: "输入字幕内容...",
                                  contentPadding: EdgeInsets.zero,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                ),
                                controller: textEditingController,
                                cursorColor: Get.theme.primaryColor,
                                keyboardType: TextInputType.multiline,
                                maxLines: null, // Allows the TextField to grow as per the content
                                textInputAction: TextInputAction.newline,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16.whs,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => save(),
                        child: Row(
                          children: [
                            const Spacer(),
                            Padding(
                              padding: EdgeInsets.all(16.whs),
                              child: Container(
                                width: 72.whs,
                                height: 45.whs,
                                decoration: BoxDecoration(
                                  color: Get.theme.colorScheme.onSurface,
                                  borderRadius: const BorderRadius.all(Radius.circular(12)),
                                ),
                                padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                                child: Center(
                                  child: Text(
                                    "保存",
                                    style: TextStyle(color: Colors.white, fontSize: 16.whs),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
    );
  }
}
