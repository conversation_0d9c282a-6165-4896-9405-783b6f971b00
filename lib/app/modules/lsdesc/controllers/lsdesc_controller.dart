import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/net/net.dart';

class LsdescController extends GetxController {
  // 处理"我已知晓，下一步"按钮点击
  void onNextButtonPressed() {
    Net.getRestClient().currentPlan().then((value) {
      if (value.data != null) {
        Get.toNamed(Routes.PLAN_GEN_DETAIL);
      }else{
        Get.toNamed(Routes.GUIDE);
      }
    }).catchError((e) {
      Get.toNamed(Routes.GUIDE);
    });
  }

  // 处理"已有账户？登录"链接点击
  void onLoginLinkPressed() {
    // Get.toNamed(Routes.LOGIN);
    Get.toNamed(Routes.GUIDE);
  }
}
