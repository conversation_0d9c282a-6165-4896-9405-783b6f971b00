import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../controllers/lsdesc_controller.dart';

class LsdescView extends GetView<LsdescController> {
  const LsdescView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.whs),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 顶部空白区域
              Gap(60.whs),

              // 标题
              Text(
                'LS学习法介绍',
                style: TextStyle(
                  fontSize: 24.whs,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),

              // 标题和描述之间的间距
              Gap(40.whs),

              // 描述文本
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.whs),
                child: Text(
                  'LS学习法是一种高效的语言学习方法，通过听说结合的方式帮助学习者快速提升语言能力。\n\n'
                  '通过反复听、模仿和跟读，LS学习法能够帮助你建立语感，提高口语流利度，加速语言习得过程。',
                  style: TextStyle(
                    fontSize: 16.whs,
                    color: Colors.black54,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // 弹性空间，将按钮推到底部
              const Spacer(),

              // 底部按钮
              SizedBox(
                width: double.infinity,
                height: 50.whs,
                child: ElevatedButton(
                  onPressed: () => controller.onNextButtonPressed(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xff101828),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.whs),
                    ),
                  ),
                  child: const Text('我已知晓，下一步'),
                ),
              ),

              // 底部按钮和登录链接之间的间距
              Gap(16.whs),

              // 登录链接
              GestureDetector(
                onTap: () => controller.onLoginLinkPressed(),
                child: Text(
                  '已有账户？登录',
                  style: TextStyle(
                    fontSize: 14.whs,
                    color: Get.theme.primaryColor,
                  ),
                ),
              ),

              // 底部边距
              Gap(20.whs),
            ],
          ),
        ),
      ),
    );
  }
}
