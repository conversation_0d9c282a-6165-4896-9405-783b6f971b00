import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lsenglish/utils/chart.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../../views/episode_chart.dart';
import '../controllers/episodedata_controller.dart';

class EpisodedataView extends GetView<EpisodedataController> {
  const EpisodedataView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.episodeName.value)),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Gap(16.whs),
            Padding(
              padding: EdgeInsets.all(16.whs),
              child: Container(
                padding: EdgeInsets.all(16.whs),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.whs),
                ),
                child: Column(
                  children: [
                    _buildGrid(),
                    Obx(() => EpisodeChartWidget(
                          // ignore: invalid_use_of_protected_member
                          episodeLsDataList: controller.episodeLsDataList.value,
                          selectIndexCallback: (i) {},
                        )),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(16.whs),
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.whs),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Gap(16.whs),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(child: Text("第几遍", textAlign: TextAlign.center, style: TextStyle(fontSize: 16.whs))),
                        Expanded(child: Text("所用时长", textAlign: TextAlign.center, style: TextStyle(fontSize: 16.whs))),
                        Expanded(child: Text("完成时间", textAlign: TextAlign.center, style: TextStyle(fontSize: 16.whs))),
                      ],
                    ),
                    Gap(16.whs),
                    const Divider(),
                    Obx(() => ListView.builder(
                          itemCount: controller.episodeLsDataList.length,
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemBuilder: (BuildContext context, int index) {
                            return Column(
                              children: [
                                Gap(index == 0 ? 16.whs : 8.whs),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "${controller.episodeLsDataList[index].lsTimes ?? 0} time",
                                        style: TextStyle(fontSize: 16.whs),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      child: RichText(
                                        text: formatMinutes(controller.episodeLsDataList[index].totalLearnDuration, numSize: 20),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      child: index == 0
                                          ? GestureDetector(
                                              onTap: () => controller.showLsTimeDialog(),
                                              child: Text(
                                                "点击完成",
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  color: Get.theme.primaryColor,
                                                  decoration: TextDecoration.underline,
                                                  decorationColor: Get.theme.primaryColor,
                                                  decorationThickness: 2,
                                                  fontSize: 16.whs,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            )
                                          : Text(
                                              controller.episodeLsDataList[index].finishTime == 0
                                                  ? "--"
                                                  : DateFormat('yyyy/MM/dd').format(
                                                      DateTime.fromMillisecondsSinceEpoch(controller.episodeLsDataList[index].finishTime! * 1000)),
                                              style: TextStyle(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 16.whs,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                    ),
                                  ],
                                ),
                                Gap(8.whs),
                                const Divider(),
                              ],
                            );
                          },
                        ))
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.2,
      ),
      itemCount: 2,
      itemBuilder: (context, gridIndex) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              gridIndex == 0 ? "总时长" : "LS遍数",
              style: TextStyle(fontSize: 14.whs, color: const Color(0xFF625B71)),
            ),
            Gap(4.whs),
            GestureDetector(
              onTap: () {
                if (gridIndex == 1) {
                  controller.showLsTimeDialog();
                }
              },
              child: Row(
                children: [
                  gridIndex == 0
                      ? Obx(
                          () => RichText(
                            text: formatMinutes(controller.episodeResp.value.totalLearnDuration, numSize: 32),
                          ),
                        )
                      : Obx(
                          () => RichText(
                            text: formatMinutes(controller.episodeResp.value.currentLsTimes, numSize: 32),
                          ),
                        ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
