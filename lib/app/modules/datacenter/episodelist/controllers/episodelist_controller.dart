import 'package:get/get.dart';
import 'package:lsenglish/base/loading_getxcontroller.dart';
import 'package:lsenglish/model/data_center_home_resp/episode.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/obs.dart';

class EpisodelistController extends LoadingGetxcontroller {
  var episodeList = <Episode>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadRemote();
    subscriptions.addAll([
      ObsUtil().renameLocalFileName.listen((v) {
        loadRemote();
      }),
    ]);
  }

  void loadRemote() {
    Net.getRestClient().dataEpisodeList().then((data) {
      episodeList.assignAll(data.data);
      setSuccess();
    }).catchError((onError) {
      setError();
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
