import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/data_center_chart_resp/data_center_chart_data.dart';
import 'package:lsenglish/model/data_center_chart_resp/data_center_chart_resp.dart';
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/chart.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../model/episode_ls_data_resp/episode_ls_data.dart';

typedef SelectIndexCallback = void Function(int?);

class EpisodeChartWidget extends StatefulWidget {
  final List<EpisodeLsData> episodeLsDataList;
  final SelectIndexCallback selectIndexCallback;
  const EpisodeChartWidget({
    super.key,
    required this.episodeLsDataList,
    required this.selectIndexCallback,
  });

  @override
  State<EpisodeChartWidget> createState() => _EpisodeChartWidgetState();
}

class _EpisodeChartWidgetState extends State<EpisodeChartWidget> {
  int? touchedGroupIndex;
  var chartData = <ChartCategoryData>[];
  var adjustMinY = 0.0;
  var adjustMaxY = 100.0;
  int? selectedIndex;
  CustomBarSeriesRenderer? _customBarSeriesRenderer;
  DataCenterChartResp? dataCenterChartResp;

  @override
  void initState() {
    super.initState();
    fetchChartData();
  }

  @override
  void dispose() {
    selectedIndex = null;
    widget.selectIndexCallback(selectedIndex);
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant EpisodeChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.episodeLsDataList != widget.episodeLsDataList) {
      selectedIndex = null;
      widget.selectIndexCallback(selectedIndex);
      _customBarSeriesRenderer?.updateNull();
      fetchChartData();
    }
  }

  void fetchChartData() {
    var tempChartData = <ChartCategoryData>[];
    for (var i = 0; i < widget.episodeLsDataList.length; i++) {
      tempChartData.add(ChartCategoryData(category: i.toString(), y: widget.episodeLsDataList[i].totalLearnDuration?.toDouble()));
    }
    if (tempChartData.length < 7) {
      for (var i = tempChartData.length; i <= 7; i++) {
        tempChartData.add(ChartCategoryData(category: i.toString(), y: -0.1));
      }
    }
    chartData = tempChartData;
    setState(() {
      _handleAdjustY();
    });
  }

  void _handleAdjustY() {
    if (chartData.isEmpty) {
      return;
    }
    var maxY = chartData.where((element) => element.y != null).map((element) => element.y!).reduce((current, next) => current > next ? current : next)
        as double;
    var minY = chartData.where((element) => element.y != null).map((element) => element.y!).reduce((current, next) => current < next ? current : next)
        as double;
    var adjustYValues = calculateYAxisLabels(minY, maxY);
    setState(() {
      adjustMinY = adjustYValues.first;
      adjustMaxY = adjustYValues.second;
    });
    logger("adjustMinY = $adjustMinY   adjustMaxY = $adjustMaxY chartData size = ${chartData.length}");
  }

  DataCenterChartData? getMaxDurationData(List<DataCenterChartData> datas) {
    if (datas.isEmpty) return null;

    DataCenterChartData? maxData = datas[0];
    for (var data in datas) {
      if (data.duration != null && data.duration! > (maxData?.duration ?? 0)) {
        maxData = data;
      }
    }
    return maxData;
  }

  @override
  Widget build(BuildContext context) {
    return widget.episodeLsDataList.isEmpty ? _buildEmptyChart() : _buildChart();
  }

  SfCartesianChart _buildChart() {
    return SfCartesianChart(
      plotAreaBorderWidth: 1,
      plotAreaBorderColor: gray200,
      primaryXAxis: CategoryAxis(
        isInversed: true,
        interval: 1,
        initialVisibleMinimum: -1,
        initialVisibleMaximum: 6,
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
        majorGridLines: MajorGridLines(width: 1, color: gray200, dashArray: const <double>[2, 2]),
        edgeLabelPlacement: EdgeLabelPlacement.shift,
        labelAlignment: LabelAlignment.center,
        axisLabelFormatter: (AxisLabelRenderDetails details) {
          return (chartData[details.value.toInt()].y ?? -1) < 0 ? ChartAxisLabel("", null) : ChartAxisLabel("${details.value.toInt() + 1}", null);
        },
      ),
      zoomPanBehavior: ZoomPanBehavior(
        enablePanning: true,
        zoomMode: ZoomMode.x,
      ),
      primaryYAxis: NumericAxis(
        opposedPosition: true,
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
        majorGridLines: MajorGridLines(width: 1, color: gray200),
        interval: (adjustMaxY - adjustMinY) / 2,
        minimum: adjustMinY,
        maximum: adjustMaxY,
        labelAlignment: LabelAlignment.center,
        axisLabelFormatter: (details) {
          return ChartAxisLabel(
            details.text,
            const TextStyle(
              height: 2,
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: Color(0xff8D8C8C),
            ),
          );
        },
      ),
      series: getDefaultBarPanningSeries(),
      onChartTouchInteractionDown: (tapArgs) {},
      onChartTouchInteractionMove: (tapArgs) {},
      onChartTouchInteractionUp: (tapArgs) {
        var chartWidth = _customBarSeriesRenderer?.size.width ?? 0;
        var barCount = chartData.length;
        double barWidth = chartWidth / barCount;
        int clickBarIndex = (tapArgs.position.dx / barWidth).floor();
        int barIndex = clickBarIndex;
        var hasClickDataIndex = true;
        if (barIndex >= 0 && barIndex < barCount) {
          if (chartData[barIndex].y == null || chartData[barIndex].y == 0) {
            // 检查周围的四个条形
            var indicesToCheck = [barIndex - 1, barIndex + 1, barIndex - 2, barIndex + 2];
            var foundNonZeroIndex = indicesToCheck.firstWhere((index) {
              return index >= 0 && index < barCount && chartData[index].y != null && chartData[index].y != 0;
            }, orElse: () => -1);

            //判断selectedIndex为null 就是不显示的时候 点击其他区域时自动定位一下 以免点不到
            if (foundNonZeroIndex != -1 && selectedIndex == null) {
              barIndex = foundNonZeroIndex;
            } else {
              hasClickDataIndex = false;
            }
          }
        } else {
          hasClickDataIndex = false;
        }
        if (!hasClickDataIndex) {
          setState(() {
            selectedIndex = null;
            _customBarSeriesRenderer?.updateNull();
          });
        } else {
          setState(() {
            selectedIndex = barIndex;
            if (chartData[barIndex].y != 0) {
              _customBarSeriesRenderer?.updateData(
                selectedIndex,
                chartData.length,
                1,
                dataCenterChartResp?.datas?[barIndex],
              );
            }
          });
        }
        widget.selectIndexCallback(selectedIndex);
      },
    );
  }

  void getNearestBarIndex(Offset tapPosition, double chartWidth) {
    var barCount = chartData.length;
    double barWidth = chartWidth / barCount;
    int nearestIndex = -1;
    double minDistance = double.infinity;

    for (int i = 0; i < barCount; i++) {
      double barCenterX = (i + 0.5) * barWidth;
      double distance = (tapPosition.dx - barCenterX).abs();

      if (distance < minDistance) {
        minDistance = distance;
        nearestIndex = i;
      }
    }

    if (nearestIndex != -1) {
      logger("Nearest bar index: $nearestIndex");
    } else {
      logger("No bar found");
    }
  }

  List<ColumnSeries<ChartCategoryData, String>> getDefaultBarPanningSeries() {
    return <ColumnSeries<ChartCategoryData, String>>[
      ColumnSeries<ChartCategoryData, String>(
        animationDuration: 500,
        dataSource: chartData,
        color: Get.theme.primaryColor,
        width: 0.7,
        borderRadius: const BorderRadius.only(topLeft: Radius.circular(4), topRight: Radius.circular(4)),
        xValueMapper: (ChartCategoryData sales, _) => sales.category ?? "",
        yValueMapper: (ChartCategoryData sales, _) => sales.y,
        onCreateRenderer: (series) {
          _customBarSeriesRenderer = CustomBarSeriesRenderer();
          return _customBarSeriesRenderer!;
        },
      ),
    ];
  }

  Widget _buildEmptyChart() {
    return const AspectRatio(
      aspectRatio: 1,
      child: Center(child: SizedBox(width: 50, height: 50, child: CircularProgressIndicator())),
    );
  }
}

class CustomBarSeriesRenderer extends ColumnSeriesRenderer<ChartCategoryData, String> {
  int? clickIndex = -1;
  int totalBars = 0;
  int tabType = 1;
  DataCenterChartData? dataCenterChartData;
  void updateNull() {
    clickIndex = null;
  }

  void updateData(int? x, int totalBars, int tabType, DataCenterChartData? dataCenterChartData) {
    clickIndex = x;
    this.totalBars = totalBars;
    this.tabType = tabType;
    this.dataCenterChartData = dataCenterChartData;
    markNeedsPaint();
  }

  @override
  void onPaint(PaintingContext context, Offset offset) {
    if (clickIndex != null && clickIndex != -1 && dataCenterChartData != null) {
      double barWidth = size.width / (totalBars * 2);
      double centerX = (clickIndex! * 2 + 1) * barWidth;
      commonTooltipsPaint(context, centerX, offset, size, getTootipsTextSpan(tabType, dataCenterChartData!));
    }

    super.onPaint(context, offset);
  }
}
