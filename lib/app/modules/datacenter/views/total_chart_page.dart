import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/data_center_chart_resp/data_center_chart_resp.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../../../../utils/chart.dart';
import 'total_chart.dart';

typedef PageIndexCallback = void Function(int);

class TotalChartPageWidget extends StatefulWidget {
  final int tabType;
  final PageIndexCallback pageIndexCallback;
  const TotalChartPageWidget({super.key, required this.tabType, required this.pageIndexCallback});

  @override
  State<TotalChartPageWidget> createState() => _TotalChartPageWidgetState();
}

class _TotalChartPageWidgetState extends State<TotalChartPageWidget> {
  int? selectedIndex;
  int? pageIndex;
  int currentPageIndex = 0;
  DataCenterChartResp? dataCenterChartResp;
  var pageKey = GlobalKey();
  String chartTimeRangeString = "";
  @override
  void initState() {
    super.initState();
    chartTimeRangeString = getChartTimeRangeString(widget.tabType, currentPageIndex);
  }

  @override
  void didUpdateWidget(covariant TotalChartPageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tabType != widget.tabType) {
      debugPrint("widget.tabType = ${widget.tabType}");
      setState(() {
        pageKey = GlobalKey();
        chartTimeRangeString = getChartTimeRangeString(widget.tabType, currentPageIndex);
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.whs),
      ),
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 20.whs),
            child: AnimatedOpacity(
              opacity: pageIndex == currentPageIndex ? (selectedIndex == null ? 1 : 0) : 1,
              duration: const Duration(milliseconds: 100),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "总时长",
                    style: TextStyle(fontSize: 22.whs, color: const Color(0xFF625B71)),
                  ),
                  RichText(
                    text: formatMinutes(dataCenterChartResp?.totalLearnDuration),
                  ),
                  Text(chartTimeRangeString),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.whs),
            child: SizedBox(
              height: Get.width + 50.whs,
              child: PageView.builder(
                key: pageKey,
                reverse: true,
                onPageChanged: (value) {
                  currentPageIndex = value;
                  widget.pageIndexCallback(value);
                  setState(() {
                    chartTimeRangeString = getChartTimeRangeString(widget.tabType, currentPageIndex);
                  });
                },
                itemBuilder: (BuildContext context, int index) {
                  return Column(
                    key: PageStorageKey(index),
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      AspectRatio(
                          aspectRatio: 1,
                          child: TotalChartWidget(
                            type: widget.tabType,
                            selectIndexCallback: (pi, si) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                setState(() {
                                  selectedIndex = si;
                                  pageIndex = pi;
                                });
                              });
                            },
                            chartDataCallback: (data) {
                              setState(() {
                                dataCenterChartResp = data;
                              });
                            },
                            pageIndex: index,
                          ))
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
