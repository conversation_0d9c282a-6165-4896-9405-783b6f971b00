import 'package:get/get.dart';
import 'package:lsenglish/model/order_item_resp%20copy/order_item_resp.dart';
import 'package:lsenglish/net/net.dart';

class OrderlistController extends GetxController {
  var orderList = <OrderItemResp>[].obs;
  @override
  void onInit() {
    super.onInit();
    Net.getRestClient().getOrderList().then((value) {
      orderList.value = value.data;
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
