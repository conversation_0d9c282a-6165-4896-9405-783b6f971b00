import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/utils/login.dart';

class SplashController extends GetxController {
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    // Get.toNamed(Routes.LSDESC);
    if (isLogin()) {
      Get.offAndToNamed(Routes.MAIN);
    } else {
      Get.offAndToNamed(Routes.LOGIN);
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
