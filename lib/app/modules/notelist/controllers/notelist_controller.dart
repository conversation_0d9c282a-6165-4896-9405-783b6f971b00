import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/note_model.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/utils/video.dart';
import 'package:lsenglish/widgets/base_dialog.dart';

class NoteListController extends GetxController with GetTickerProviderStateMixin {
  late TabController tabController;
  late PageController pageController;
  bool _isPageChanging = false;
  var _noteList = <NoteModel>[];
  var noteList = <NoteModel>[].obs;
  var localSubtitleId = "";
  var subtitlePath = "";
  var subtitles = <Subtitle>[].obs;
  var noteSubtitles = <Subtitle>[].obs;
  var notesMap = <int, NoteModel>{}.obs;
  var localSentenceCollectIndexs = <int>[].obs;
  @override
  void onInit() async {
    super.onInit();
    tabController = TabController(vsync: this, length: 2);
    pageController = PageController();
    tabController.addListener(_handleTabSelection);
    localSubtitleId = Get.arguments?['localSubtitleId'] ?? "";
    subtitlePath = Get.arguments?['subtitlePath'] ?? "";
    loadData();
  }

  void loadData() {
    loadSubtitleAsync();
    Net.getRestClient().localsentence(localSubtitleId).then((value) {
      localSentenceCollectIndexs.value = value.data.indexs ?? [];
    });
  }

  void updateList() {
    // var tempsubtitles = <Subtitle>[];
    // for (var note in _noteList) {
    //   var matchingSubtitle = subtitles.firstWhereOrNull((s) => s.subtitleIndex == note.index);
    //   if (matchingSubtitle != null) {
    //     tempsubtitles.add(matchingSubtitle);
    //     notesMap[note.index!] = note;
    //   }
    // }
    // noteSubtitles.value = tempsubtitles;
    noteList.value = _noteList;
  }

  Future<void> loadSubtitleAsync() async {
    var data = await loadSubtitlesInternal(subtitlePath);
    subtitles.value = data.item2;
    localNotesAsync();
  }

  Future<void> localNotesAsync() async {
    Net.getRestClient().getNoteList(1, subtitles.length, localSubtitleId, 1).then((value) {
      // _noteList = value.data..sort((a, b) => a.index!.compareTo(b.index!));

      updateList();
    }).catchError((e) {
      _noteList = [];
      return;
    });
  }

  void deleteNote(int index) {
    Get.dialog(CommonDialog(
      title: '是否删除笔记?',
      options: const ["删除"],
      callbacks: [
        () => {
              Net.getRestClient().deleteLocalNote({
                'id': noteList[index].id,
              }).then((p) {
                toastInfo("删除成功");
                _noteList.removeAt(index);
                updateList();
              })
            }
      ],
    ));
  }

  void noteClick(int index) {
    // Get.bottomSheet(
    //   AddNoteWidget(
    //     subtitle: noteSubtitles[index],
    //     index: noteSubtitles[index].subtitleIndex,
    //     localSubtitleId: localSubtitleId,
    //     noteId: noteList[index].id ?? "",
    //     noteSaveCallback: (type) {
    //       if (type == 2) {
    //         updateList();
    //         loadData();
    //       }
    //     },
    //   ),
    //   shape: const RoundedRectangleBorder(
    //     borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    //   ),
    //   isDismissible: false,
    //   isScrollControlled: true,
    //   enableDrag: false,
    //   barrierColor: const Color(0x99000000),
    // );
  }

  void noteClickByAll(int index) {
    // Get.bottomSheet(
    //   AddNoteWidget(
    //     subtitle: subtitles[index],
    //     index: subtitles[index].subtitleIndex,
    //     localSubtitleId: localSubtitleId,
    //     noteId: notesMap[index]?.id ?? "",
    //     noteSaveCallback: (type) {
    //       if (type == 2) {
    //         updateList();
    //         loadData();
    //       }
    //     },
    //   ),
    //   shape: const RoundedRectangleBorder(
    //     borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    //   ),
    //   isDismissible: false,
    //   isScrollControlled: true,
    //   enableDrag: false,
    //   barrierColor: const Color(0x99000000),
    // );
  }

  void _handleTabSelection() {
    if (_isPageChanging) {
      return;
    }
    if (tabController.indexIsChanging) {
      pageController.jumpToPage(tabController.index);
    }
  }

  void onPageChanged(int index) async {
    _isPageChanging = true;
    tabController.animateTo(index);
    Future.delayed(const Duration(milliseconds: 300));
    _isPageChanging = false;
  }

  void addOneLocalSentenceCollect(int subtitleIndex) {
    toastInfo("收藏成功", icon: Icons.check_circle_outline_sharp);
    localSentenceCollectIndexs.add(subtitleIndex);
    localSentenceCollectIndexs.refresh();
    Net.getRestClient().addLocalSencenceCollect({
      'localSubtitleId': localSubtitleId,
      'indexs': [subtitleIndex],
    });
  }

  void removeOneLocalSentenceCollect(int subtitleIndex) {
    toastInfo("取消收藏");
    localSentenceCollectIndexs.remove(subtitleIndex);
    localSentenceCollectIndexs.refresh();
    Net.getRestClient().removeLocalSencenceCollect({
      'localSubtitleId': localSubtitleId,
      'indexs': [subtitleIndex],
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    tabController.dispose();
    pageController.dispose();
    super.onClose();
  }
}
