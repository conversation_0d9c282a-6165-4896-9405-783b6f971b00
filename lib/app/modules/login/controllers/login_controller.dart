import 'dart:io';

import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class LoginController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    SPUtil().clearUserInfo();
  }

  @override
  void onReady() {
    super.onReady();
    SPUtil().clearUserInfo();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void loginByPassword() {
    Net.getRestClient().login({'username': "miaoyongjun", 'password': "11111111"}).then((value) => {
          Net.getDio().options.headers['Authorization'] = value.data.token,
          SPUtil().saveUserInfo(value.data),
          ObsUtil().loginStatus.value = true,
          ObsUtil().loginStatus.refresh(),
          Get.offAndToNamed(Routes.MAIN),
        });
  }

  // void loginTing() {
  //   Net.getRestClient().loginByApple({
  //     'userIdentifier': '000144.d20fdeb8ce3a431e9864673cb9623569.0356',
  //     'identityToken': 'credential.identityToken',
  //   }).then((value) {
  //     Net.getDio().options.headers['Authorization'] = value.data.token;
  //     SPUtil().saveUserInfo(value.data);
  //     ObsUtil().loginStatus.value = true;
  //     ObsUtil().loginStatus.refresh();
  //     Get.back();
  //   });
  // }
  void loginTing() {
    Net.getRestClient().login({'username': "panting", 'password': "11111111"}).then((value) {
      Net.getDio().options.headers['Authorization'] = value.data.token;
      SPUtil().saveUserInfo(value.data);
      ObsUtil().loginStatus.value = true;
      ObsUtil().loginStatus.refresh();
      Get.back();
    });
  }

  void appleLogin() async {
    if (Platform.isMacOS) {
      loginByPassword();
      return;
    }
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    Net.getRestClient().loginByApple({
      'userIdentifier': credential.userIdentifier,
      'identityToken': credential.identityToken,
    }).then((value) {
      Net.getDio().options.headers['Authorization'] = value.data.token;
      SPUtil().saveUserInfo(value.data);
      ObsUtil().loginStatus.value = true;
      ObsUtil().loginStatus.refresh();
      Get.back();
    });
  }
}
