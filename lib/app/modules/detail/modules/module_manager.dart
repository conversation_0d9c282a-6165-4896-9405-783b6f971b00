import 'dart:async';
import 'package:lsenglish/app/modules/detail/interfaces/detail_module_interface.dart';
import 'package:lsenglish/utils/log.dart';

/// 模块管理器，负责管理所有功能模块的生命周期
class ModuleManager {
  final Map<String, DetailModuleInterface> _modules = {};
  final List<StreamSubscription> _subscriptions = [];
  bool _isInitialized = false;

  /// 注册模块
  void registerModule(String key, DetailModuleInterface module) {
    if (_modules.containsKey(key)) {
      logger("ModuleManager: 模块 $key 已存在，将被覆盖");
    }
    _modules[key] = module;
    logger("ModuleManager: 注册模块 $key (${module.moduleName})");
  }

  /// 获取模块
  T? getModule<T extends DetailModuleInterface>(String key) {
    final module = _modules[key];
    if (module is T) {
      return module;
    }
    logger("ModuleManager: 模块 $key 不存在或类型不匹配");
    return null;
  }

  /// 获取所有模块
  Map<String, DetailModuleInterface> get allModules => Map.unmodifiable(_modules);

  /// 初始化所有模块
  Future<void> initialize() async {
    if (_isInitialized) {
      logger("ModuleManager: 模块管理器已经初始化");
      return;
    }

    logger("ModuleManager: 开始初始化 ${_modules.length} 个模块");

    try {
      for (final entry in _modules.entries) {
        final key = entry.key;
        final module = entry.value;

        logger("ModuleManager: 初始化模块 $key (${module.moduleName})");
        await module.initialize();
      }

      _isInitialized = true;
      logger("ModuleManager: 所有模块初始化完成");
    } catch (e) {
      logger("ModuleManager: 模块初始化失败 - $e");
      rethrow;
    }
  }

  /// 销毁所有模块
  Future<void> dispose() async {
    if (!_isInitialized) {
      logger("ModuleManager: 模块管理器未初始化，跳过销毁");
      return;
    }

    logger("ModuleManager: 开始销毁 ${_modules.length} 个模块");

    // 取消所有订阅
    for (final subscription in _subscriptions) {
      await subscription.cancel();
    }
    _subscriptions.clear();

    // 销毁所有模块
    for (final entry in _modules.entries) {
      final key = entry.key;
      final module = entry.value;

      try {
        logger("ModuleManager: 销毁模块 $key (${module.moduleName})");
        await module.dispose();
      } catch (e) {
        logger("ModuleManager: 销毁模块 $key 失败 - $e");
      }
    }

    _modules.clear();
    _isInitialized = false;
    logger("ModuleManager: 所有模块销毁完成");
  }

  Future<void> onFetchDetailData({Exception? error}) async {
    for (final module in _modules.values) {
      await module.onFetchDetailData(error);
    }
  }

  /// 通知所有模块可见性丢失
  void onVisibilityLost() {
    logger("ModuleManager: 通知所有模块可见性丢失");
    for (final module in _modules.values) {
      try {
        module.onVisibilityLost();
      } catch (e) {
        logger("ModuleManager: 模块 ${module.moduleName} 处理可见性丢失失败 - $e");
      }
    }
  }

  /// 通知所有模块可见性获得
  void onVisibilityGained() {
    logger("ModuleManager: 通知所有模块可见性获得");
    for (final module in _modules.values) {
      try {
        module.onVisibilityGained();
      } catch (e) {
        logger("ModuleManager: 模块 ${module.moduleName} 处理可见性获得失败 - $e");
      }
    }
  }

  /// 通知所有模块界面尺寸变化
  void didChangeMetrics() {
    logger("ModuleManager: 通知所有模块界面尺寸变化");
    for (final module in _modules.values) {
      try {
        module.didChangeMetrics();
      } catch (e) {
        logger("ModuleManager: 模块 ${module.moduleName} 处理界面尺寸变化失败 - $e");
      }
    }
  }

  /// 添加订阅
  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  /// 移除订阅
  void removeSubscription(StreamSubscription subscription) {
    _subscriptions.remove(subscription);
  }

  /// 检查模块是否已初始化
  bool get isInitialized => _isInitialized;

  /// 获取模块数量
  int get moduleCount => _modules.length;

  /// 检查模块是否存在
  bool hasModule(String key) => _modules.containsKey(key);

  /// 获取模块名称列表
  List<String> get moduleNames => _modules.keys.toList();

  /// 获取所有模块的状态信息
  Map<String, dynamic> getAllModulesState() {
    final state = <String, dynamic>{};

    for (final entry in _modules.entries) {
      final key = entry.key;
      final module = entry.value;
      state[key] = module.stateInfo;
    }

    return state;
  }

  /// 获取模块管理器状态
  Map<String, dynamic> getManagerState() {
    return {
      'isInitialized': _isInitialized,
      'moduleCount': moduleCount,
      'moduleNames': moduleNames,
      'subscriptionCount': _subscriptions.length,
    };
  }
}
