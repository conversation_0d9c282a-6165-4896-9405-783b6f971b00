import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/modules/base_module.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/player_menu.dart';
import 'package:lsenglish/utils/video_compression.dart';
import 'package:lsenglish/widgets/base_dialog.dart';

/// 播放控制模块
/// 负责管理视频播放控制、跳转、速度切换等功能
class PlaybackModule extends DefaultModule {
  // 播放器菜单项
  final RxList<PlayerMenu> _playerMenuItems = <PlayerMenu>[].obs;

  PlaybackModule() : super('PlaybackModule');

  @override
  Future<void> onInitialize() async {
    logger("$moduleName: 初始化播放控制模块");
    // 播放控制模块初始化相对简单，主要是方法封装
    logger("$moduleName: 播放控制模块初始化完成");
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {}

  @override
  Future<void> onDispose() async {
    logger("$moduleName: 销毁播放控制模块");
    logger("$moduleName: 播放控制模块销毁完成");
  }

  /// 获取播放器菜单项
  RxList<PlayerMenu> get playerMenuItems => _playerMenuItems;

  /// 初始化播放器菜单
  void initializePlayerMenu() {
    _playerMenuItems.value = PlayerMenuManager().menuByConfig();
  }

  /// 跳转到上一句字幕 - 使用简化的统一入口
  void jumpPreSubtitle() {
    logger("$moduleName: 跳转到上一句字幕");
    detailState.videoKit.skipFindReverse = true;

    var currentIndex = detailState.videoKit.currentSubtitleIndex.value;
    var targetIndex = currentIndex - 1;

    if (detailState.videoKit.openLsMode.value) {
      // LS模式下使用统一入口
      detailState.videoKit.playSubtitleInLsMode(targetIndex, fromUserAction: true);
    } else {
      // 非LS模式下使用原有逻辑
      detailState.videoKit.preSubtitle();
      detailState.videoKit.play();
    }
  }

  /// 跳转到下一句字幕 - 使用简化的统一入口
  void jumpNextSubtitle() {
    logger("$moduleName: 跳转到下一句字幕");
    detailState.videoKit.skipFindReverse = false;

    var currentIndex = detailState.videoKit.currentSubtitleIndex.value;
    var targetIndex = currentIndex + 1;

    if (detailState.videoKit.openLsMode.value) {
      // LS模式下使用统一入口
      detailState.videoKit.playSubtitleInLsMode(targetIndex, fromUserAction: true);
    } else {
      // 非LS模式下使用原有逻辑
      detailState.videoKit.nextSubtitle();
      detailState.videoKit.play();
    }
  }

  /// LS模式播放点击处理
  void lsPlayClick() async {
    logger("$moduleName: LS模式播放点击");
    if (!detailState.isLandscape.value &&
        detailState.pageController.hasClients &&
        detailState.pageController.page == null &&
        detailState.videoKit.openLsMode.value) {
      logger("$moduleName: LS模式播放点击 - pageController.page为null，跳过");
      return;
    }

    await controller.speechEvaluationModule.recognitionService.stop();
    playCurrentLsIndex();
  }

  /// 播放当前LS索引 - 使用简化的统一入口
  Future<void> playCurrentLsIndex() async {
    var index = subtitleModule.getCurrentSubtitleIndex();
    logger("$moduleName: 播放当前LS索引 index=$index");

    // 使用新的统一播放入口，自动处理各种播放状态
    await detailState.videoKit.playSubtitleInLsMode(index, fromUserAction: true);
  }

  /// LS容器点击处理 - 使用简化的统一入口
  void lsContainerClick(int index) async {
    logger("$moduleName: LS容器点击 index=$index");
    controller.speechEvaluationModule.stopRecording(forceThrowRecord: true);

    // 使用新的统一播放入口
    await detailState.videoKit.playSubtitleInLsMode(index, fromUserAction: true);
  }

  /// 切换播放速度
  void switchSpeed() async {
    logger("$moduleName: 切换播放速度");
    await detailState.videoKit.switchSpeed();

    lsContainerClick(detailState.currentPage.value);
    detailState.showCurrentLandScapeToast.value = true;
    detailState.currentToast.value = "已切换至${detailState.videoKit.currentSpeed.value}倍数";
    await Future.delayed(const Duration(seconds: 1));
    detailState.showCurrentLandScapeToast.value = false;
  }

  /// 关闭LS模式
  void closeLsMode() {
    logger("$moduleName: 关闭LS模式");
    detailState.videoKit.openLsMode.value = false;
    controller.subtitleListScrollToCenter();
  }

  /// 打开LS模式
  void openLsMode() {
    logger("$moduleName: 打开LS模式");
    detailState.videoKit.openLsMode.value = true;
    controller.jumpToPage(detailState.videoKit.currentSubtitleIndex.value);
  }

  /// 全屏时返回处理
  void backWhenFullscreen() {
    logger("$moduleName: 全屏时返回");
    detailState.videoKit.exitNativeFullscreen();
  }

  /// 检查视频分辨率并提示压缩
  Future<void> checkVideoResolution(String videoPath) async {
    logger("$moduleName: 检查视频分辨率");
    var videoInfo = await FFmpegUtils().getVideoInfo(videoPath);
    logger("$moduleName: 视频信息 width=${videoInfo['width']} height=${videoInfo['height']} frameRate=${videoInfo['frameRate']}");

    if (FFmpegUtils().isVideoOver4K(videoInfo['width'] ?? 0, videoInfo['height'] ?? 0)) {
      detailState.videoKit.pause();
      Get.dialog(
        CommonDialog(title: "视频分辨率大于等于4K,会影响体验\n是否进行视频压缩？", options: const [
          "确定"
        ], callbacks: [
          () => {
                VideoCompressionUtil().addCompress(VideoCompression(videoPath: videoPath)),
                Get.back(),
              }
        ]),
        barrierDismissible: false,
      );
    }
  }

  /// 获取当前播放状态
  bool get isPlaying => detailState.videoKit.isPlaying();

  /// 获取当前播放速度
  String get currentSpeed => (detailState.videoKit.currentSpeed.value).toString();

  /// 获取是否处于LS模式
  bool get isInLsMode => detailState.videoKit.openLsMode.value;

  /// 获取当前字幕索引
  int get currentSubtitleIndex => subtitleModule.getCurrentSubtitleIndex();

  @override
  Map<String, dynamic> get stateInfo => {
        'moduleName': moduleName,
        'isPlaying': isPlaying,
        'currentSpeed': currentSpeed,
        'isInLsMode': isInLsMode,
        'currentSubtitleIndex': currentSubtitleIndex,
      };
}
