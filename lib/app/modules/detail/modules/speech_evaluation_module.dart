import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:lsenglish/app/modules/detail/modules/base_module.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_day_resp.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';
import 'package:lsenglish/model/speech_evaluation_resp.dart';
import 'package:lsenglish/model/video_time_interval.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/extension.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/oss.dart';
import 'package:lsenglish/utils/speech_evaluation.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/utils/url.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/datacenter_time_manager_extension.dart';
import 'package:lsenglish/widgets/base_dialog.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path/path.dart';
import 'package:lsenglish/utils/obs.dart';
import '../controllers/detail_util.dart';

/// 语音评测模块实现
class SpeechEvaluationModule extends DefaultModule {
  final SpeechEvaluation _recognitionService = SpeechEvaluation.instance;
  final FlutterSoundPlayer _recordSoundPlayer = FlutterSoundPlayer(logLevel: Level.fatal);

  // 响应式数据
  final RxMap<int, SpeechEvaluationResp> _speechEvaluationMap = <int, SpeechEvaluationResp>{}.obs;
  final RxBool _recordingInLsMode = false.obs;
  final RxBool _hasMicPermission = false.obs;

  // 从服务器获取的已录制字幕时间范围（响应式）
  final RxList<VideoTimeInterval> _subtitleTimeRanges = <VideoTimeInterval>[].obs;

  // 当前天的计划数据
  var currentPlanDay = Rx<PlanDayResp?>(null);

  // 流控制器
  final StreamController<Map<int, SpeechEvaluationResp>> _evaluationController = StreamController<Map<int, SpeechEvaluationResp>>.broadcast();

  // 录音相关状态
  String? _currentRecordFilePath;
  String? _currentRecordFileName;

  SpeechEvaluationModule() : super('SpeechEvaluationModule');

  @override
  Future<void> onInitialize() async {
    logger("$moduleName: 初始化语音评测模块");

    // 初始化录音播放器
    await _recordSoundPlayer.openPlayer();

    // 初始化语音识别服务
    await _recognitionService.init();

    // 检查麦克风权限
    _hasMicPermission.value = await Permission.microphone.isGranted;

    // 设置语音识别结果监听
    _recognitionService.resultNotifier.addListener(_handleSpeechEvaluationResult);

    // 监听评测警告
    _recognitionService.warningNotifier.addListener(_handleSpeechEvaluationWarning);

    await _getDayRecordedRanges();

    addSubscription(detailState.videoKit.playing.listen((playing) {
      if (playing) {
        _recordSoundPlayer.stopPlayer();
      }
    }));

    logger("$moduleName: 语音评测模块初始化完成");
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {
    await fetchSpeechEvaluationList();
  }

  @override
  Future<void> onDispose() async {
    logger("$moduleName: 销毁语音评测模块");

    // 停止录音和播放
    await stopRecording();
    await _recordSoundPlayer.stopPlayer();
    await _recordSoundPlayer.closePlayer();

    // 销毁语音识别服务
    _recognitionService.dispose();

    // 关闭流控制器
    await _evaluationController.close();

    // 通过 obs 发送录制时间范围长度
    final updateData = {
      'newSentences': _subtitleTimeRanges.length,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    ObsUtil().updatePlanProgress.value = updateData;
    logger("$moduleName: 已通过 obs 发送录制时间范围长度: ${_subtitleTimeRanges.length}");
    // 新增：批量上传本地录音到OSS并同步到后端（异步执行，不阻塞页面关闭）
    await batchUploadSpeechEvaluationsToOssAndSync(_speechEvaluationMap);
  }

  /// 调用updateSentences接口，传递当前新增的时间范围
  Future<void> _updateSentences(VideoTimeInterval newTimeRange) async {
    try {
      if (detailState.localDetailResp == null) {
        logger("$moduleName: localDetailResp为空，无法调用updateSentences接口");
        return;
      }

      // 构建当前新增的时间范围
      final Map<String, int> subtitleTimeRange = {'start': newTimeRange.start ?? 0, 'end': newTimeRange.end ?? 0};
      logger("$moduleName: 准备调用updateSentences接口，新增时间范围: ${newTimeRange.start}-${newTimeRange.end}");

      // 调用接口，传递当前新增的时间范围
      await Net.getRestClient().updateSentences({'dayId': detailState.dayId, 'recordedRange': subtitleTimeRange});

      logger("$moduleName: updateSentences接口调用成功");
    } catch (e) {
      logger("$moduleName: updateSentences接口调用失败: $e");
    }
  }

  Future<void> _getDayRecordedRanges() async {
    if (detailState.dayId.isEmpty == true) {
      logger("$moduleName: dayId为空，跳过获取录音时间范围列表");
      return;
    }

    final response = await Net.getRestClient().getPlanDay(detailState.dayId);
    if (response.data != null) {
      logger("$moduleName: 从服务器获取到PlanDayResp数据");

      // 存储当前天的计划数据
      currentPlanDay.value = response.data;

      // 解析recordedRanges
      final recordedRanges = response.data?.recordedRanges ?? [];

      // 清空并重新填充已录制的时间范围
      _subtitleTimeRanges.clear();
      _subtitleTimeRanges.addAll(recordedRanges);

      for (final timeRange in recordedRanges) {
        logger("$moduleName: 服务器已录制时间范围: ${timeRange.start}-${timeRange.end}");
      }

      logger("$moduleName: 已加载 ${_subtitleTimeRanges.length} 个已录制的时间范围");
    } else {
      logger("$moduleName: 服务器没有已录制的时间范围");
      _subtitleTimeRanges.clear();
    }
  }

  /// 处理语音评测结果
  void _handleSpeechEvaluationResult() async {
    final result = _recognitionService.resultNotifier.value;
    if (result != null) {
      await _processSpeechEvaluationResult(result);
    }
  }

  /// 处理语音评测警告
  void _handleSpeechEvaluationWarning() {
    final warnings = _recognitionService.warningNotifier.value;
    final msg = SpeechEvaluation.getWarningTextForList(warnings);
    if (msg != null && msg.isNotEmpty) {
      msg.toast;
    }
  }

  /// 记录语音评测学习统计
  void _recordSpeechEvaluationLearning(int subtitleIndex, SpeechEvaluationResult result) {
    final score = _recognitionService.getScore(result);
    if (score >= 0) {
      // 获取当前字幕的时间范围
      if (subtitleIndex >= 0 && subtitleIndex < detailState.videoKit.subtitles.length) {
        final subtitle = detailState.videoKit.subtitles[subtitleIndex];
        final startTime = subtitle.start.inMilliseconds;
        final endTime = subtitle.end.inMilliseconds;

        // 检查是否已经在服务器上录制过这个时间范围
        final currentTimeRange = VideoTimeInterval(start: startTime, end: endTime);
        bool alreadyRecorded = _subtitleTimeRanges.any((range) => range.start == startTime && range.end == endTime);

        if (!alreadyRecorded) {
          // 如果服务器上没有录制过，则添加到本地记录中并调用接口
          _subtitleTimeRanges.add(currentTimeRange);
          logger("$moduleName: 新增录制语音评测统计 - 句子索引: $subtitleIndex, 字幕时间范围: $startTime-$endTime, 评分: $score");

          // 调用 updateSentences 接口，传递当前新增的时间范围
          _updateSentences(currentTimeRange);
        } else {
          logger("$moduleName: 字幕时间范围 $startTime-$endTime 已在服务器上录制过，跳过统计");
        }
      }
    } else {
      logger("$moduleName: 句子 $subtitleIndex 评分 $score 未达到80分，不计入学习统计");
    }
  }

  /// 处理语音评测结果
  Future<void> _processSpeechEvaluationResult(SpeechEvaluationResult result) async {
    logger("$moduleName: 收到语音评测结果: eof=${result.eof}");

    // 如果是最终结果，立即更新本地UI，然后上传到服务器
    if (result.eof == 1) {
      final currentIndex = subtitleModule.getCurrentSubtitleIndex();
      if (currentIndex >= detailState.videoKit.subtitles.length) {
        logger("$moduleName: 字幕索引越界: $currentIndex");
        return;
      }

      final subtitle = detailState.videoKit.subtitles[currentIndex];

      // 获取最新的录音路径
      String? localAudioPath;
      try {
        localAudioPath = await _recognitionService.stkouyuPlugin.getLastRecordPath();
        logger("$moduleName: 获取到本地录音路径: $localAudioPath");
      } catch (e) {
        logger("$moduleName: 获取本地录音路径失败: $e");
      }

      // 立即创建临时的评测记录更新UI
      final tempEvaluation = SpeechEvaluationResp(
        id: null, // 临时记录没有ID
        resourceId: detailState.localDetailResp?.resourceId,
        resourceType: detailState.localDetailResp?.resourceType,
        content: subtitle.targetData,
        audioUrl: result.audioUrl,
        startTime: subtitle.start.inMilliseconds,
        endTime: subtitle.end.inMilliseconds,
        evalResult: result,
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
        localAudioPath: localAudioPath, // 设置本地音频路径
      );

      // 立即更新本地UI
      _speechEvaluationMap[currentIndex] = tempEvaluation;

      // 异步上传到服务器
      _uploadSpeechEvaluationResult(result, currentIndex, tempEvaluation);
    }
  }

  /// 上传语音评测结果到服务器
  Future<void> _uploadSpeechEvaluationResult(SpeechEvaluationResult result, int subtitleIndex, SpeechEvaluationResp tempEvaluation) async {
    if (detailState.localDetailResp == null) {
      logger("$moduleName: localDetailResp为空，无法上传语音评测结果");
      return;
    }

    try {
      final subtitle = detailState.videoKit.subtitles[subtitleIndex];
      final existingEvaluation = _speechEvaluationMap[subtitleIndex];

      // 判断是新增还是更新
      final bool isUpdate = existingEvaluation?.id != null;

      if (isUpdate) {
        // 更新现有记录
        final response = await Net.getRestClient().updateSpeechEvaluation({
          'id': existingEvaluation!.id!,
          'resourceId': detailState.localDetailResp!.resourceId!,
          'resourceType': detailState.localDetailResp!.resourceType!,
          'content': jsonEncode(result),
          'audioUrl': result.audioUrl ?? '',
          'startTime': subtitle.start.inMilliseconds,
          'endTime': subtitle.end.inMilliseconds,
        });

        // 更新本地数据（替换临时数据）
        _speechEvaluationMap[subtitleIndex] = response.data;
        _speechEvaluationMap[subtitleIndex]?.evalResult = result;
        _speechEvaluationMap[subtitleIndex]?.localAudioPath = existingEvaluation.localAudioPath;

        logger("$moduleName: 语音评测结果更新成功: ${response.data.id}");
      } else {
        // 创建新记录
        final response = await Net.getRestClient().createSpeechEvaluation({
          'resourceId': detailState.localDetailResp!.resourceId!,
          'resourceType': detailState.localDetailResp!.resourceType!,
          'content': jsonEncode(result),
          'audioUrl': result.audioUrl ?? '',
          'startTime': subtitle.start.inMilliseconds,
          'endTime': subtitle.end.inMilliseconds,
        });

        // 用服务器返回的数据替换临时数据
        _speechEvaluationMap[subtitleIndex] = response.data;
        _speechEvaluationMap[subtitleIndex]?.evalResult = SpeechEvaluationResult.fromJson(jsonDecode(response.data.content!));
        _speechEvaluationMap[subtitleIndex]?.localAudioPath = existingEvaluation?.localAudioPath;

        logger("$moduleName: 语音评测结果创建成功: ${response.data.id}");
      }
      _recordSpeechEvaluationLearning(subtitleIndex, result);
    } catch (e) {
      logger("$moduleName: 上传语音评测结果失败: $e");
      // 上传失败时，保留临时数据，用户仍能看到评测结果
    }
  }

  /// 获取语音评测列表
  Future<void> fetchSpeechEvaluationList() async {
    if (detailState.localDetailResp == null) {
      return;
    }

    try {
      final response = await Net.getRestClient().getSpeechEvaluations(
        detailState.localDetailResp!.resourceId!,
        detailState.localDetailResp!.resourceType!,
      );

      // 构建索引映射
      _speechEvaluationMap.clear();
      for (final evaluation in response.data) {
        // 根据时间范围匹配字幕索引
        final index = _findSubtitleIndexByTime(evaluation.startTime, evaluation.endTime);
        if (index != -1) {
          _speechEvaluationMap[index] = evaluation;
          // 新增：content转evalResult
          if (evaluation.content != null && evaluation.content!.isNotEmpty) {
            try {
              evaluation.evalResult = SpeechEvaluationResult.fromJson(jsonDecode(evaluation.content!));
            } catch (e) {
              logger("$moduleName: content转evalResult失败: $e");
            }
          }
        }
      }

      logger("$moduleName: 获取语音评测列表成功，共${_speechEvaluationMap.length}条记录");
    } catch (e) {
      logger("$moduleName: 获取语音评测列表失败: $e");
    }
  }

  /// 根据时间范围查找字幕索引
  int _findSubtitleIndexByTime(int? startTime, int? endTime) {
    if (startTime == null || endTime == null) return -1;

    for (int i = 0; i < detailState.videoKit.subtitles.length; i++) {
      final subtitle = detailState.videoKit.subtitles[i];
      if (subtitle.start.inMilliseconds == startTime && subtitle.end.inMilliseconds == endTime) {
        return i;
      }
    }
    return -1;
  }

  /// 开始录音
  Future<void> startRecording() async {
    return safeExecute(() async {
      if (!_hasMicPermission.value) {
        Map<Permission, PermissionStatus> statuses = await [
          Permission.microphone,
        ].request();
        // 检查每个权限的状态
        if (statuses[Permission.microphone]?.isGranted == false) {
          _requestVoicePermission(title: "需要录音权限");
          return;
        }
        _hasMicPermission.value = true;
      }

      int index = subtitleModule.getCurrentSubtitleIndex();
      bool isLSMode = detailState.videoKit.openLsMode.value;

      DataCenterHelper.recordRecordEvent(
        isStart: true,
        index: index,
        isLSMode: isLSMode,
        subtitles: detailState.videoKit.subtitles,
      );

      await _recordStart();
    }, operationName: 'startRecording');
  }

  /// 内部录音开始方法
  Future<void> _recordStart() async {
    logger("$moduleName: _recordStart playingInLsMode to false");

    try {
      await detailState.videoKit.pause();
    } catch (e) {
      logger("$moduleName: 播放器暂停失败，可能已销毁: $e");
    }

    if (detailState.videoKit.playing.value == true || _recordSoundPlayer.isPlaying) {
      await _recordSoundPlayer.stopPlayer();
    }

    detailState.videoKit.playing.value = false;

    var recordFilePath = await FileUtils().getStringByDir(FileUtils().getRecordDir());
    logger("$moduleName: recordStart _recordFilePath = $recordFilePath");

    _recordingInLsMode.value = true;

    // 录音开始时隐藏字幕
    subtitleModule.hideSubtitleOnRecordStart();

    // 设置录音文件路径和名称
    _currentRecordFilePath = recordFilePath;
    _currentRecordFileName =
        "${basenameWithoutExtension(filterVideoUrl(detailState.videoUrlOrPath))}${detailState.videoKit.currentSubtitleIndex.value}.mp3";

    _recognitionService.startSentence(
      detailState.videoKit.subtitles[detailState.currentPage.value].targetData,
      localAudioFilePath: _currentRecordFilePath,
      localAudioFileName: _currentRecordFileName,
    );
  }

  /// 停止录音
  Future<void> stopRecording({bool forceThrowRecord = false}) async {
    return safeExecute(() async {
      logger("$moduleName: recordStop start");

      // 安全检查：确保播放器仍然可用
      try {
        detailState.videoKit.pause();
      } catch (e) {
        logger("$moduleName: 播放器暂停失败，可能已销毁: $e");
      }

      _recordingInLsMode.value = false;
      await _recognitionService.stop();

      // 安全检查：确保控制器仍然可用
      int index = subtitleModule.getCurrentSubtitleIndex();
      bool isLSMode = detailState.videoKit.openLsMode.value;

      DataCenterHelper.recordRecordEvent(
        isStart: false,
        index: index,
        isLSMode: isLSMode,
        subtitles: detailState.videoKit.subtitles,
      );

      if (forceThrowRecord) {
        logger("$moduleName: recordStop forceThrowRecord return");
        return;
      }

      final recordPath = await _recognitionService.getLastRecordPath() ?? "";
      await _playRecordWhenRecordEnd(recordPath);

      // 录音结束后自动显示字幕
      subtitleModule.showSubtitleAfterRecordEnd();
    }, operationName: 'stopRecording');
  }

  /// 录音结束后播放录音
  Future<void> _playRecordWhenRecordEnd(String path) async {
    logger("$moduleName: playRecordWhenRecordEnd path=$path");
    if (path.isEmpty) {
      return;
    }

    // 这里需要从配置中获取是否自动播放录音的设置
    // 暂时使用默认值，后续可以从配置中获取
    bool autoPlayRecordWhenRecordEnd = true;

    if (autoPlayRecordWhenRecordEnd) {
      try {
        //可能是调用了recordSoundPlayer的stop之后 还是有一些时间需要回收
        //不加就会导致偶尔出现录制结束后播放录音只播放了一点点
        await Future.delayed(const Duration(milliseconds: 350));

        // 安全检查：确保播放器仍然可用
        try {
          await detailState.videoKit.pause();
        } catch (e) {
          logger("$moduleName: 播放器暂停失败，可能已销毁: $e");
        }

        _recordSoundPlayer.startPlayer(fromURI: path);
      } catch (e) {
        logger("$moduleName: recordStop 录音播放失败: $e");
      }
    }
  }

  /// 播放录音
  Future<void> playRecord() async {
    return safeExecute(() async {
      var index = subtitleModule.getCurrentSubtitleIndex();
      try {
        var evaluation = _speechEvaluationMap[index];
        var audioUrl = evaluation?.audioUrl;
        var localAudioPath = evaluation?.localAudioPath;
        logger("$moduleName: index=$index audioUrl =$audioUrl, localAudioPath =$localAudioPath");

        // 优先使用本地音频路径
        String? playPath;
        if (localAudioPath != null && localAudioPath.isNotEmpty) {
          playPath = localAudioPath;
          logger("$moduleName: 使用本地音频路径播放: $playPath");
        } else if (audioUrl != null && audioUrl.isNotEmpty) {
          playPath = ensureHttps(audioUrl);
          logger("$moduleName: 使用网络音频路径播放: $playPath");
        }

        if (playPath != null) {
          _recordSoundPlayer.startPlayer(
            fromURI: playPath,
            whenFinished: () {
              _recordSoundPlayer.stopPlayer();
              // 安全检查：确保播放器仍然可用
              try {
                detailState.videoKit.unmute();
              } catch (e) {
                logger("$moduleName: 播放器取消静音失败，可能已销毁: $e");
              }
            },
          );
        } else {
          "你还没有录制的声音".toast;
        }
      } catch (e) {
        logger("$moduleName: 录音播放失败: $e");
      }
    }, operationName: 'playRecord');
  }

  /// 请求语音权限
  void _requestVoicePermission({String title = "需要录音和语音识别权限"}) {
    if (Platform.isIOS || Platform.isAndroid) {
      // 这里需要导入CommonDialog，暂时注释掉
      CommonDialog(title: title, options: const [
        "跳转"
      ], callbacks: [
        () => {
              openAppSettings(),
            },
      ]).showDialog;
    }
  }

  /// 批量上传本地录音到OSS并同步到后端
  Future<void> batchUploadSpeechEvaluationsToOssAndSync(Map<int, SpeechEvaluationResp> speechEvaluationMap) async {
    try {
      List<Map<String, dynamic>> evaluations = [];
      for (var entry in speechEvaluationMap.entries) {
        var eval = entry.value;
        String? audioUrl = eval.audioUrl;
        if (eval.localAudioPath != null && eval.localAudioPath!.isNotEmpty) {
          String? ossUrl = await OssUtil().uploadUserRecordFile(eval.localAudioPath!);
          if (ossUrl != null) {
            audioUrl = ossUrl;
          }
        }
        if (audioUrl != null && eval.id != null) {
          evaluations.add({
            'id': eval.id,
            'audioUrl': audioUrl,
          });
        }
      }
      if (evaluations.isNotEmpty) {
        await Net.getRestClient().batchUpdateSpeechEvaluationAudioUrl({'evaluations': evaluations});
      }
    } catch (e) {
      logger('批量上传语音评测音频失败: $e');
    }
  }

  // Getter方法，供外部访问
  RxMap<int, SpeechEvaluationResp> get speechEvaluationMap => _speechEvaluationMap;
  RxBool get recordingInLsMode => _recordingInLsMode;
  RxBool get hasMicPermission => _hasMicPermission;
  FlutterSoundPlayer get recordSoundPlayer => _recordSoundPlayer;
  SpeechEvaluation get recognitionService => _recognitionService;

  /// 获取已录制的句子数量（响应式）
  RxInt get recordedSentencesCount => _subtitleTimeRanges.length.obs;

  /// 获取目标句子数量（响应式）
  RxInt get targetSentencesCount => (currentPlanDay.value?.targetSentences ?? 0).obs;

  /// 获取当前天的计划数据
  PlanDayResp? get planDayData => currentPlanDay.value;

  /// 获取模块状态信息
  @override
  Map<String, dynamic> get stateInfo => {
        ...super.stateInfo,
        'speechEvaluationCount': _speechEvaluationMap.length,
        'isRecording': _recordingInLsMode.value,
        'hasMicPermission': _hasMicPermission.value,
      };
}
