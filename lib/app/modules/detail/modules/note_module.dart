import 'dart:async';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/modules/base_module.dart';
import 'package:lsenglish/app/modules/detail/views/add_note_widget.dart';
import 'package:lsenglish/model/note_model.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/subtitle/subtitle.dart';

/// 笔记模块实现
class NoteModule extends DefaultModule {
  final RxMap<int, NoteModel> _notes = <int, NoteModel>{}.obs;
  final StreamController<Map<int, NoteModel>> _notesController = StreamController<Map<int, NoteModel>>.broadcast();

  NoteModule() : super('NoteModule');

  @override
  Future<void> onInitialize() async {
    logger("$moduleName: 初始化笔记模块");
    // 笔记模块初始化时不需要特殊操作
  }

  @override
  Future<void> onDispose() async {
    logger("$moduleName: 销毁笔记模块");
    await _notesController.close();
  }

  /// 设置资源信息（已废弃，现在通过BaseModule获取）
  @Deprecated('使用BaseModule获取资源信息')
  void setResourceInfoLegacy(String? resourceId, int? resourceType) {
    logger("$moduleName: 此方法已废弃，资源信息现在通过BaseModule获取");
  }

  /// 设置字幕列表（已废弃，现在通过BaseModule获取）
  @Deprecated('使用BaseModule获取字幕列表')
  void setSubtitlesLegacy(List<Subtitle> subtitles) {
    logger("$moduleName: 此方法已废弃，字幕列表现在通过BaseModule获取");
  }

  /// 从服务器数据加载笔记
  Future<void> loadNotesFromServer(List<NoteModel>? serverNotes) async {
    return safeExecuteSync(() {
      _notes.clear();
      if (serverNotes != null) {
        for (final note in serverNotes) {
          // 根据时间范围匹配字幕索引
          final index = _findSubtitleIndexByTime(note.videoStartTime, note.videoEndTime);
          if (index != -1) {
            _notes[index] = note;
          }
        }
      }
      _notifyNotesChanged();
    }, operationName: 'loadNotesFromServer');
  }

  /// 根据时间范围查找字幕索引
  int _findSubtitleIndexByTime(int? startTime, int? endTime) {
    if (startTime == null || endTime == null) return -1;

    for (int i = 0; i < detailState.videoKit.subtitles.length; i++) {
      final subtitle = detailState.videoKit.subtitles[i];
      if (subtitle.start.inMilliseconds == startTime && subtitle.end.inMilliseconds == endTime) {
        return i;
      }
    }
    return -1;
  }

  /// 通知笔记变化
  void _notifyNotesChanged() {
    _notesController.add(Map.from(_notes));
    // 触发响应式更新
    _notes.refresh();
  }

  // 笔记功能实现
  Future<void> addNote(int subtitleIndex, String content) async {
    return safeExecute(() async {
      if (detailState.localDetailResp?.resourceId == null || detailState.localDetailResp?.resourceType == null) {
        logger("$moduleName: 资源信息未设置，无法添加笔记");
        return;
      }

      if (subtitleIndex < 0 || subtitleIndex >= detailState.videoKit.subtitles.length) {
        logger("$moduleName: 字幕索引越界: $subtitleIndex");
        return;
      }

      final subtitle = detailState.videoKit.subtitles[subtitleIndex];

      // 创建笔记
      final response = await Net.getRestClient().addNote({
        'resourceId': detailState.localDetailResp!.resourceId,
        'resourceType': detailState.localDetailResp!.resourceType,
        'content': content,
        'videoStartTime': subtitle.start.inMilliseconds,
        'videoEndTime': subtitle.end.inMilliseconds,
      });

      // 更新本地数据
      _notes[subtitleIndex] = response.data;
      _notifyNotesChanged();

      logger("$moduleName: 笔记添加成功: ${response.data.id}");
    }, operationName: 'addNote');
  }

  Future<void> updateNote(String noteId, String content) async {
    return safeExecute(() async {
      if (noteId.isEmpty) {
        logger("$moduleName: 笔记ID为空，无法更新");
        return;
      }

      if (detailState.localDetailResp?.resourceId == null || detailState.localDetailResp?.resourceType == null) {
        logger("$moduleName: 资源信息未设置，无法更新笔记");
        return;
      }

      // 找到对应的字幕索引
      final subtitleIndex = _notes.entries.where((entry) => entry.value.id == noteId).map((entry) => entry.key).firstOrNull;
      if (subtitleIndex == null) {
        logger("$moduleName: 找不到对应的字幕索引: $noteId");
        return;
      }

      if (subtitleIndex < 0 || subtitleIndex >= detailState.videoKit.subtitles.length) {
        logger("$moduleName: 字幕索引越界: $subtitleIndex");
        return;
      }

      final subtitle = detailState.videoKit.subtitles[subtitleIndex];

      // 更新笔记（使用和addNote一样的接口，但传入noteId）
      final response = await Net.getRestClient().addNote({
        'id': noteId, // 传入noteId表示更新现有笔记
        'resourceId': detailState.localDetailResp!.resourceId,
        'resourceType': detailState.localDetailResp!.resourceType,
        'content': content,
        'videoStartTime': subtitle.start.inMilliseconds,
        'videoEndTime': subtitle.end.inMilliseconds,
      });

      // 更新本地数据
      _notes[subtitleIndex] = response.data;
      _notifyNotesChanged();

      logger("$moduleName: 笔记更新成功: ${response.data.id}");
    }, operationName: 'updateNote');
  }

  Future<void> deleteNote(String noteId) async {
    return safeExecute(() async {
      if (noteId.isEmpty) {
        logger("$moduleName: 笔记ID为空，无法删除");
        return;
      }

      // 删除笔记
      await Net.getRestClient().deleteLocalNote({
        'id': noteId,
      });

      // 从本地数据中移除
      final subtitleIndex = _notes.entries.where((entry) => entry.value.id == noteId).map((entry) => entry.key).firstOrNull;

      if (subtitleIndex != null) {
        _notes.remove(subtitleIndex);
        _notifyNotesChanged();
      }

      logger("$moduleName: 笔记删除成功: $noteId");
    }, operationName: 'deleteNote');
  }

  Map<int, NoteModel> get notes => Map.unmodifiable(_notes);

  NoteModel? getNoteBySubtitleIndex(int subtitleIndex) {
    return _notes[subtitleIndex];
  }

  Stream<Map<int, NoteModel>> get notesStream => _notesController.stream;

  // 额外的便捷方法
  RxMap<int, NoteModel> get notesRx => _notes;

  /// 检查指定字幕是否有笔记
  bool hasNote(int subtitleIndex) {
    return _notes.containsKey(subtitleIndex) && _notes[subtitleIndex]?.content?.isNotEmpty == true;
  }

  /// 获取笔记内容
  String getNoteContent(int subtitleIndex) {
    return _notes[subtitleIndex]?.content ?? '';
  }

  /// 清空所有笔记
  void clearNotes() {
    safeExecuteSync(() {
      _notes.clear();
      _notifyNotesChanged();
    }, operationName: 'clearNotes');
  }

  /// 获取笔记数量
  int get noteCount => _notes.length;

  /// 显示添加笔记对话框
  void showAddNoteDialog(int currentPageIndex) {
    if (detailState.localDetailResp?.resourceId == null || detailState.localDetailResp?.resourceType == null) {
      logger("$moduleName: 资源信息未设置，无法显示笔记对话框");
      return;
    }

    if (currentPageIndex < 0 || currentPageIndex >= detailState.videoKit.subtitles.length) {
      logger("$moduleName: 页面索引越界: $currentPageIndex");
      return;
    }

    final subtitle = detailState.videoKit.subtitles[currentPageIndex];
    final subtitleIndex = subtitle.subtitleIndex;
    final note = getNoteBySubtitleIndex(subtitleIndex);

    AddNoteWidget.showAddNoteModal(
      Get.context!,
      subtitle: subtitle,
      noteId: note?.id ?? '',
      noteContent: note?.content ?? '',
      noteSaveCallback: (savedNote) {
        if (savedNote != null) {
          // 笔记模块会自动处理数据更新
          logger("$moduleName: 笔记保存回调，数据已处理");
          _notes[subtitleIndex] = savedNote;
          _notifyNotesChanged();
        }
      },
      resourceType: detailState.localDetailResp!.resourceType!,
      resourceId: detailState.localDetailResp!.resourceId!,
      videoStartTime: subtitle.start.inMilliseconds,
      videoEndTime: subtitle.end.inMilliseconds,
    );
  }

  /// 获取模块状态信息
  @override
  Map<String, dynamic> get stateInfo {
    final baseInfo = super.stateInfo;
    baseInfo.addAll({
      'resourceId': detailState.localDetailResp?.resourceId,
      'resourceType': detailState.localDetailResp?.resourceType,
      'noteCount': noteCount,
      'subtitleCount': detailState.videoKit.subtitles.length,
    });
    return baseInfo;
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {
    await loadNotesFromServer(detailState.localDetailResp?.notes);
  }

}
