import 'package:flutter/material.dart';
import 'package:lsenglish/app/modules/detail/modules/base_module.dart';
import 'package:lsenglish/utils/datacenter_time_manager_extension.dart';
import 'package:lsenglish/utils/log.dart';

class DataModule extends DefaultModule {
  DataModule() : super('DataModule');

  @override
  Future<void> onInitialize() async {
    logger("$moduleName: 初始化数据模块");
  }

  @override
  Future<void> onDispose() async {
    // 销毁时暂停会话
    pauseSession();
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {
    if (error == null) {
      // 数据获取成功后开始会话
      beginSession();
    }
  }

  /// 播放状态变化时的数据上报
  void onPlay(bool playing) {
    logger("$moduleName: 播放状态变化 $playing");
    int index = controller.subtitleModule.getCurrentSubtitleIndex();
    bool isLSMode = detailState.videoKit.openLsMode.value;
    if (playing) {
      DataCenterHelper.resumeSession();
      DataCenterHelper.recordPlayEvent(
        isStart: true,
        index: index,
        isLSMode: isLSMode,
        subtitles: detailState.videoKit.subtitles,
      );
    } else {
      DataCenterHelper.recordPlayEvent(
        isStart: false,
        index: index,
        isLSMode: isLSMode,
        subtitles: detailState.videoKit.subtitles,
      );
    }
  }

  /// 可见性丢失时暂停会话
  @override
  void onVisibilityLost() {
    pauseSession();
  }

  /// 可见性获得时恢复会话
  @override
  void onVisibilityGained() {
    resumeSession();
  }

  /// 界面尺寸变化回调（数据模块可能需要记录横竖屏切换等）
  @override
  void didChangeMetrics() {
    // 数据模块可以在这里记录横竖屏切换等信息
    logger("$moduleName: 界面尺寸变化");
  }

  /// 应用生命周期变化时的会话管理
  void onAppLifecycleStateChanged(AppLifecycleState state) {
    logger("$moduleName: 应用生命周期变化 $state");
    if (state == AppLifecycleState.resumed) {
      resumeSession();
    } else {
      pauseSession();
    }
  }

  /// 开始会话
  void beginSession() {
    logger("$moduleName: 开始数据会话");
    if (detailState.localDetailResp != null) {
      DataCenterHelper.beginSession(
        resourceId: detailState.localDetailResp?.resourceId,
        resourceType: detailState.localDetailResp?.resourceType,
        lsTimes: detailState.localDetailResp?.currentLsTimes,
      );
    }
  }

  /// 暂停会话
  void pauseSession() {
    logger("$moduleName: 暂停数据会话");
    DataCenterHelper.pauseSession();
  }

  /// 恢复会话
  void resumeSession() {
    logger("$moduleName: 恢复数据会话");
    DataCenterHelper.resumeSession();
  }

  /// 上传学习数据
  void uploadLearningData() {
    logger("$moduleName: 上传学习数据");
    if (detailState.localDetailResp == null) {
      logger("$moduleName: localDetailResp为空，无法上传学习数据");
      return;
    }
    DataCenterHelper.uploadLearningData(
      resourceId: detailState.localDetailResp!.resourceId!,
      resourceType: detailState.localDetailResp?.resourceType ?? 2,
      currentLsTimes: detailState.localDetailResp!.currentLsTimes ?? 0,
    );
  }
}
