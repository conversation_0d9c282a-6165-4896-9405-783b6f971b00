import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/modules/base_module.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/model/video_time_interval.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/subtitle.dart';

/// 收藏模块
/// 负责管理视频字幕的收藏功能
class CollectionModule extends DefaultModule {
  // 收藏状态管理
  final RxMap<int, VideoTimeInterval?> _sentenceCollectMap = <int, VideoTimeInterval?>{}.obs;

  CollectionModule() : super('CollectionModule');

  @override
  Future<void> onInitialize() async {
    logger("$moduleName: 初始化收藏模块");
    // 收藏模块初始化相对简单，主要是状态管理
    logger("$moduleName: 收藏模块初始化完成");
  }

  @override
  Future<void> onFetchDetailData(Exception? error) async {}


  @override
  Future<void> onDispose() async {
    logger("$moduleName: 销毁收藏模块");
    _sentenceCollectMap.clear();
    logger("$moduleName: 收藏模块销毁完成");
  }

  /// 切换收藏状态
  void switchCollect(int index) {
    if (detailState.localDetailResp == null) {
      Get.toNamed(Routes.LOGIN);
      return;
    }

    var isRemove = _sentenceCollectMap[index] != null;
    var videoTimeInterval = VideoTimeInterval(
      start: detailState.videoKit.subtitles[index].start.inMilliseconds,
      end: detailState.videoKit.subtitles[index].end.inMilliseconds,
    );

    if (isRemove) {
      _removeCollection(index, videoTimeInterval);
    } else {
      _addCollection(index, videoTimeInterval);
    }
  }

  /// 添加收藏
  void _addCollection(int index, VideoTimeInterval videoTimeInterval) {
    _sentenceCollectMap[index] = videoTimeInterval;
    _sentenceCollectMap.refresh();
    _addLocalSentenceCollect(videoTimeInterval);
  }

  /// 移除收藏
  void _removeCollection(int index, VideoTimeInterval videoTimeInterval) {
    _sentenceCollectMap[index] = null;
    _sentenceCollectMap.refresh();
    _removeLocalSentenceCollect(videoTimeInterval);
  }

  /// 处理字幕与索引的映射关系
  void processRelationSubtitle2IndexMap() {
    logger("$moduleName: 处理字幕与收藏索引映射");

    if (detailState.localDetailResp?.sentenceCollects != null) {
      processIntervals2Map(
        detailState.localDetailResp!.sentenceCollects!,
        _sentenceCollectMap,
        detailState.videoKit.subtitles,
      );
    }

    logger("$moduleName: 字幕与收藏索引映射处理完成，共${_sentenceCollectMap.length}个收藏");
  }

  /// 从服务器加载收藏数据
  void loadCollectionsFromServer(List<VideoTimeInterval>? collections) {
    if (collections == null) {
      logger("$moduleName: 服务器收藏数据为空");
      return;
    }

    logger("$moduleName: 从服务器加载收藏数据，共${collections.length}个收藏");
    processIntervals2Map(collections, _sentenceCollectMap, detailState.videoKit.subtitles);
  }

  /// 添加本地句子收藏到服务器
  Future<void> _addLocalSentenceCollect(VideoTimeInterval videoTimeInterval) async {
    try {
      await Net.getRestClient().addLocalSencenceCollect({
        'resourceId': detailState.localDetailResp!.resourceId,
        'resourceType': detailState.localDetailResp!.resourceType,
        'times': [videoTimeInterval],
      });
      logger("$moduleName: 添加收藏成功");
    } catch (e) {
      logger("$moduleName: 添加收藏失败: $e");
    }
  }

  /// 从服务器移除本地句子收藏
  Future<void> _removeLocalSentenceCollect(VideoTimeInterval videoTimeInterval) async {
    try {
      await Net.getRestClient().removeLocalSencenceCollect({
        'resourceId': detailState.localDetailResp!.resourceId,
        'resourceType': detailState.localDetailResp!.resourceType,
        'times': [videoTimeInterval],
      });
      logger("$moduleName: 移除收藏成功");
    } catch (e) {
      logger("$moduleName: 移除收藏失败: $e");
    }
  }

  /// 检查指定索引是否已收藏
  bool isCollected(int index) {
    return _sentenceCollectMap[index] != null;
  }

  /// 获取收藏数量
  int get collectionCount => _sentenceCollectMap.length;

  /// 获取收藏状态映射
  RxMap<int, VideoTimeInterval?> get sentenceCollectMap => _sentenceCollectMap;

  @override
  Map<String, dynamic> get stateInfo => {
        'moduleName': moduleName,
        'collectionCount': collectionCount,
        'collections': _sentenceCollectMap.entries
            .where((entry) => entry.value != null)
            .map((entry) => {
                  'index': entry.key,
                  'start': entry.value!.start,
                  'end': entry.value!.end,
                })
            .toList(),
      };
}
