import 'package:flutter/material.dart';

/// 只播放有字幕行的图标组件
/// 支持激活/未激活两种状态，颜色可自定义
class OnlyLinesIcon extends StatelessWidget {
  final bool isActive;
  final Color? color;
  final double size;

  const OnlyLinesIcon({
    Key? key,
    required this.isActive,
    this.color,
    this.size = 24.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Colors.white;
    
    return CustomPaint(
      size: Size(size, size),
      painter: _OnlyLinesIconPainter(
        isActive: isActive,
        color: iconColor,
      ),
    );
  }
}

class _OnlyLinesIconPainter extends CustomPainter {
  final bool isActive;
  final Color color;

  _OnlyLinesIconPainter({
    required this.isActive,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final fillPaint = Paint()
      ..style = PaintingStyle.fill;

    final scaleX = size.width / 24.0;
    final scaleY = size.height / 24.0;

    // 绘制三个圆圈（底部的点）
    final circleRadius = 1.0 * scaleX;
    final circleY = 19.0 * scaleY;
    
    // 左圆圈
    paint.color = isActive ? Colors.white : Color(0xFF625B71);
    canvas.drawCircle(Offset(4.0 * scaleX, circleY), circleRadius, paint);
    
    // 中圆圈  
    canvas.drawCircle(Offset(12.0 * scaleX, circleY), circleRadius, paint);
    
    // 右圆圈
    canvas.drawCircle(Offset(20.0 * scaleX, circleY), circleRadius, paint);

    // 绘制填充的圆点（激活状态的实心点）
    if (isActive) {
      fillPaint.color = Colors.white;
    } else {
      fillPaint.color = Color(0xFF625B71);
    }
    
    // 左填充圆
    canvas.drawCircle(Offset(4.0 * scaleX, 18.0 * scaleY), circleRadius, fillPaint);
    
    // 右填充圆
    canvas.drawCircle(Offset(20.0 * scaleX, 18.0 * scaleY), circleRadius, fillPaint);

    // 绘制箭头路径
    final arrowPath = Path();
    arrowPath.moveTo(20.7764 * scaleX, 10.0 * scaleY);
    arrowPath.cubicTo(
      20.7764 * scaleX, 10.0 * scaleY,
      20.655 * scaleX, 9.15076 * scaleY,
      17.1403 * scaleX, 5.63604 * scaleY,
    );
    arrowPath.cubicTo(
      13.6256 * scaleX, 2.12132 * scaleY,
      7.92713 * scaleX, 2.12132 * scaleY,
      4.41241 * scaleX, 5.63604 * scaleY,
    );
    arrowPath.cubicTo(
      3.16713 * scaleX, 6.88131 * scaleY,
      2.36306 * scaleX, 8.40072 * scaleY,
      2.00019 * scaleX, 10.0 * scaleY,
    );
    
    // 箭头的垂直线
    arrowPath.moveTo(20.7764 * scaleX, 10.0 * scaleY);
    arrowPath.lineTo(20.7764 * scaleX, 4.0 * scaleY);
    
    // 箭头的水平线
    arrowPath.moveTo(20.7764 * scaleX, 10.0 * scaleY);
    arrowPath.lineTo(14.7764 * scaleX, 10.0 * scaleY);

    paint.color = isActive ? Color(0xFF32D74B) : Color(0xFF625B71);
    canvas.drawPath(arrowPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _OnlyLinesIconPainter ||
        oldDelegate.isActive != isActive ||
        oldDelegate.color != color;
  }
}
