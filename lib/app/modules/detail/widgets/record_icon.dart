import 'dart:ui';
import 'package:flutter/material.dart';

/// 录音图标组件
/// 支持在录音状态和停止录音状态之间切换
class RecordIcon extends StatefulWidget {
  /// 图标颜色
  final Color? color;
  
  /// 图标大小
  final double size;
  
  /// 动画持续时间
  final Duration animationDuration;
  
  /// 录音状态变化回调
  final Function(bool isRecording)? onRecordingStateChanged;
  
  /// 外部录音状态（可选）
  /// 如果提供，将根据此状态显示UI，而不是内部状态
  final bool? isRecording;
  
  /// 点击回调
  final VoidCallback? onTap;

  const RecordIcon({
    super.key,
    this.color,
    this.size = 72.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.onRecordingStateChanged,
    this.isRecording,
    this.onTap,
  });

  // 静态列表来跟踪所有实例
  static final List<_RecordIconState> _instances = [];

  // 静态方法来重置所有实例的状态
  static void resetAllInstances() {
    for (final instance in _instances) {
      instance.resetRecordingState();
    }
  }

  @override
  State<RecordIcon> createState() => _RecordIconState();
}

class _RecordIconState extends State<RecordIcon> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _internalRecordingState = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    // 初始状态为未录音（圆形）
    _animationController.value = 0.0;
    
    // 注册实例
    RecordIcon._instances.add(this);
  }

  @override
  void didUpdateWidget(RecordIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果外部状态发生变化，更新内部状态和动画
    if (oldWidget.isRecording != widget.isRecording && widget.isRecording != null) {
      _updateRecordingState(widget.isRecording!);
    }
  }

  /// 获取当前录音状态
  bool get _currentRecordingState {
    return widget.isRecording ?? _internalRecordingState;
  }

  void _toggleRecording() {
    if (widget.onTap != null) {
      // 如果有外部点击回调，直接调用
      widget.onTap!();
    } else if (widget.isRecording != null) {
      // 如果外部状态被控制，只触发回调
      widget.onRecordingStateChanged?.call(!_currentRecordingState);
    } else {
      // 否则更新内部状态
      _updateRecordingState(!_currentRecordingState);
      widget.onRecordingStateChanged?.call(_currentRecordingState);
    }
  }

  void _updateRecordingState(bool isRecording) {
    if (widget.isRecording != null) {
      // 外部状态被控制，只更新内部状态用于动画
      _internalRecordingState = isRecording;
    } else {
      // 内部状态被控制
      _internalRecordingState = isRecording;
    }
    
    if (_internalRecordingState) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  /// 重置录音状态（外部调用）
  void resetRecordingState() {
    _updateRecordingState(false);
  }

  @override
  void dispose() {
    // 注销实例
    RecordIcon._instances.remove(this);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleRecording,
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return CustomPaint(
              painter: _RecordIconPainter(
                iconColor: widget.color ?? const Color(0xFFF24822),
                strokeColor: Colors.black.withOpacity(0.3),
                animationValue: _animation.value,
              ),
            );
          },
        ),
      ),
    );
  }
}

/// 录音图标绘制器
class _RecordIconPainter extends CustomPainter {
  final Color iconColor;
  final Color strokeColor;
  final double animationValue;

  _RecordIconPainter({
    required this.iconColor,
    required this.strokeColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = iconColor;

    final strokePaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = strokeColor
      ..strokeWidth = size.width * 0.044; // 3.17647 / 72

    // 绘制外圈边框（始终不变）
    final borderRect = Rect.fromLTWH(
      size.width * 0.022, // 1.58824 / 72
      size.width * 0.022,
      size.width * 0.956, // 68.8235 / 72
      size.height * 0.956,
    );
    final borderRadius = size.width * 0.478; // 34.4118 / 72
    canvas.drawRRect(
      RRect.fromRectAndRadius(borderRect, Radius.circular(borderRadius)),
      strokePaint,
    );

    // 圆形参数（录音状态 - isRecording = false）
    final circleLeft = size.width * 0.103; // 7.41016 / 72
    final circleTop = size.width * 0.103; // 7.41211 / 72
    final circleWidth = size.width * 0.794; // 57.1765 / 72
    final circleHeight = size.height * 0.794;
    final circleRadius = size.width * 0.397; // 28.5882 / 72

    // 矩形参数（停止录音状态 - isRecording = true）
    final rectLeft = size.width * 0.292; // 21 / 72
    final rectTop = size.width * 0.292;
    final rectWidth = size.width * 0.417; // 30 / 72
    final rectHeight = size.height * 0.417;
    final rectRadius = size.width * 0.083; // 6 / 72

    // 插值计算 - 注意：isRecording为true时显示矩形，为false时显示圆形
    // 所以这里不需要反转animationValue
    final currentLeft = lerpDouble(circleLeft, rectLeft, animationValue)!;
    final currentTop = lerpDouble(circleTop, rectTop, animationValue)!;
    final currentWidth = lerpDouble(circleWidth, rectWidth, animationValue)!;
    final currentHeight = lerpDouble(circleHeight, rectHeight, animationValue)!;
    final currentRadius = lerpDouble(circleRadius, rectRadius, animationValue)!;

    // 绘制内部形状
    final innerRect = Rect.fromLTWH(currentLeft, currentTop, currentWidth, currentHeight);
    canvas.drawRRect(
      RRect.fromRectAndRadius(innerRect, Radius.circular(currentRadius)),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is _RecordIconPainter) {
      return oldDelegate.iconColor != iconColor ||
             oldDelegate.strokeColor != strokeColor ||
             oldDelegate.animationValue != animationValue;
    }
    return true;
  }
} 