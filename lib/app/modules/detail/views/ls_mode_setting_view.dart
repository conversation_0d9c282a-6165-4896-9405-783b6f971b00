import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/widgets/base_dialog.dart';
import 'package:lsenglish/widgets/my_expansion_panel.dart' as my_expansion_panel;

class LsSettingModel {
  String title;
  List<String> children;
  List<int> defaultChecks;
  bool canEdit;
  LsSettingModel({required this.title, required this.children, required this.defaultChecks, required this.canEdit});
}

class LsModeSettingWidget extends StatefulWidget {
  const LsModeSettingWidget({super.key});

  @override
  State<LsModeSettingWidget> createState() => _LsModeSettingWidgetState();
}

class _LsModeSettingWidgetState extends State<LsModeSettingWidget> {
  int? _expandedIndex = 0;
  var lsSettingModelList = <LsSettingModel>[];
  var openLsMode = true;

  @override
  void initState() {
    super.initState();
    lsSettingModelList.add(LsSettingModel(
      title: '初学模式',
      children: ['自动-开始录制', '自动-结束录制', '自动-播放录制', '自动-播放原声'],
      defaultChecks: [2],
      canEdit: false,
    ));
    lsSettingModelList.add(LsSettingModel(
      title: '熟练模式',
      children: ['自动-开始录制', '自动-结束录制（时长：1.0 倍', '自动-播放录制', '自动-播放原声'],
      defaultChecks: [0, 1, 2],
      canEdit: false,
    ));
    lsSettingModelList.add(LsSettingModel(
      title: '自定义模式',
      children: ['自动-开始录制', '自动-结束录制（时长：1.0 倍', '自动-播放录制', '自动-播放原声'],
      defaultChecks: [0, 1, 2],
      canEdit: true,
    ));
  }

  void save(int index) {
    if (index == 0) {
      Config().playerConfig.autoRecord = false;
      Config().playerConfig.autoStopRecord = false;
      Config().playerConfig.autoPlayRecordWhenRecordEnd = true;
      Net.getRestClient().updatePlayerConfig({
        'autoRecord': Config().playerConfig.autoRecord,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.whs),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'LS模式',
                  style: TextStyle(fontSize: 32.whs, fontWeight: FontWeight.w600),
                ),
                CupertinoSwitch(
                  value: openLsMode,
                  activeColor: Get.theme.primaryColor,
                  onChanged: (value) {
                    setState(() {
                      openLsMode = !openLsMode;
                    });
                  },
                ),
              ],
            ),
            Gap(8.whs),
            Text(
              'LS 练习法介绍及使用方法',
              style: TextStyle(
                color: Get.theme.primaryColor,
                fontSize: 14.whs,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.underline,
                decorationColor: Get.theme.primaryColor,
              ),
            ),
            Gap(30.whs),
            Expanded(
              child: SingleChildScrollView(
                child: my_expansion_panel.ExpansionPanelList(
                  elevation: 0,
                  materialGapSize: 0,
                  dividerColor: Colors.transparent,
                  expandedHeaderPadding: EdgeInsets.zero,
                  children: lsSettingModelList.asMap().entries.map((entry) {
                    int index = entry.key;
                    var item = entry.value;
                    return my_expansion_panel.ExpansionPanel(
                      backgroundColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      canTapOnHeader: false,
                      headerBuilder: (BuildContext context, bool isExpanded) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _expandedIndex = index;
                            });
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 4.whs),
                            child: Container(
                              padding: EdgeInsets.all(16.whs),
                              decoration: ShapeDecoration(
                                color: const Color(0x338477FF),
                                shape: RoundedRectangleBorder(
                                  side: _expandedIndex == index
                                      ? BorderSide(width: 2, color: Get.theme.primaryColor)
                                      : const BorderSide(width: 2, color: Color(0xff2C2C2E)),
                                  borderRadius: BorderRadius.circular(14),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    item.title,
                                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                  ),
                                  Visibility(visible: _expandedIndex == index, child: Icon(Icons.check_rounded, size: 24.whs)),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      body: Column(
                        children: item.children.asMap().entries.map<Widget>((entry) {
                          int index = entry.key;
                          var subItem = entry.value;
                          return GestureDetector(
                            onTap: () {
                              if (item.canEdit) {
                                setState(() {
                                  if (item.defaultChecks.contains(index)) {
                                    item.defaultChecks.remove(index);
                                  } else {
                                    item.defaultChecks.add(index);
                                  }
                                });
                              } else {
                                Get.dialog(const CommonDialog(
                                  title: "你想自定义选项？",
                                  subtitle: "初学&熟练模式下不可更改，你可前往自定义修改",
                                  options: ["是的，我要自定义"],
                                  callbacks: [],
                                ));
                              }
                            },
                            child: Container(
                              color: Colors.transparent,
                              child: Padding(
                                padding: EdgeInsets.all(16.whs),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      subItem,
                                      style: TextStyle(
                                        fontSize: 16.whs,
                                        fontWeight: FontWeight.w400,
                                        color: item.defaultChecks.contains(index)
                                            ? Theme.of(context).textTheme.displayLarge?.color
                                            : item.canEdit
                                                ? Theme.of(context).textTheme.displayLarge?.color
                                                : Theme.of(context).textTheme.displayLarge?.color?.withOpacity(0.3),
                                      ),
                                    ),
                                    item.defaultChecks.contains(index)
                                        ? Icon(Icons.check_rounded, size: 24.whs)
                                        : Icon(
                                            item.canEdit ? Icons.check_rounded : Icons.remove,
                                            size: 24.whs,
                                            color: item.canEdit
                                                ? (item.defaultChecks.contains(index)
                                                    ? Get.theme.iconTheme.color
                                                    : Get.theme.iconTheme.color?.withOpacity(0.3))
                                                : Get.theme.iconTheme.color?.withOpacity(0.3),
                                          ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      isExpanded: _expandedIndex == index,
                    );
                  }).toList(),
                ),
              ),
            ),
            Container(
              width: double.infinity,
              height: 50.whs,
              padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 8.whs),
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                color: Get.theme.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(14.whs),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '确认并返回',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16.whs,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Gap(36.whs),
          ],
        ),
      ),
    );
  }
}
