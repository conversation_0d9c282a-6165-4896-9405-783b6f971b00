import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:super_tooltip/super_tooltip.dart';

typedef TitleMoreCallback = void Function(int);

class TitleMoreWidget extends StatefulWidget {
  final List<String> titles;
  final List<String> icons;
  final Color? color;
  final TitleMoreCallback titleMoreCallback;
  const TitleMoreWidget({
    super.key,
    required this.titles,
    required this.icons,
    required this.titleMoreCallback,
    this.color,
  });

  @override
  State<TitleMoreWidget> createState() => _TitleMoreWidgetState();
}

class _TitleMoreWidgetState extends State<TitleMoreWidget> {
  final SuperTooltipController _superTooltipController = SuperTooltipController();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: SuperTooltip(
        barrierColor: Colors.transparent,
        hideTooltipOnBarrierTap: true,
        controller: _superTooltipController,
        hideTooltipOnTap: true,
        backgroundColor: Colors.black,
        borderRadius: 16.whs,
        hasShadow: false,
        overlayDimensions: EdgeInsets.zero,
        bubbleDimensions: EdgeInsets.symmetric(horizontal: 16.whs),
        arrowLength: 0,
        arrowTipDistance: 10.whs,
        content: Container(
          color: Colors.black,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              widget.titles.length,
              (rowIndex) => GestureDetector(
                onTap: () {
                  _superTooltipController.hideTooltip();
                  widget.titleMoreCallback(rowIndex);
                },
                child: Container(
                  padding: EdgeInsets.only(top: rowIndex == 0 ? 16.whs : 8.whs, bottom: rowIndex == widget.titles.length - 1 ? 16.whs : 8.whs),
                  color: Colors.transparent,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.titles[rowIndex],
                        style: TextStyle(color: Colors.white, fontSize: 17.whs),
                      ),
                      Gap(16.whs),
                      ImageLoader(
                        widget.icons[rowIndex],
                        color: Get.isDarkMode ? Colors.white : Colors.white,
                        size: 24.whs,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 16.whs, right: 8.whs, top: 8.whs, bottom: 8.whs),
          child: ImageLoader(
            R.more,
            size: 24.whs,
            color: widget.color ?? Get.theme.iconTheme.color,
          ),
        ),
      ),
    );
  }
}
