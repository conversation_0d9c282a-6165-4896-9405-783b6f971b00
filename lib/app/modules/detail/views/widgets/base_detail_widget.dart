import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/video/media_player.dart';
import '../../controllers/detail_controller.dart';
import '../../interfaces/detail_state.dart';
import '../../modules/collection_module.dart';
import '../../modules/data_module.dart';
import '../../modules/note_module.dart';
import '../../modules/player_module.dart';
import '../../modules/playback_module.dart';
import '../../modules/speech_evaluation_module.dart';
import '../../modules/subtitle_module.dart';

/// Detail页面基础Widget
/// 提供统一的controller、state和module访问
/// 所有Detail相关的组件都应该继承此类
abstract class BaseDetailWidget extends GetView<DetailController> {
  const BaseDetailWidget({Key? key}) : super(key: key);

  // ==================== 快捷访问器 ====================

  /// 获取Detail状态
  DetailState get detailState => controller.detailState;

  // ==================== 模块访问器 ====================

  /// 字幕模块
  SubtitleModule get subtitleModule => controller.subtitleModule;

  /// 语音评测模块
  SpeechEvaluationModule get speechModule => controller.speechEvaluationModule;

  /// 笔记模块
  NoteModule get noteModule => controller.noteModule;

  /// 收藏模块
  CollectionModule get collectionModule => controller.collectionModule;

  /// 播放控制模块
  PlaybackModule get playbackModule => controller.playbackModule;

  /// 数据模块
  DataModule get dataModule => controller.dataModule;

  /// 播放器模块
  PlayerModule get playerModule => controller.playerModule;

  // ==================== 常用状态访问器 ====================

  /// 视频Kit
  MediaPlayer get videoKit => detailState.videoKit;

  /// 字幕列表
  List<Subtitle> get subtitles => detailState.videoKit.subtitles;

  /// 当前页面索引
  int get currentPage => detailState.currentPage.value;

  /// 是否横屏
  bool get isLandscape => detailState.isLandscape.value;

  /// 是否加载中
  bool get isLoading => detailState.loadingSubtitle.value;

  /// 是否LS模式
  bool get isLsMode => detailState.videoKit.openLsMode.value;

  // ==================== 常用操作方法 ====================

  /// 跳转到指定字幕
  void jumpToSubtitle(int index) {
    controller.onSubTitleListClick(index);
  }

  /// 切换字幕占位符
  void toggleSubtitlePlaceholder() {
    subtitleModule.toggleSubtitlePlaceholder();
  }

  /// 开始录音
  void startRecording() {
    speechModule.startRecording();
  }

  /// 停止录音
  void stopRecording() {
    speechModule.stopRecording();
  }

  /// 播放录音
  void playRecord() {
    controller.playRecord();
  }

  /// 切换收藏状态
  void toggleCollect() {
    controller.switchCollect();
  }

  /// 显示笔记对话框
  void showNoteDialog([int? subtitleIndex]) {
    noteModule.showAddNoteDialog(subtitleIndex ?? currentPage);
  }

  // ==================== 工具方法 ====================

  /// 安全获取当前字幕
  Subtitle? get currentSubtitle {
    if (subtitles.isEmpty || currentPage >= subtitles.length || currentPage < 0) {
      return null;
    }
    return subtitles[currentPage];
  }

  /// 检查是否有字幕
  bool get hasSubtitles => subtitles.isNotEmpty;

  /// 检查当前字幕是否已收藏
  bool get isCurrentSubtitleCollected {
    return collectionModule.sentenceCollectMap[currentPage] != null;
  }

  /// 获取当前字幕的语音评测结果
  get currentSpeechEvaluation {
    return speechModule.speechEvaluationMap[currentPage];
  }

  /// 检查是否正在录音
  bool get isRecording => speechModule.recordingInLsMode.value;

  /// 检查字幕占位符是否显示
  bool get isSubtitlePlaceholderVisible => subtitleModule.showSubtitlePlaceholder.value;

  // ==================== 播放控制方法 ====================

  /// 跳转到上一句
  void jumpPrevious() => playbackModule.jumpPreSubtitle();

  /// 跳转到下一句
  void jumpNext() => playbackModule.jumpNextSubtitle();
}

/// 针对特定功能的基础Widget（可选扩展）

/// 字幕相关Widget基类
abstract class BaseSubtitleWidget extends BaseDetailWidget {
  const BaseSubtitleWidget({Key? key}) : super(key: key);

  /// 搜索字幕
  void searchSubtitle() => subtitleModule.searchSubtitle();

  /// 获取自带字幕
  void getVideoSubtitle() => subtitleModule.getSubtitleByVideo();

  /// 编辑字幕
  void editSubtitle() => subtitleModule.editSubtitle();

  /// AI生成字幕
  void generateAISubtitle() => controller.goAIGenSubtitle();
}

/// 语音相关Widget基类
abstract class BaseSpeechWidget extends BaseDetailWidget {
  const BaseSpeechWidget({Key? key}) : super(key: key);

  /// 获取当前语音评分
  int? get currentScore {
    final evalResult = currentSpeechEvaluation?.evalResult;
    return evalResult != null ? speechModule.recognitionService.getScore(evalResult) : null;
  }

  /// 检查是否有录音
  bool get hasRecording => currentSpeechEvaluation != null;
}

/// 播放控制相关Widget基类
abstract class BasePlayerWidget extends BaseDetailWidget {
  const BasePlayerWidget({Key? key}) : super(key: key);

  /// 切换播放速度
  void switchSpeed() => playbackModule.switchSpeed();

  /// 返回（横屏时）
  void backFromFullscreen() => playbackModule.backWhenFullscreen();

  /// 获取播放器菜单项
  get playerMenuItems => controller.playbackModule.playerMenuItems;
}
