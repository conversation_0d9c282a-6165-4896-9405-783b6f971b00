import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/video/video_controls_material.dart';
import 'package:lsenglish/widgets/split_english.dart';
import 'package:media_kit_video/media_kit_video.dart';
import '../../controllers/detail_controller.dart';
import '../landscape.dart';

const videoRatio = 9 / 16;

/// 视频播放器组件
/// 职责：处理视频播放相关的UI显示
class VideoPlayerWidget extends GetView<DetailController> {
  final bool isLandscape;

  const VideoPlayerWidget({
    Key? key,
    this.isLandscape = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isLandscape ? _buildLandscapeVideo() : _buildPortraitVideo();
  }

  /// 竖屏视频播放器
  Widget _buildPortraitVideo() {
    return GestureDetector(
      onTap: () {
        controller.detailState.videoKit.toggleFullscreen();
      },
      child: AspectRatio(
        aspectRatio: (1 / videoRatio),
        child: Obx(() {
          if (!controller.detailState.isVideoControllerInitialized.value || 
              controller.detailState.videoController == null) {
            return const Center(child: CircularProgressIndicator());
          }
          
          final videoController = controller.detailState.videoController!;
          return MyVideoControlsTheme(
            normal: kDefaultMyVideoControlsThemeData,
            local: controller.detailState.isLocalVideo 
                ? kDefaultLocalVideoControlsThemeData 
                : null,
            videoControlsCallbacks: controller.detailState.videoControlsCallbacks,
            child: Video(
              controller: videoController,
              fit: BoxFit.cover,
              controls: NoVideoControls,
              pauseUponEnteringBackgroundMode: true,
              resumeUponEnteringForegroundMode: controller.detailState.videoKit.onlyPlayLines.value,
            ),
          );
        }),
      ),
    );
  }

  /// 横屏视频播放器
  Widget _buildLandscapeVideo() {
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(30)),
      child: Stack(
        children: [
          Obx(() {
            if (!controller.detailState.isVideoControllerInitialized.value || 
                controller.detailState.videoController == null) {
              return const Center(child: CircularProgressIndicator());
            }
            
            final videoController = controller.detailState.videoController!;
            return MyVideoControlsTheme(
              normal: kDefaultMyVideoControlsThemeData,
              local: controller.detailState.isLocalVideo 
                  ? kDefaultLocalVideoControlsThemeData 
                  : null,
              child: Video(
                controller: videoController,
                fit: BoxFit.cover,
                controls: MyPhoneVideoControls,
                pauseUponEnteringBackgroundMode: true,
                resumeUponEnteringForegroundMode: controller.detailState.videoKit.onlyPlayLines.value,
              ),
            );
          }),
          _buildTapJumpWidget(),
          _buildLandscapeSubtitle(),
          _buildLandscapeToast(),
        ],
      ),
    );
  }

  /// 横屏模式点击跳转字幕
  Widget _buildTapJumpWidget() {
    return Positioned.fill(
      child: TapJumpSubtitleWidget(
        tapJumpSubtitleCallback: (bool isForward) {
          if (isForward) {
            controller.playbackModule.jumpNextSubtitle();
          } else {
            controller.playbackModule.jumpPreSubtitle();
          }
        },
      ),
    );
  }

  /// 横屏模式字幕显示
  Widget _buildLandscapeSubtitle() {
    return Obx(() => Visibility(
      visible: controller.detailState.videoKit.subtitles.isNotEmpty && 
               !controller.subtitleModule.showSubtitlePlaceholder.value,
      child: Positioned(
        left: 0,
        right: 0,
        bottom: 0,
        child: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: Get.height * 0.5),
          child: ClipRRect(
            child: Stack(
              children: [
                SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                    child: Obx(() => Center(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.whs, 
                          vertical: 4.whs
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(4.whs),
                        ),
                        child: SplitEnglishWidget(
                          isLandscape: true,
                          showPlaceholder: false,
                          centerText: true,
                          subtitle: controller.detailState.videoKit
                              .subtitles[controller.detailState.videoKit.currentSubtitleIndex.value],
                          subtitleMode: controller.detailState.videoKit.currentSubtitleMode.value,
                          evalResult: controller.speechEvaluationModule
                              .speechEvaluationMap[controller.detailState.videoKit.currentSubtitleIndex.value]?.evalResult,
                        ),
                      ),
                    )),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ));
  }

  /// 横屏模式Toast提示
  Widget _buildLandscapeToast() {
    return Positioned(
      top: 6.whs,
      left: 0,
      right: 0,
      child: Obx(() => AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        opacity: controller.detailState.showCurrentLandScapeToast.value ? 1.0 : 0.0,
        child: IntrinsicWidth(
          child: Center(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.all(Radius.circular(8.whs)),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                controller.detailState.currentToast.value,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      )),
    );
  }
}