import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';
import 'package:lsenglish/widgets/nest_pageview.dart';
import 'package:lsenglish/widgets/split_english.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'base_detail_widget.dart';

/// 字幕内容组件 - 使用BaseDetailWidget版本
/// 职责：处理字幕相关的UI显示，包括LS模式和仅听模式
class SubtitleContentWidget extends BaseSubtitleWidget {
  const SubtitleContentWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => Stack(
          children: [
            // 使用基类提供的便捷访问器
            isLsMode ? _buildLsModeSubtitles() : _buildOnlyListenSubtitles(),
            _buildLoadingSubtitle(),
          ],
        ));
  }

  /// 加载中状态
  Widget _buildLoadingSubtitle() {
    return Obx(() => isLoading
        ? const Positioned.fill(child: Center(child: CircularProgressIndicator()))
        : !hasSubtitles
            ? _buildNoSubtitle()
            : const SizedBox.shrink());
  }

  /// 无字幕状态
  Widget _buildNoSubtitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text("暂无字幕,请导入"),
        const Gap(32),
        Column(
          children: [
            FilledButton(
              onPressed: searchSubtitle, // 使用基类方法
              child: const Text("去导入"),
            ),
            const Gap(20),
            FilledButton(
              onPressed: generateAISubtitle, // 使用基类方法
              child: const Text("AI生成字幕"),
            ),
            const Gap(20),
            FilledButton(
              onPressed: getVideoSubtitle, // 使用基类方法
              child: const Text("获取自带字幕"),
            ),
          ],
        ),
      ],
    );
  }

  /// LS模式字幕显示
  Widget _buildLsModeSubtitles() {
    return Obx(() {
      if (!detailState.isPageControllerInitialized.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return NestPageView(
          pageController: detailState.pageController,
          onUserScrollEnd: () => controller.onUserScrollEnd(),
          onUserScrollStart: () => controller.onUserScrollStart(),
          onPageScrollDirection: (isForward) => controller.onPageScrollDirection(isForward),
          builder: (BuildContext context, PageController pageController, ScrollController scrollController, int pageIndex) {
            return Obx(() => PageView.builder(
                  onPageChanged: (value) => controller.onPageChanged(value),
                  controller: pageController,
                  scrollDirection: Axis.horizontal,
                  itemCount: subtitles.length, // 使用基类访问器
                  itemBuilder: (BuildContext context, int index) {
                    return GestureDetector(
                      onTap: () => playbackModule.lsContainerClick(index), // 使用基类访问器
                      onDoubleTap: toggleSubtitlePlaceholder, // 使用基类方法
                      child: SingleChildScrollView(
                        physics: const NeverScrollableScrollPhysics(),
                        controller: scrollController,
                        child: Container(
                          key: PageStorageKey(index),
                          child: _buildLsPageChild(context, index),
                        ),
                      ),
                    );
                  },
                ));
          });
    });
  }

  /// LS模式单页内容
  Widget _buildLsPageChild(BuildContext context, int index) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${(subtitles[index].subtitleIndex + 1).toString()}/${subtitles.length}",
            style: Get.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          const Gap(8),
          Container(
            color: Colors.transparent,
            child: Obx(
              () => SplitEnglishWidget(
                showPlaceholder: isSubtitlePlaceholderVisible, // 使用基类访问器
                subtitle: subtitles[index], // 使用基类访问器
                subtitleMode: videoKit.currentSubtitleMode.value, // 使用基类访问器
                evalResult: speechModule.speechEvaluationMap[index]?.evalResult, // 使用基类访问器
                onPlaceholderTap: toggleSubtitlePlaceholder, // 使用基类方法
              ),
            ),
          ),
          // 笔记显示
          _buildNoteSection(index),
        ],
      ),
    );
  }

  /// 笔记区域
  Widget _buildNoteSection(int index) {
    return Obx(() => Visibility(
          visible: noteModule.notesRx.containsKey(subtitles[index].subtitleIndex) &&
              noteModule.notesRx[subtitles[index].subtitleIndex]?.content?.isNotEmpty == true,
          child: IntrinsicHeight(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200),
              child: GestureDetector(
                onTap: () => showNoteDialog(index), // 使用基类方法
                child: Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      color: isSubtitlePlaceholderVisible // 使用基类访问器
                          ? const Color(0xFFF6F6F6).withValues(alpha: 0.5)
                          : const Color(0xFFF6F6F6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Container(
                          width: 4,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            color: isSubtitlePlaceholderVisible // 使用基类访问器
                                ? Get.theme.primaryColor.withValues(alpha: 0.5)
                                : Get.theme.primaryColor,
                          ),
                        ),
                        const Gap(8),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(6),
                            child: Text(
                              !isSubtitlePlaceholderVisible // 使用基类访问器
                                  ? noteModule.notesRx[subtitles[index].subtitleIndex]?.content ?? ""
                                  : "\n\n\n",
                              overflow: TextOverflow.fade,
                              softWrap: true,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ));
  }

  /// 仅听模式字幕列表
  Widget _buildOnlyListenSubtitles() {
    return Obx(
      () => Listener(
        onPointerDown: (event) => controller.onPointerDown(),
        onPointerUp: (event) => controller.onPointerUp(),
        child: ListView.builder(
            controller: detailState.itemScrollController,
            itemCount: subtitles.length, // 使用基类访问器
            itemBuilder: (BuildContext context, int index) {
              return AutoScrollTag(
                key: ValueKey(index),
                controller: detailState.itemScrollController,
                index: index,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: 20,
                    left: 16,
                    right: 16,
                    bottom: index == subtitles.length - 1 ? 16 : 8,
                  ),
                  child: GestureDetector(
                    onTap: () => jumpToSubtitle(index), // 使用基类方法
                    child: Obx(
                      () => SplitEnglishWidget(
                        subtitle: subtitles[index], // 使用基类访问器
                        subtitleMode: videoKit.currentSubtitleMode.value, // 使用基类访问器
                        evalResult: speechModule.speechEvaluationMap[index]?.evalResult, // 使用基类访问器
                        onPlaceholderTap: () {
                          subtitleModule.toggleSubtitlePlaceholder();
                        },
                        showPlaceholder: isSubtitlePlaceholderVisible, // 使用基类访问器
                      ),
                    ),
                  ),
                ),
              );
            }),
      ),
    );
  }
}
