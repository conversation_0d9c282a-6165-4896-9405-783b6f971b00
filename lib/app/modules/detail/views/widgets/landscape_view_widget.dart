import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import '../../controllers/detail_controller.dart';
import 'video_player_widget.dart';
import 'voice_control_widget.dart';
import 'player_control_widget.dart';

/// 横屏视图组件
/// 职责：处理横屏模式下的完整UI布局
class LandscapeViewWidget extends GetView<DetailController> {
  const LandscapeViewWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      child: Row(
        children: [
          // 视频区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Stack(
                children: [
                  const VideoPlayerWidget(),
                  Positioned(child: _buildTitlebar()),
                ],
              ),
            ),
          ),
          // 控制区域
          Row(
            children: [
              const VoiceControlWidget(isDark: true),
              SizedBox(
                height: Get.height,
                child: const PlayerControlWidget(),
              ),
              const Gap(16),
            ],
          ),
        ],
      ),
    );
  }

  /// 横屏标题栏
  Widget _buildTitlebar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(onTap: () => controller.playbackModule.backWhenFullscreen(), child: ImageLoader(R.fullscreen_close)),
          const Gap(8),
          const Spacer(),
        ],
      ),
    );
  }
}
