import 'package:flutter/material.dart';
import 'package:lsenglish/utils/size_extension.dart';

typedef TapJumpSubtitleCallback = void Function(bool isForward);

class TapJumpSubtitleWidget extends StatefulWidget {
  final TapJumpSubtitleCallback tapJumpSubtitleCallback;
  const TapJumpSubtitleWidget({super.key, required this.tapJumpSubtitleCallback});

  @override
  State<TapJumpSubtitleWidget> createState() => _TapJumpSubtitleWidgetState();
}

class _TapJumpSubtitleWidgetState extends State<TapJumpSubtitleWidget> {
  var showNext = false;
  var showPre = false;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                showPre = true;
                widget.tapJumpSubtitleCallback(false);
              });
            },
            child: Container(
              color: Colors.transparent,
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: showPre ? 1.0 : 0.0),
                duration: Durations.medium3,
                onEnd: () {
                  setState(() {
                    showPre = false;
                  });
                },
                builder: (context, opacity, child) {
                  return Opacity(
                    opacity: opacity,
                    child: Icon(Icons.arrow_back, size: 50.whs, color: Colors.white),
                  );
                },
              ),
            ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                showNext = true;
                widget.tapJumpSubtitleCallback(true);
              });
            },
            child: Container(
              color: Colors.transparent,
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: showNext ? 1.0 : 0.0),
                duration: Durations.medium3,
                onEnd: () {
                  setState(() {
                    showNext = false;
                  });
                },
                builder: (context, opacity, child) {
                  return Opacity(
                    opacity: opacity,
                    child: Icon(Icons.arrow_forward, size: 50.whs, color: Colors.white),
                  );
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}
