import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';
import 'package:super_tooltip/super_tooltip.dart';
import 'package:lsenglish/model/note_model.dart';

//0无效 1收藏 2保存笔记
typedef NoteSaveCallback = void Function(NoteModel? note);

// 带确认功能的关闭按钮组件
class CloseButtonWithConfirm extends StatelessWidget {
  final SuperTooltipController tooltipController;
  final VoidCallback onConfirmClose;

  const CloseButtonWithConfirm({
    super.key,
    required this.tooltipController,
    required this.onConfirmClose,
  });

  void _showCloseConfirmMenu() {
    tooltipController.showTooltip();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showCloseConfirmMenu,
      child: SuperTooltip(
        popupDirection: TooltipDirection.down,
        controller: tooltipController,
        backgroundColor: Colors.transparent,
        borderColor: Colors.transparent,
        hasShadow: false,
        bubbleDimensions: EdgeInsets.symmetric(horizontal: 0.whs, vertical: 20.whs),
        arrowLength: 0,
        content: IntrinsicWidth(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ElevatedButton(
                onPressed: () {
                  tooltipController.hideTooltip();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.whs)),
                  textStyle: TextStyle(color: Colors.white, fontSize: 17.whs, fontWeight: FontWeight.w500),
                ),
                child: const Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: EdgeInsets.all(4.0),
                      child: Text("直接关闭"),
                    )),
              ),
              Gap(8.whs),
              ElevatedButton(
                onPressed: () {
                  tooltipController.hideTooltip();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.whs)),
                  textStyle: TextStyle(color: Colors.white, fontSize: 17.whs, fontWeight: FontWeight.w500),
                ),
                child: const Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: EdgeInsets.all(4.0),
                      child: Text("关闭并保存本次编辑"),
                    )),
              ),
            ],
          ),
        ),
        child: const Icon(Icons.close),
      ),
    );
  }
}

class AddNoteWidget extends StatelessWidget {
  final Subtitle subtitle;
  final int resourceType;
  final String resourceId;
  final String noteId;
  final int videoStartTime;
  final int videoEndTime;
  final NoteSaveCallback noteSaveCallback;
  final String? noteContent;

  const AddNoteWidget({
    super.key,
    required this.subtitle,
    required this.resourceType,
    required this.resourceId,
    required this.noteId,
    required this.noteSaveCallback,
    required this.videoStartTime,
    required this.videoEndTime,
    this.noteContent,
  });

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink(); // 这个 Widget 不会直接显示，而是通过 showAddNoteModal 调用
  }

  // 静态方法来显示模态框
  static void showAddNoteModal(
    BuildContext context, {
    required Subtitle subtitle,
    required int resourceType,
    required String resourceId,
    required String noteId,
    required NoteSaveCallback noteSaveCallback,
    required int videoStartTime,
    required int videoEndTime,
    String? noteContent,
  }) {
    final closeTooltipController = SuperTooltipController();

    void confirmClose() {
      closeTooltipController.hideTooltip();
      Navigator.of(context).pop();
    }

    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (modalContext) {
        final pages = <SliverWoltModalSheetPage>[];

        // 如果有笔记内容，先添加只读页面
        if (noteContent != null && noteContent.isNotEmpty) {
          pages.add(
            WoltModalSheetPage(
              leadingNavBarWidget: Padding(
                padding: EdgeInsets.only(left: 16.whs),
                child: Text("笔记", style: Get.textTheme.headlineMedium),
              ),
              trailingNavBarWidget: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
              stickyActionBar: Row(
                children: [
                  const Spacer(),
                  ElevatedButton(
                    onPressed: () {
                      WoltModalSheet.of(modalContext).showNext();
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: const Text("编辑"),
                  ),
                  Gap(16.whs),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.only(bottom: 36.whs),
                child: ReadOnlyNotePage(noteContent: noteContent),
              ),
            ),
          );
        }
        // 添加编辑页面
        pages.add(
          NonScrollingWoltModalSheetPage(
            leadingNavBarWidget: Padding(
              padding: EdgeInsets.only(left: 16.whs),
              child: Text("笔记", style: Get.textTheme.headlineMedium),
            ),
            trailingNavBarWidget: Padding(
              padding: EdgeInsets.only(right: 16.whs),
              child: CloseButtonWithConfirm(
                tooltipController: closeTooltipController,
                onConfirmClose: confirmClose,
              ),
            ),
            child: Builder(builder: (context) {
              return Padding(
                padding: EdgeInsets.only(top: 70.whs),
                child: EditNotePage(
                  subtitle: subtitle,
                  resourceType: resourceType,
                  resourceId: resourceId,
                  noteId: noteId,
                  noteSaveCallback: noteSaveCallback,
                  videoStartTime: videoStartTime,
                  videoEndTime: videoEndTime,
                  noteContent: noteContent,
                ),
              );
            }),
          ),
        );

        return pages;
      },
    );
  }
}

// 只读页面组件
class ReadOnlyNotePage extends StatelessWidget {
  final String? noteContent;

  const ReadOnlyNotePage({
    super.key,
    required this.noteContent,
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: 200.whs,
      ),
      child: GestureDetector(
        onTap: () {
          WoltModalSheet.of(context).showNext();
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 16.whs),
          child: Text(
            noteContent ?? '',
            style: Get.textTheme.titleMedium,
          ),
        ),
      ),
    );
  }
}

// 编辑页面组件
class EditNotePage extends StatefulWidget {
  final Subtitle subtitle;
  final int resourceType;
  final String resourceId;
  final String noteId;
  final int videoStartTime;
  final int videoEndTime;
  final NoteSaveCallback noteSaveCallback;
  final String? noteContent;

  const EditNotePage({
    super.key,
    required this.subtitle,
    required this.resourceType,
    required this.resourceId,
    required this.noteId,
    required this.noteSaveCallback,
    required this.videoStartTime,
    required this.videoEndTime,
    this.noteContent,
  });

  @override
  State<EditNotePage> createState() => _EditNotePageState();
}

class _EditNotePageState extends State<EditNotePage> {
  final FocusNode _focusNode = FocusNode();
  late TextEditingController textEditingController;
  final SuperTooltipController _deleteTooltipController = SuperTooltipController();

  @override
  void initState() {
    super.initState();
    textEditingController = TextEditingController(text: widget.noteContent ?? "");
    textEditingController.addListener(() {
      setState(() {});
    });
    if (widget.noteId.isNotEmpty && (widget.noteContent == null || widget.noteContent!.isEmpty)) {
      Net.getRestClient().localNote(widget.noteId).then((value) {
        textEditingController.text = value.data.content ?? "";
      });
    }

    // 延迟聚焦，确保页面完全显示后再弹出软键盘
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }

  void saveNote() {
    if (textEditingController.text.isEmpty) {
      "请输入笔记内容".toast;
      return;
    }
    Net.getRestClient().addNote({
      'id': widget.noteId,
      'resourceId': widget.resourceId,
      'resourceType': widget.resourceType,
      'content': textEditingController.text,
      'videoStartTime': widget.videoStartTime,
      'videoEndTime': widget.videoEndTime,
    }).then((value) {
      if (mounted) {
        widget.noteSaveCallback(value.data); // 传递保存后的NoteModel对象
        toastInfoSuccess("笔记已保存");
        Navigator.of(context).pop(); // 关闭编辑页面
      }
    });
  }

  void _showDeleteConfirmMenu() {
    _deleteTooltipController.showTooltip();
  }

  void _clearNote() {
    _deleteTooltipController.hideTooltip();
    textEditingController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.whs),
            child: TextField(
              focusNode: _focusNode,
              maxLines: null, // 允许无限行数，支持自动换行
              expands: true, // 让TextField填满可用空间
              decoration: const InputDecoration(
                hintText: "请在这里写下你的笔记...",
                contentPadding: EdgeInsets.zero,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
              ),
              style: Get.textTheme.titleMedium,
              controller: textEditingController,
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 16.whs),
          child: Row(
            children: [
              GestureDetector(
                onTap: _showDeleteConfirmMenu,
                child: SuperTooltip(
                  popupDirection: TooltipDirection.up,
                  controller: _deleteTooltipController,
                  backgroundColor: Colors.transparent,
                  borderColor: Colors.transparent,
                  hasShadow: false,
                  bubbleDimensions: EdgeInsets.symmetric(horizontal: 0.whs, vertical: 20.whs),
                  arrowLength: 0,
                  content: IntrinsicWidth(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            _deleteTooltipController.hideTooltip();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.whs)),
                            textStyle: TextStyle(color: Colors.white, fontSize: 17.whs, fontWeight: FontWeight.w500),
                          ),
                          child: const Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.all(4.0),
                                child: Text("不删除"),
                              )),
                        ),
                        Gap(8.whs),
                        ElevatedButton(
                          onPressed: _clearNote,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFF4511E),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.whs)),
                            textStyle: TextStyle(color: Colors.white, fontSize: 17.whs, fontWeight: FontWeight.w500),
                          ),
                          child: const Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.all(4.0),
                                child: Text("确认删除笔记"),
                              )),
                        ),
                      ],
                    ),
                  ),
                  child: Icon(
                    Icons.delete_outline,
                    color: gray400,
                    size: 20.whs,
                  ),
                ),
              ),
              Gap(8.whs),
              // 字符限制展示
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "${textEditingController.text.length}",
                      style: TextStyle(
                        color: textEditingController.text.length > 1000 ? Colors.red : Get.textTheme.bodyMedium?.color,
                      ),
                    ),
                    TextSpan(
                      text: "/1000",
                      style: TextStyle(
                        color: Get.textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              ElevatedButton(
                onPressed: textEditingController.text.isNotEmpty ? () => saveNote() : null,
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text("保存"),
              ),
              Gap(16.whs),
            ],
          ),
        )
      ],
    );
  }
}
