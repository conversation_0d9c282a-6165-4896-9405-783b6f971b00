import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/controllers/detail_controller.dart';
import 'package:lsenglish/app/modules/setting/views/subtitle_widget.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../../setting/views/player_widget.dart';
import '../../setting/views/setting_warp.dart';

class PlayerMoreWidget extends GetWidget<DetailController> {
  const PlayerMoreWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 56.whs),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.9,
          color: Get.theme.scaffoldBackgroundColor,
          child: Column(
            children: [
              AppBar(
                title: Text(
                  "更多",
                  style: Get.textTheme.titleLarge,
                ),
                backgroundColor: Get.theme.scaffoldBackgroundColor,
                leading: GestureDetector(onTap: () => Get.back(), child: const Icon(Icons.close_rounded)),
              ),
              Gap(20.whs),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: () {
                          Get.back();
                          // controller.goSubtitleSearch();
                        },
                        child: SettingItem(
                          child: Row(
                            children: [
                              Text(
                                "导入字幕",
                                style: Get.textTheme.titleSmall,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Gap(20.whs),
                      SettingItem(
                        child: Row(
                          children: [
                            Text(
                              "反馈",
                              style: Get.textTheme.titleSmall,
                            ),
                            const Spacer(),
                            Icon(Icons.arrow_forward_ios_rounded, size: 16.whs)
                          ],
                        ),
                      ),
                      Gap(20.whs),
                      const PlayerSettingWidget(),
                      Gap(20.whs),
                      const SubtitleSettingWidget(),
                      Gap(20.whs),
                      SettingItem(
                        child: Row(
                          children: [
                            Text(
                              "功能指引",
                              style: Get.textTheme.titleSmall,
                            ),
                            const Spacer(),
                            Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: 16.whs,
                            )
                          ],
                        ),
                      ),
                      Gap(30.whs),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
