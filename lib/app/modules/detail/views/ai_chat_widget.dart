import 'dart:convert';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';

import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';

//string: 分析结果 int:  1为覆盖 2为添加至笔记后
typedef AiSentenceSaveCallback = void Function(String, int);

class AiChatWidget extends StatefulWidget {
  final Subtitle subtitle;
  final bool isInputEmpty;

  const AiChatWidget({
    super.key,
    required this.subtitle,
    this.isInputEmpty = false,
  });

  @override
  State<AiChatWidget> createState() => _AiChatWidgetState();
}

class _AiChatWidgetState extends State<AiChatWidget> {
  var content = "";
  var englishSource = "";
  bool needCancelToken = false;

  final _systemAuthorId = "system";
  final _userAuthorId = "user";
  var sessionId = "";
  var prompt = "";
  var modelMessages = <ModelMessage>[];
  final _chatController = InMemoryChatController();

  void fetchAI() async {
    setState(() {
      if (_chatController.messages.isEmpty) {
        _chatController.insertMessage(
            Message.text(
              id: randomString(),
              text: "正在解析句子${widget.subtitle.targetData},请稍后...",
              authorId: _systemAuthorId,
            ),
            index: _chatController.messages.length);
      } else {
        _chatController.insertMessage(
            Message.text(
              id: randomString(),
              text: "正在努力回答中...",
              authorId: _systemAuthorId,
            ),
            index: _chatController.messages.length);
      }
    });
    content = "";
    try {
      needCancelToken = false;
      final cancelToken = CancelToken();
      // https://www.volcengine.com/docs/82379/1298454#%E8%AF%B7%E6%B1%82%E4%BD%93
      final response = await Net.getDio().post(
        'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer 73490fa9-1d7b-42b1-80a4-4de4b21c82e2',
            'Content-Type': 'application/json',
          },
          responseType: ResponseType.stream,
        ),
        data: jsonEncode({
          'model': 'ep-20241124203623-4zmmh',
          'stream': true,
          "messages": modelMessages,
        }),
        cancelToken: cancelToken,
      );

      // Use transform to decode the stream properly
      response.data.stream.cast<List<int>>().transform(utf8.decoder).transform(const LineSplitter()).listen((data) {
        debugPrint("data=$data");
        final jsonStartIndex = data.indexOf('{');
        if (jsonStartIndex != -1) {
          final jsonString = data.substring(jsonStartIndex);
          try {
            if (data == "[DONE]") {
              modelMessages.add(ModelMessage(role: 'system', content: content));
              content = "";
              return;
            }
            final jsonData = jsonDecode(jsonString);
            setState(() {
              var result = jsonData['choices'][0]['delta']['content'];
              content = content + result;
              _chatController.updateMessage(
                  _chatController.messages[0],
                  Message.text(
                    id: _chatController.messages[0].id,
                    text: content,
                    authorId: _chatController.messages[0].authorId,
                  ));
            });
          } catch (e) {
            logger('JSON decode error: $e');
          }
        }

        // Example condition to cancel
        if (needCancelToken) {
          cancelToken.cancel();
          needCancelToken = false;
        }
      }, onDone: () {
        logger('Stream finished.');
        SmartDialog.dismiss();
      }, onError: (error) {
        logger('Error: $error');
        SmartDialog.dismiss();
      });
    } catch (e) {
      logger('Error: $e');
      SmartDialog.dismiss();
    }
  }

  @override
  void initState() {
    super.initState();
    englishSource = widget.subtitle.targetData;
    prompt = englishSource;
    modelMessages
        .add(ModelMessage(role: 'system', content: "你是一个英语老师，简单地解析下句子的语法和难点。并且根据我提供的大量句子来结合解析我后续给你发送的单独的句子的含义,不要使用markdown的格式，最大的回复量不要超过1000个字符"));
    modelMessages.add(ModelMessage(role: 'user', content: prompt));
    fetchAI();
  }

  String randomString() {
    final random = Random.secure();
    final values = List<int>.generate(16, (i) => random.nextInt(255));
    return base64UrlEncode(values);
  }

  void _handleSendPressed(String message) {
    final textMessage = Message.text(
      id: randomString(),
      text: message,
      authorId: _userAuthorId,
    );

    prompt = message;
    setState(() {
      _chatController.insertMessage(textMessage, index: _chatController.messages.length);
    });
    modelMessages.add(ModelMessage(role: 'user', content: message));
    fetchAI();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("AI解析"),
      ),
      body: Chat(
        onMessageSend: _handleSendPressed,
        currentUserId: '',
        resolveUser: (String id) async {
          // return _user;
          return User(id: id);
        },
        chatController: _chatController,
      ),
    );
  }
}

class ModelMessage {
  String? role;
  String? content;

  ModelMessage({this.role, this.content});

  // 转换为 JSON 对象的方法
  Map<String, String?> toJson() {
    return {
      'role': role,
      'content': content,
    };
  }
}
