import 'package:lsenglish/utils/type.dart';

import '../../../../model/local_detail_resp/resource_detail_resp.dart';

bool shouldUseDoubleSubtitle(ResourceDetailResp? localDetailResp) {
  return localDetailResp?.resourceType == ResourceType.remoteResouce.index && localDetailResp?.subtitleUrl == "";
}

/// 过滤视频URL，移除查询参数部分
/// 例如：将 https://example.com/video.mp4?param1=value1&param2=value2
/// 转换为 https://example.com/video.mp4
String filterVideoUrl(String url) {
  if (url.isEmpty) return url;

  try {
    final uri = Uri.parse(url);
    return '${uri.scheme}://${uri.host}${uri.path}';
  } catch (e) {
    return url;
  }
}
