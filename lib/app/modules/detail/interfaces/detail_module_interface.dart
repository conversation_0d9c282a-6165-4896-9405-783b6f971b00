/// DetailController的基础接口，定义各个模块需要实现的通用方法
abstract class DetailModuleInterface {
  /// 模块初始化
  Future<void> initialize();

  /// 模块销毁
  Future<void> dispose();

  /// 获取详情数据
  Future<void> onFetchDetailData(Exception? error);

  /// 获取模块名称（用于调试）
  String get moduleName;

  /// 获取模块状态信息
  Map<String, dynamic> get stateInfo;

  /// 可见性丢失回调
  void onVisibilityLost();

  /// 可见性获得回调
  void onVisibilityGained();

  /// 界面尺寸变化回调
  void didChangeMetrics();
}
