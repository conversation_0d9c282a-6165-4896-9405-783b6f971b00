import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import '../controllers/pay_controller.dart';

class PayView extends GetView<PayController> {
  const PayView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PayView'),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Obx(() => Text("是否为会员(${controller.vipInfo.value.isVip})")),
          Obx(() => Text("是否为订阅(${controller.vipInfo.value.isSubscription})")),
          Obx(() {
            final expireDateStr = controller.vipInfo.value.expireDate;
            DateTime? expireDate;
            if (expireDateStr != null) {
              try {
                expireDate = DateTime.parse(expireDateStr);
              } catch (e) {
                print('日期解析错误: $e');
              }
            }
            if (expireDate != null) {
              final now = DateTime.now();
              final difference = expireDate.difference(now).inDays;
              return Text("会员过期时间(${controller.vipInfo.value.expireDate})，还有 $difference 天");
            }
            return const Text("会员过期时间(无)");
          }),
          Obx(
            () => ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.all(16),
              itemCount: controller.vipProducts.length,
              itemBuilder: (BuildContext context, int index) {
                return Column(
                  children: [
                    Text(controller.vipProducts[index].name ?? ""),
                    const Gap(16),
                    Text(controller.vipProducts[index].originPrice.toString()),
                    const Gap(16),
                    Text(controller.vipProducts[index].price.toString()),
                    const Gap(16),
                    TextButton(
                      style: TextButton.styleFrom(
                        backgroundColor: Get.theme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () => controller.chooseProduct(index),
                      child: const Text('购买'),
                    ),
                  ],
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
