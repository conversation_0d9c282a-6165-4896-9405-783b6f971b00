import 'package:apple_pay_plugin/apple_pay_plugin.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/vip/vip_product_resp/vip_product_resp.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/toast.dart';

import '../../../../model/vip/user_vip_info_resp/user_vip_info_resp.dart';

class PayController extends GetxController {
  ApplePayPlugin applePayPlugin = ApplePayPlugin();
  var vipProducts = <VipProductResp>[].obs;
  var vipInfo = UserVipInfoResp().obs;

  @override
  void onInit() {
    super.onInit();
    _fetchVipInfo();
    Net.getRestClient().getVipProducts().then((onValue) {
      vipProducts.value = onValue.data;
    });
  }

  void _fetchVipInfo() {
    Net.getRestClient().vipInfo().then((onValue) {
      vipInfo.value = onValue.data;
    });
  }

  void chooseProduct(int index) async {
    if (vipProducts[index].iosProductId == "") {
      "productId is empty".toast;
      return;
    }
    SmartDialog.showLoading(msg: "准备支付...");
    Net.getRestClient().createOrder({'productId': vipProducts[index].id}).then((onValue) async {
      debugPrint("uuid=${onValue.data.uuid}");
      if (onValue.data.uuid == "") {
        "uuid is empty".toast;
        SmartDialog.dismiss();
        return;
      }
      try {
        await applePayPlugin.purchase(vipProducts[index].iosProductId ?? "", onValue.data.uuid!);
      } catch (e) {
        SmartDialog.dismiss();
      }
      _fetchVipInfo();
      SmartDialog.dismiss();
    });
  }
}
