import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/app/modules/mine/audio_convert/controllers/audio_convert_controller.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/xfyun.dart';
import 'package:path/path.dart';

class AudioConvertView extends GetView<AudioConvertController> {
  const AudioConvertView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AudioConvertView'),
        centerTitle: true,
      ),
      body: Obx(() => Xfyun().xfyunAnalyzes.isEmpty
          ? Center(child: Text("暂无正在转换的资源", style: Get.textTheme.titleLarge))
          : ListView.builder(
              itemCount: Xfyun().xfyunAnalyzes.length,
              itemBuilder: (BuildContext context, int index) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 8.whs),
                  child: Container(
                    padding: EdgeInsets.all(16.whs),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(16.whs)),
                      color: Get.theme.primaryColor.withOpacity(0.5),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                basenameWithoutExtension(Xfyun().xfyunAnalyzes[index]?.audioFilePath ?? ""),
                                style: Get.textTheme.titleLarge,
                              ),
                            ),
                            SizedBox(
                              height: 30.whs,
                              child: ListView.builder(
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: Xfyun().xfyunAnalyzes[index]?.phaseCount,
                                itemBuilder: (BuildContext context, int phaseIndex) {
                                  return Padding(
                                    padding: EdgeInsets.all(2.whs),
                                    child: Obx(() => Container(
                                          width: 10.whs,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.all(Radius.circular(16.whs)),
                                            color: Xfyun().xfyunAnalyzes[index]?.currentPhaseCount.value == phaseIndex
                                                ? Get.theme.primaryColor
                                                : Get.theme.primaryColor.withOpacity(0.3),
                                          ),
                                        )),
                                  );
                                },
                              ),
                            )
                          ],
                        ),
                        Gap(16.whs),
                        Obx(() => Xfyun().xfyunAnalyzes[index]?.errorText.value != ""
                            ? Text(
                                Xfyun().xfyunAnalyzes[index]?.errorText.value ?? "",
                                style: const TextStyle(color: Colors.red),
                              )
                            : Text(Xfyun().xfyunAnalyzes[index]?.loadingText.value ?? "")),
                        Gap(8.whs),
                        Obx(() => Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(child: Text(Xfyun().xfyunAnalyzes[index]?.localResourceId ?? "")),
                                Xfyun().xfyunAnalyzes[index]?.errorText.value != ""
                                    ? FilledButton(
                                        onPressed: () {
                                          controller.retryXfyun(index);
                                        },
                                        child: Text("重试"),
                                      )
                                    : FilledButton(
                                        onPressed: () {
                                          controller.cancelXfyun(index);
                                        },
                                        child: Text("取消"),
                                      ),
                                Visibility(
                                    visible: Xfyun().xfyunAnalyzes[index]?.errorText.value != "",
                                    child: FilledButton(
                                      onPressed: () {
                                        controller.cancelXfyun(index);
                                      },
                                      child: Text("删除"),
                                    ))
                              ],
                            )),
                      ],
                    ),
                  ),
                );
              },
            )),
    );
  }
}
