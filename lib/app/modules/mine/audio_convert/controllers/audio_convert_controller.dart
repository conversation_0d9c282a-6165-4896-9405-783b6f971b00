
import 'package:get/get.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/xfyun.dart';

class AudioConvertController extends GetxController {
  var videoPath = '';
  var localResourceId = '';

  @override
  void onInit() {
    super.onInit();
    logger("AudioConvertController init");
    videoPath = Get.arguments['videoPath'] ?? "";
    localResourceId = Get.arguments['localResourceId'] ?? "";
    logger("videoPath = $videoPath");
    if (videoPath == '') {
      Get.back();
      return;
    }
    Xfyun().addAnalyze(XfyunAnalyze(audioFilePath: videoPath, localResourceId: localResourceId));
  }

  void cancelXfyun(int index) {
    Xfyun().cancelAnalyze(Xfyun().xfyunAnalyzes[index]?.localResourceId);
  }

  void retryXfyun(int index) {
    Xfyun().retryAnalyze(Xfyun().xfyunAnalyzes[index]?.localResourceId);
  }
}
