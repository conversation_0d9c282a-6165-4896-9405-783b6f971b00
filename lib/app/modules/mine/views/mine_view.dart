import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/generated/locales.g.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/widgets/only_lines_switch.dart';

import '../../../routes/app_pages.dart';
import '../controllers/mine_controller.dart';

class MineView extends GetView<MineController> {
  const MineView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的'),
        centerTitle: true,
      ),
      body: Padding(
          padding: EdgeInsets.all(16.whs),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Obx(() => GestureDetector(
                    onTap: () => controller.uploadAvatar(),
                    child: <PERSON><PERSON><PERSON><PERSON>(controller.user.value.avatar?.isEmpty ?? true ? R.ai : controller.user.value.avatar!,
                        width: 70.whs, height: 70.whs))),
                Gap(16.whs),
                Obx(() => GestureDetector(onTap: () => controller.uploadAvatar(), child: Text(controller.user.value.nickname ?? ""))),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      controller.testSubtitle();
                    },
                    child: Text("字幕测试")),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      Get.toNamed(Routes.AUDIO_CONVERT);
                    },
                    child: Text(LocaleKeys.setting_voiceTransList.tr)),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      Get.toNamed(Routes.VIDEO_COMPRESS);
                    },
                    child: Text(LocaleKeys.setting_videoCompressList.tr)),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      Get.toNamed(Routes.PAY);
                    },
                    child: Text("支付")),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      Get.toNamed(Routes.ORDERLIST);
                    },
                    child: Text(LocaleKeys.setting_orderList.tr)),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      controller.testAliyunPan();
                    },
                    child: Text("阿里云盘")),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      Get.toNamed(Routes.SETTING);
                    },
                    child: Text(LocaleKeys.setting_title.tr)),
                    OnlyLinesSwitch(),
              ],
            ),
          )),
    );
  }
}
