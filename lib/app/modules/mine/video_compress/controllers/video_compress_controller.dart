import 'package:get/get.dart';
import 'package:lsenglish/utils/video_compression.dart';

class VideoCompressController extends GetxController {
  var videoPath = '';
  var localResourceId = '';

  @override
  void onInit() {
    super.onInit();
    videoPath = Get.arguments?['videoPath'] ?? "";
    localResourceId = Get.arguments?['localResourceId'] ?? "";
    if (videoPath == '') {
      Get.back();
      return;
    }
  }

  void cancelCompress(int index) {
    VideoCompressionUtil().cancelCompress(VideoCompressionUtil().videoCompresses[index]?.videoPath);
  }

  void retryCompress(int index) {
    VideoCompressionUtil().retryCompress(VideoCompressionUtil().videoCompresses[index]?.videoPath);
  }
}
