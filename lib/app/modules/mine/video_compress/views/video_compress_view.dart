import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:path/path.dart';

import '../../../../../utils/video_compression.dart';
import '../controllers/video_compress_controller.dart';

class VideoCompressView extends GetView<VideoCompressController> {
  const VideoCompressView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AudioConvertView'),
        centerTitle: true,
      ),
      body: Obx(() => VideoCompressionUtil().videoCompresses.isEmpty
          ? Center(child: Text("暂无正在压缩的资源", style: Get.textTheme.titleLarge))
          : ListView.builder(
              itemCount: VideoCompressionUtil().videoCompresses.length,
              itemBuilder: (BuildContext context, int index) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 8.whs),
                  child: Container(
                    padding: EdgeInsets.all(16.whs),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(16.whs)),
                      color: Get.theme.primaryColor.withOpacity(0.5),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          basenameWithoutExtension(VideoCompressionUtil().videoCompresses[index]?.videoPath ?? ""),
                          style: Get.textTheme.titleLarge,
                        ),
                        Gap(16.whs),
                        Obx(() => VideoCompressionUtil().videoCompresses[index]?.errorText.value != ""
                            ? Text(
                                VideoCompressionUtil().videoCompresses[index]?.errorText.value ?? "",
                                style: const TextStyle(color: Colors.red),
                              )
                            : Text(VideoCompressionUtil().videoCompresses[index]?.loadingText.value ?? "")),
                        Gap(8.whs),
                        Obx(() => Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                VideoCompressionUtil().videoCompresses[index]?.errorText.value != ""
                                    ? FilledButton(
                                        onPressed: () {
                                          controller.retryCompress(index);
                                        },
                                        child: Text("重试"),
                                      )
                                    : FilledButton(
                                        onPressed: () {
                                          controller.cancelCompress(index);
                                        },
                                        child: Text("取消"),
                                      ),
                                Visibility(
                                    visible: VideoCompressionUtil().videoCompresses[index]?.errorText.value != "",
                                    child: FilledButton(
                                      onPressed: () {
                                        controller.cancelCompress(index);
                                      },
                                      child: Text("删除"),
                                    ))
                              ],
                            )),
                      ],
                    ),
                  ),
                );
              },
            )),
    );
  }
}
