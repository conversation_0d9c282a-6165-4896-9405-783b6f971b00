import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import '../controllers/aliyunpan_controller.dart';
import 'package:lsenglish/app/modules/aliyunpan/views/aliyunpan_file_manager.dart';

class AliyunpanView extends GetView<AliyunpanController> {
  const AliyunpanView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('阿里云盘'),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0.0,
      ),
      body: Obx(() {
        if (controller.isLoading.value || controller.drives.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        final drive = controller.drives[0];
        final resourceDriveId = drive.resourceDriveId;
        final backupDriveId = drive.backupDriveId;

        return ListView(
          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
          children: [
            _buildDriveItem('资源库', resourceDriveId ?? ''),
            _buildDriveItem('备份盘', backupDriveId ?? ''),
          ],
        );
      }),
    );
  }

  Widget _buildDriveItem(String title, String driveId) {
    return InkWell(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(width: 0.5, color: Color(0xffe5e5e5))),
        ),
        child: ListTile(
          title: Text(title),
          trailing: const Icon(Icons.chevron_right),
        ),
      ),
      onTap: () async {
        var result = await Get.to(
            () => AliyunpanFileManager(
                  driveId: driveId,
                  parentFileId: 'root',
                  title: title,
                ),
            preventDuplicates: false);
        if (result != null) {
          await Get.toNamed(Routes.DETAIL, arguments: {
            'videoUrlOrPath': result['videoUrlOrPath'],
            'videoName': result['videoName'],
          });
        }
      },
    );
  }
}
