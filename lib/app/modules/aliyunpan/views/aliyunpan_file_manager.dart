import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:aliyunpan_sdk/aliyunpan_sdk.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:lsenglish/app/modules/aliyunpan/controllers/aliyunpan_controller.dart';

class AliyunpanFileManager extends StatefulWidget {
  const AliyunpanFileManager({
    super.key,
    required this.driveId,
    required this.parentFileId,
    required this.title,
  });

  final String driveId;
  final String parentFileId;
  final String title;

  @override
  State<AliyunpanFileManager> createState() => _AliyunpanFileManagerState();
}

class _AliyunpanFileManagerState extends State<AliyunpanFileManager> {
  final controller = Get.find<AliyunpanController>();
  final fileList = <FileInfo>[].obs;
  final isLoading = false.obs;
  final currentParentFileId = 'root'.obs;

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('zh_CN');
    currentParentFileId.value = widget.parentFileId;
    _getFiles();
  }

  Future<void> _getFiles() async {
    try {
      isLoading.value = true;
      final result = await controller.aliyunpanClient.send(
        GetFiles(
          driveId: widget.driveId,
          parentFileId: currentParentFileId.value,
          orderBy: FileOrder.updatedAt,
          orderDirection: FileOrderDirection.desc,
        ),
      );

      fileList.value = result.items;
    } catch (e) {
      debugPrint('获取文件列表失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.parentFileId == 'root' ? '阿里云盘' : widget.title,
          style: const TextStyle(color: Colors.black),
        ),
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0.0,
      ),
      body: Obx(() {
        if (isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return ListView(
          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
          children: fileList.map((file) {
            if (file.type == FileType.folder) {
              return _buildFolderItem(file);
            } else {
              return _buildFileItem(file);
            }
          }).toList(),
        );
      }),
    );
  }

  Widget _buildFileItem(FileInfo file) {
    String modifiedTime = DateFormat('yyyy-MM-dd HH:mm:ss', 'zh_CN').format(file.updatedAt.toLocal());

    return InkWell(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(width: 0.5, color: Color(0xffe5e5e5))),
        ),
        child: ListTile(
          leading: file.thumbnail != null && file.thumbnail!.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.network(
                    file.thumbnail!,
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => const Icon(Icons.insert_drive_file),
                  ),
                )
              : const Icon(Icons.insert_drive_file),
          title: Text(file.name),
          subtitle: Text('$modifiedTime  ${_formatFileSize(file.size ?? 0)}', style: const TextStyle(fontSize: 12.0)),
        ),
      ),
      onTap: () async {
        if (file.type == FileType.file && file.category == FileCategory.video) {
          SmartDialog.showLoading();
          final result = await controller.aliyunpanClient.send(
            GetVideoPlayInfo(
              driveId: file.driveId,
              fileId: file.fileId,
            ),
          );
          var firstVideo = result.videoPreviewPlayInfo.liveTranscodingTaskList.first;
          Get.back(result: {
            'offAll': true,
            'videoUrlOrPath': firstVideo.url,
            'videoName': file.name,
          });
          SmartDialog.dismiss();
        }
      },
    );
  }

  Widget _buildFolderItem(FileInfo folder) {
    String modifiedTime = DateFormat('yyyy-MM-dd HH:mm:ss', 'zh_CN').format(folder.updatedAt.toLocal());

    return InkWell(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(width: 0.5, color: Color(0xffe5e5e5))),
        ),
        child: ListTile(
          leading: folder.thumbnail != null && folder.thumbnail!.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.network(
                    folder.thumbnail!,
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => const Icon(Icons.folder),
                  ),
                )
              : const Icon(Icons.folder),
          title: Text(folder.name),
          subtitle: Text(modifiedTime, style: const TextStyle(fontSize: 12.0)),
          trailing: const Icon(Icons.chevron_right),
        ),
      ),
      onTap: () async {
        var result = await Get.to(
            () => AliyunpanFileManager(
                  driveId: widget.driveId,
                  parentFileId: folder.fileId,
                  title: folder.name,
                ),
            preventDuplicates: false);
        if (result != null && result['offAll'] == true) {
          Get.back(result: {
            'offAll': true,
            'videoUrlOrPath': result['videoUrlOrPath'],
            'videoName': result['videoName'],
          });
        }
      },
    );
  }

  String _formatFileSize(int size) {
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(2)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }
}
