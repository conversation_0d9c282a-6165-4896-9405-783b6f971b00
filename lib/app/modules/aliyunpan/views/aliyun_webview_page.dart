import 'dart:io';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AliyunWebViewPage extends StatefulWidget {
  final Uri uri;
  final Function(String)? accept;

  const AliyunWebViewPage({
    Key? key,
    required this.uri,
    this.accept,
  }) : super(key: key);

  @override
  State<AliyunWebViewPage> createState() => _AliyunWebViewPageState();
}

class _AliyunWebViewPageState extends State<AliyunWebViewPage> {
  late final controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setBackgroundColor(const Color(0x00000000))
    ..setNavigationDelegate(
      NavigationDelegate(
        onProgress: (int progress) {
          // 可以在这里添加加载进度条
        },
        onNavigationRequest: (request) {
          if (widget.accept?.call(request.url) ?? false) {
            Navigator.pop(context, request.url);
            return NavigationDecision.prevent;
          }
          return NavigationDecision.navigate;
        },
      ),
    )
    ..loadRequest(widget.uri);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('阿里云盘授权'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Platform.isIOS || Platform.isAndroid
          ? WebViewWidget(controller: controller)
          : const Center(
              child: Text(
                'WebView 仅支持 Android 和 iOS',
                style: TextStyle(fontSize: 20),
              ),
            ),
    );
  }
} 