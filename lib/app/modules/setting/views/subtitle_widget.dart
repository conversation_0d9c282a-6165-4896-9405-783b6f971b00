import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/size_extension.dart';

import 'setting_warp.dart';

class SubtitleSettingWidget extends StatelessWidget {
  const SubtitleSettingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.whs),
            child: Text(
              "字幕",
              style: Get.textTheme.bodyLarge?.copyWith(color: gray500),
            ),
          ),
          Gap(12.whs),
          SettingItem(
              child: Column(
            children: [
              Row(
                children: [
                  Text(
                    "遮挡字幕",
                    style: Get.textTheme.titleSmall,
                  ),
                  const Spacer(),
                  Obx(() => CupertinoSwitch(
                        activeColor: Get.theme.primaryColor,
                        value: Config().coverSubtitle.value,
                        onChanged: (bool value) {
                          Config().setCoverSubtitle();
                          if (value) {
                            Get.back();
                          }
                        },
                      ))
                ],
              ),
              const Divider(),
              Row(
                children: [
                  Text(
                    "去第三方查找字幕",
                    style: Get.textTheme.titleSmall,
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 16.whs,
                  ),
                ],
              ),
            ],
          )),
        ],
      ),
    );
  }
}
