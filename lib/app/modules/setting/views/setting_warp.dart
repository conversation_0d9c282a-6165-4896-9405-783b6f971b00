import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';

class SettingItem extends StatelessWidget {
  final Widget? child;
  const SettingItem({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 9.whs),
      child: Container(
        decoration: BoxDecoration(
          color: Get.theme.scaffoldBackgroundColor,
          borderRadius: BorderRadius.all(Radius.circular(8.whs)),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 12.whs),
          child: child,
        ),
      ),
    );
  }
}
