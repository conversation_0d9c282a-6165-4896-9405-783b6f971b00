import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/generated/locales.g.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/sp.dart';

import '../controllers/setting_controller.dart';

class SettingView extends GetView<SettingController> {
  const SettingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SettingView'),
        centerTitle: true,
      ),
      body: Padding(
          padding: EdgeInsets.all(16.whs),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Obx(() => Switch(
                      value: Config().themeMode.value == ThemeMode.dark,
                      onChanged: (bool value) {
                        controller.toggleTheme(value);
                      },
                    )),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      controller.switchLanguage();
                    },
                    child: Text(LocaleKeys.setting_switchLanguage.tr)),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      controller.uploadLog();
                    },
                    child: Text(LocaleKeys.setting_uploadLog.tr)),
                Gap(16.whs),
                FilledButton(
                    onPressed: () {
                      SPUtil().clearUserInfo();
                      Get.toNamed(Routes.LOGIN);
                    },
                    child: Text(LocaleKeys.setting_logout.tr)),
              ],
            ),
          )),
    );
  }
}
