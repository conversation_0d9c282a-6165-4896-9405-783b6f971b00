import 'package:get/get.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/model/setting/lang.dart';
import 'package:lsenglish/net/net.dart';

class LanguageSettingsController extends GetxController {
  var selectedNativeLang = LangModel().obs;
  var selectedTargetLang = LangModel().obs;
  var isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    // 初始化当前用户的语言设置
    final user = Config().currentUser.value;
    if (user.nativeLangCode != null && user.nativeLangCode!.isNotEmpty) {
      final nativeLang = Config().langs.nativeLanguages?.firstWhereOrNull((lang) => lang.code == user.nativeLangCode);
      if (nativeLang != null) {
        selectedNativeLang.value = nativeLang;
      }
    }

    if (user.targetLangCode != null && user.targetLangCode!.isNotEmpty) {
      final targetLang = Config().langs.targetLanguages?.firstWhereOrNull((lang) => lang.code == user.targetLangCode);
      if (targetLang != null) {
        selectedTargetLang.value = targetLang;
      }
    }
  }

  void selectNativeLanguage(LangModel language) {
    selectedNativeLang.value = language;
  }

  void selectTargetLanguage(LangModel language) {
    selectedTargetLang.value = language;
  }

  Future<void> saveLanguageSettings() async {
    if (selectedNativeLang.value.code == null || selectedTargetLang.value.code == null) {
      Get.snackbar('提示', '请选择母语和目标语言');
      return;
    }

    isLoading.value = true;
    try {
      final user = await Net.getRestClient().updateUserInfo({
        'nativeLangCode': selectedNativeLang.value.code,
        'targetLangCode': selectedTargetLang.value.code,
      });

      Config().currentUser.value = user.data;
      Get.snackbar('成功', '语言设置已保存');
      Get.back();
    } catch (e) {
      Get.snackbar('错误', '保存失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }
}
