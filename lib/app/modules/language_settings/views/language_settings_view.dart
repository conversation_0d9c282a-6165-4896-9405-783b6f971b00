import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/config/config.dart';
import '../controllers/language_settings_controller.dart';

class LanguageSettingsView extends GetView<LanguageSettingsController> {
  const LanguageSettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final langs = Config().langs;
    if (langs.nativeLanguages == null || langs.targetLanguages == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('语言设置'),
          backgroundColor: Get.theme.appBarTheme.backgroundColor,
          foregroundColor: Get.theme.appBarTheme.foregroundColor,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('语言设置'),
        backgroundColor: Get.theme.appBarTheme.backgroundColor,
        foregroundColor: Get.theme.appBarTheme.foregroundColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 母语选择
            Text(
              '选择您的母语',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Get.theme.dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ExpansionTile(
                title: Obx(() => Text(
                  controller.selectedNativeLang.value.name ?? '请选择母语',
                  style: Get.textTheme.bodyLarge,
                )),
                children: langs.nativeLanguages!.map((lang) {
                  return ListTile(
                    title: Text(lang.name ?? ''),
                    subtitle: Text(lang.code ?? ''),
                    trailing: Obx(() => controller.selectedNativeLang.value.code == lang.code
                        ? Icon(Icons.check, color: Get.theme.primaryColor)
                        : const SizedBox.shrink()),
                    onTap: () => controller.selectNativeLanguage(lang),
                  );
                }).toList(),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // 目标语言选择
            Text(
              '选择您要学习的目标语言',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Get.theme.dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ExpansionTile(
                title: Obx(() => Text(
                  controller.selectedTargetLang.value.name ?? '请选择目标语言',
                  style: Get.textTheme.bodyLarge,
                )),
                children: langs.targetLanguages!.map((lang) {
                  return ListTile(
                    title: Text(lang.name ?? ''),
                    subtitle: Text(lang.code ?? ''),
                    trailing: Obx(() => controller.selectedTargetLang.value.code == lang.code
                        ? Icon(Icons.check, color: Get.theme.primaryColor)
                        : const SizedBox.shrink()),
                    onTap: () => controller.selectTargetLanguage(lang),
                  );
                }).toList(),
              ),
            ),
            
            const Spacer(),
            
            // 保存按钮
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value 
                    ? null 
                    : controller.saveLanguageSettings,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: controller.isLoading.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text(
                        '保存设置',
                        style: TextStyle(fontSize: 16),
                      ),
              )),
            ),
          ],
        ),
      ),
    );
  }
} 