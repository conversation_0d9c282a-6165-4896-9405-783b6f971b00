import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/base/loading_getxcontroller.dart';
import 'package:lsenglish/model/resource.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/extension.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/utils/routes.dart';
import 'package:lsenglish/utils/type.dart';
import 'package:lsenglish/widgets/base_dialog.dart';

class WatchHistoryRespWrap {
  int position;
  String name;
  String resourceId;
  int resourceType;
  String cover;
  String existPath;
  Resource? resource;

  WatchHistoryRespWrap({
    this.name = "",
    this.position = 0,
    this.resourceId = "",
    this.cover = "",
    this.existPath = "",
    this.resourceType = 0,
    this.resource,
  });
}

class HomeController extends LoadingGetxcontroller {
  var watchHistorys = <WatchHistoryRespWrap>[].obs;
  @override
  void onInit() {
    super.onInit();
    subscriptions.addAll([
      ObsUtil().renameLocalFileName.listen((data) {
        if (data) {
          getWatchHistory();
        }
      }),
      ObsUtil().watchHistoryPositionChange.listen((data) {
        if (data.length == 2) {
          loadTargetVideoThumbnail(data[0], data[1]);
        }
      }),
      ObsUtil().loginStatus.listen((isLogin) {
        if (isLogin) {
          getWatchHistory();
        }
      }),
    ]);
    getWatchHistory();
  }

  @override
  void resetState() {
    super.resetState();
    getWatchHistory();
  }

  @override
  void onNetConnected() {
    if (watchHistorys.isEmpty) {
      getWatchHistory();
    }
  }

  void getWatchHistory() {
    Net.getRestClient().watchHistorys(1, 10).then((value) async {
      var tempWatchHistorys = <WatchHistoryRespWrap>[];
      setLoading(message: "正在生成预览图...");
      for (var e in value.data) {
        var videoPath = findFirstExistingPath(e.resource?.localVideoPaths);
        logger("getWatchHistory findFirstExistingPath=$videoPath");
        var thumb = "";
        if (videoPath == null || videoPath == "") {
          if (e.resource?.name != null && e.resource?.name != "") {
            videoPath = await FileUtils().findVideoFilePath((await FileUtils().getSaveDir()).path, e.resource!.name!);
          }
        }
        if (videoPath != null && videoPath != "") {
          thumb = "${(await FileUtils().getVideoThumbDir()).path}/${e.resourceId}.jpg";
        }
        tempWatchHistorys.add(WatchHistoryRespWrap(
          position: e.position ?? 0,
          resourceId: e.resourceId ?? "",
          cover: e.resourceType == ResourceType.localResource.index ? thumb : (e.resource?.cover ?? ""),
          existPath: videoPath ?? "",
          name: e.resource?.name ?? "",
          resourceType: e.resourceType ?? 1,
          resource: e.resource,
        ));
      }
      setSuccess();
      watchHistorys.assignAll(tempWatchHistorys);
      watchHistorys.refresh();
      loadVideoThumbnail();
    }).catchError((e) {
      setError();
    });
  }

  void onFocusGained() {
    if (watchHistorys.isEmpty) {
      getWatchHistory();
    }
  }

  void loadVideoThumbnail() async {
    for (var element in watchHistorys) {
      var thumb = "";
      if (element.existPath != "") {
        var timestamp = DateTime.now().millisecondsSinceEpoch;
        await cleanUpThumbnails(element.resourceId);
        var outputThumbnailPath = "${(await FileUtils().getVideoThumbDir()).path}/${element.resourceId}_$timestamp.jpg";
        logger("${element.name}开始生成封面图$outputThumbnailPath element.position=${element.position}");
        await FFmpegUtils().getVideoThumbnail(element.existPath, outputThumbnailPath, duration: Duration(milliseconds: element.position));
        thumb = outputThumbnailPath;
        logger("${element.name}获取到封面图$thumb");
        element.cover = thumb;
      }
    }
    watchHistorys.refresh();
  }

  void loadTargetVideoThumbnail(String resourceId, int position) async {
    logger("loadTargetVideoThumbnail resourceId=$resourceId position=$position");
    for (var element in watchHistorys) {
      var thumb = "";
      if (element.resourceId == resourceId && element.existPath != "") {
        await cleanUpThumbnails(element.resourceId);
        var timestamp = DateTime.now().millisecondsSinceEpoch;
        var outputThumbnailPath = "${(await FileUtils().getVideoThumbDir()).path}/${element.resourceId}_$timestamp.jpg";
        logger("loadTargetVideoThumbnail outputThumbnailPath=$outputThumbnailPath");
        await FFmpegUtils().getVideoThumbnail(element.existPath, outputThumbnailPath, duration: Duration(milliseconds: position));
        thumb = outputThumbnailPath;
        element.cover = thumb;
      }
    }
    watchHistorys.refresh();
  }

  Future<void> cleanUpThumbnails(String resourceId) async {
    final dir = await FileUtils().getVideoThumbDir();
    final files = dir.listSync();

    for (var file in files) {
      if (file is File && file.path.contains(resourceId)) {
        await file.delete();
      }
    }
  }

  bool pathExists(String path) {
    return File(path).existsSync();
  }

  String? findFirstExistingPath(List<String>? paths) {
    if (paths == null) return null;
    return paths.firstWhereOrNull((path) => pathExists(path));
  }

  void historyLongPress(int index) async {
    CommonDialog(
      title: "删除该记录?",
      options: const ["确定"],
      callbacks: [
        () => {
              Net.getRestClient().deleteWatchHistory({
                'resourceId': watchHistorys[index].resourceId,
                'resourceType': watchHistorys[index].resourceType,
              }),
              watchHistorys.removeAt(index),
            }
      ],
    ).showDialog;
  }

  void historyClick(int index) async {
    if (watchHistorys[index].resourceType == ResourceType.localResource.index && watchHistorys[index].existPath == "") {
      clickEmptyPath(watchHistorys[index].name);
      return;
    }

    if (watchHistorys[index].resourceType == ResourceType.localResource.index) {
      await RoutesUtil().goDetailByPath(watchHistorys[index].existPath);
    } else {
      await RoutesUtil().goVideoDetailByRemote(
        watchHistorys[index].resource?.videoURL,
        watchHistorys[index].resource?.name,
        watchHistorys[index].resourceId,
      );
    }

    var item = watchHistorys.removeAt(index); // 移除指定索引的元素
    watchHistorys.insert(0, item);
    watchHistorys.refresh();
  }

  void clickEmptyPath(String videoName) {
    // toastInfoError("路径为空，不允许进入");
    CommonDialog(title: "\"$videoName\"\n在本设备中找不到对应的视频，是否导入视频匹配该数据?", options: const [
      "去导入"
    ], callbacks: [
      () => {
            importVideo2OriginData(videoName),
          }
    ]).showDialog;
  }

  void importVideo2OriginData(String videoName) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      initialDirectory: (await FileUtils().getSaveDir()).path,
    );
    if (result != null) {
      var videoPath = result.files.single.path ?? "";
      var movedVideoPath = await FileUtils().moveFileToDocuments(videoPath);
      var newNameVideoPath = await FileUtils().renameFile(movedVideoPath, videoName);
      ObsUtil().renameLocalFileName.value = true;
      ObsUtil().renameLocalFileName.refresh();
      await RoutesUtil().goDetailByPath(newNameVideoPath);
      getWatchHistory();
    }
  }

  void importVideo({BuildContext? context}) async {
    // Get.to(() => FileManager(targetDirPath: "/sdcard/"), preventDuplicates: false);
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      initialDirectory: (await FileUtils().getSaveDir()).path,
    );
    if (result != null) {
      logger("local path =${result.files.single.path}");
      await RoutesUtil().goDetailByPath(result.files.single.path ?? "");
      getWatchHistory();
    }
    // if (GetPlatform.isDesktop) {
    //   FilePickerResult? result = await FilePicker.platform.pickFiles(type: FileType.video);
    //   if (result != null) {
    //     File file = File(result.files.single.path!);
    //     Get.toNamed(Routes.DETAIL, arguments: {'videoPath': file.path});
    //   }
    // } else {
    //   final List<AssetEntity>? result = await AssetPicker.pickAssets(
    //     context,
    //     pickerConfig: AssetPickerConfig(
    //       maxAssets: 1,
    //       themeColor: Get.theme.primaryColor,
    //       requestType: RequestType.video,
    //     ),
    //   );
    //   if (result != null && result.isNotEmpty) {
    //     final file = await result[0].file;
    //     if (file != null) {
    //       Get.toNamed(Routes.DETAIL, arguments: {'videoPath': file.path});
    //     }
    //   }
    // }
  }
}
