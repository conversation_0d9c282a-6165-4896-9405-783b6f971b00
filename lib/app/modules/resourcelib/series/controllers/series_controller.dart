import 'package:get/get.dart';
import 'package:lsenglish/model/resource_resp/resource_resp.dart';
import 'package:lsenglish/model/series_resp/series_detail_resp.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/routes.dart';

class SeriesController extends GetxController {
  var seriesDetailResp = SeriesDetailResp().obs;
  var resources = <ResourceResp>[].obs;
  
  // 选择模式相关参数
  bool isSelectionMode = false;
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取传递的参数
    final arguments = Get.arguments;
    if (arguments != null) {
      isSelectionMode = arguments['isSelectionMode'] ?? false;
    }
    
    var seriesId = arguments?['seriesId'] ?? "";
    Net.getRestClient().getResourceBySeriesId(seriesId).then((onValue) {
      seriesDetailResp.value = onValue.data;
      resources.value = onValue.data.resources ?? [];
    });
  }

  void onResourceItemClick(int index) {
    var resource = resources[index];
    
    // 如果是选择模式，直接返回选中的资源
    if (isSelectionMode) {
      Get.back(result: {
        'resourceId': resource.id,
        'name': resource.defaultLangTitle,
        'cover': resource.cover,
      });
      return;
    }
    
    // 正常模式，进入详情页
    RoutesUtil().goVideoDetailByRemote(resource.videoUrl, resource.defaultLangTitle, resource.id);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
