import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../controllers/series_controller.dart';

class SeriesView extends GetView<SeriesController> {
  const SeriesView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: AspectRatio(
              aspectRatio: 482 / 680,
              child: Stack(
                children: [
                  Positioned.fill(child: Obx(() => ImageLoader(controller.seriesDetailResp.value.cover ?? ""))),
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: const Alignment(0.00, -1.00),
                          end: const Alignment(0, 1),
                          colors: [Colors.black.withOpacity(0), Colors.black],
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 16.whs,
                    left: 16.whs,
                    right: 16.whs,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Obx(() => Center(
                              child: Text(
                                controller.seriesDetailResp.value.title ?? "",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 19.whs,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            )),
                        Gap(32.whs),
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                height: 48.whs,
                                decoration: ShapeDecoration(
                                  color: Colors.white.withOpacity(0.3),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    ImageLoader(R.play, size: 20.whs, color: Colors.white),
                                    Gap(8.whs),
                                    Text(
                                      '收藏',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 17.whs,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Gap(12.whs),
                            Expanded(
                              child: Container(
                                height: 48.whs,
                                decoration: ShapeDecoration(
                                  color: Colors.white.withOpacity(0.3),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    ImageLoader(R.download, size: 20.whs, color: Colors.white),
                                    Gap(8.whs),
                                    Text(
                                      '下载',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 17.whs,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                        Gap(16.whs),
                        Obx(() => Text(
                              overflow: TextOverflow.ellipsis,
                              maxLines: 3,
                              textAlign: TextAlign.start,
                              controller.seriesDetailResp.value.description ?? "",
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.6),
                                fontSize: 14.whs,
                                fontWeight: FontWeight.w400,
                              ),
                            )),
                      ],
                    ),
                  ),
                  Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 50.whs),
                      child: GestureDetector(onTap: () => Get.back(), child: const Icon(Icons.arrow_back_ios_new_rounded, color: Colors.white))),
                ],
              ),
            ),
          ),
          Obx(() => SliverList.builder(
                itemCount: controller.resources.length,
                itemBuilder: (BuildContext context, int index) {
                  return GestureDetector(
                    onTap: () => controller.onResourceItemClick(index),
                    child: Padding(
                      padding: EdgeInsets.only(top: index == 0 ? 16.whs : 0),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 8.whs),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Text(
                                  "${index + 1}",
                                  style: TextStyle(fontSize: 16.whs),
                                ),
                                Gap(16.whs),
                                Expanded(child: Text(controller.resources[index].defaultLangTitle ?? "", style: TextStyle(fontSize: 16.whs))),
                                Gap(16.whs),
                                ImageLoader(R.heart_select, size: 20.whs, color: Get.theme.colorScheme.secondary),
                                Gap(16.whs),
                                ImageLoader(R.download, size: 20.whs, color: Get.theme.colorScheme.secondary),
                              ],
                            ),
                            Gap(8.whs),
                            const Divider(),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )),
        ],
      ),
    );
  }
}
