import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/model/resource_lib_home_resp/category.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/routes.dart';

import '../../../../model/resource_lib_home_resp/category_type_list.dart';
import '../../../../model/resource_lib_home_resp/recommend.dart';
import '../../../../model/resource_lib_home_resp/resource_lib_home_item_resp.dart';

class ResourcelibController extends GetxController with GetTickerProviderStateMixin {
  var categoryTypeList = <CategoryTypeList>[].obs;
  var recommendList = <Recommend>[].obs;
  List<TabController> tabControllers = [];
  Map<int, String> categoryIdsMap = {};
  List<String> categoryIds = [];
  var resourceList = <ResourceLibHomeItemResp>[].obs;
  
  // 选择模式相关参数
  bool isSelectionMode = false;
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取传递的参数
    final arguments = Get.arguments;
    if (arguments != null) {
      isSelectionMode = arguments['isSelectionMode'] ?? false;
    }
    
    Net.getRestClient().resourceHome().then((onValue) {
      var data = onValue.data.categoryTypeList ?? [];
      recommendList.value = onValue.data.recommend ?? [];
      for (var i = 0; i < data.length; i++) {
        data[i].categories?.insert(0, Category(name: "全部"));
      }
      categoryTypeList.value = data;
      for (var controller in tabControllers) {
        controller.dispose();
      }
      tabControllers.clear();
      for (var i = 0; i < categoryTypeList.length; i++) {
        var t = TabController(vsync: this, length: categoryTypeList[i].categories?.length ?? 0);
        tabControllers.add(t);
        addTabListener(t, categoryTypeList[i].categories, i);
      }
      whenTabChange();
    });
    
  }

  void addTabListener(TabController tabController, List<Category>? categories, int index) {
    tempListener() {
      if (tabController.indexIsChanging) {
        categoryIdsMap[index] = categories?[tabController.index].id ?? "";
        whenTabChange();
      }
    }

    tabController.addListener(tempListener);
  }

  void whenTabChange() {
    categoryIds = categoryIdsMap.entries.map((e) => e.value).where((e) => e != "").toList();
    Net.getRestClient().resourceHomeItems({'categoryIds': categoryIds}).then((onValue) {
      resourceList.value = onValue.data;
    });
  }

  void onItemClick(int index) {
    var resource = resourceList[index];
    
    // 如果是选择模式，直接返回选中的资源
    if (isSelectionMode) {
      Get.back(result: {
        'resourceId': resource.id,
        'name': resource.name,
        'cover': resource.cover,
      });
      return;
    }
    
    // 正常模式，进入详情页或系列页
    if (resource.contentType == 1) {
      Get.toNamed(Routes.SERIES, arguments: {
        'seriesId': resource.id,
        'isSelectionMode': isSelectionMode,
      });
    } else {
      RoutesUtil().goVideoDetailByRemote(resource.videoUrl, "", resource.id);
    }
  }

  void onRecommendClick(int index) {
    var resource = recommendList[index];
    
    // 如果是选择模式，直接返回选中的资源
    if (isSelectionMode) {
      Get.back(result: {
        'resourceId': resource.id,
        'name': resource.id, // Recommend没有name字段，暂时使用id
        'cover': resource.cover,
      });
      return;
    }
    
    // 正常模式，进入详情页或系列页
    if (resource.contentType == 1) {
      Get.toNamed(Routes.SERIES, arguments: {
        'seriesId': resource.id,
        'isSelectionMode': isSelectionMode,
      });
    } else {
      RoutesUtil().goVideoDetailByRemote(resource.videoUrl, "", resource.id);
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    for (var controller in tabControllers) {
      controller.dispose();
    }
  }
}
