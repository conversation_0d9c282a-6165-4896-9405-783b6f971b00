import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';

import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_stage_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_week_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_day_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/plan_resource_resp.dart';

import '../controllers/plan_controller.dart';

// 添加动画组件 - 整个日计划卡片
class AnimatedDayCard extends StatefulWidget {
  final PlanDayResp day;
  final PlanResourceResp? resource;
  final int currentDaySentences;
  final VoidCallback onStartLearning;

  const AnimatedDayCard({
    super.key,
    required this.day,
    this.resource,
    required this.currentDaySentences,
    required this.onStartLearning,
  });

  @override
  State<AnimatedDayCard> createState() => _AnimatedDayCardState();
}

class _AnimatedDayCardState extends State<AnimatedDayCard> with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _bounceController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;

  int _previousSentences = 0;

  // Overlay 相关
  OverlayEntry? _overlayEntry;
  late final GlobalKey _cardKey;
  RenderBox? _renderBox;
  Offset? _cardPosition;
  Size? _cardSize;

  @override
  void initState() {
    super.initState();
    _previousSentences = widget.currentDaySentences;

    // 为每个实例创建唯一的 GlobalKey，使用 day.id 确保唯一性
    _cardKey = GlobalKey(debugLabel: 'AnimatedDayCard_${widget.day.id}');

    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // 弹跳动画控制器
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // 缩放动画 - 使用弹性效果
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // 弹跳动画 - 添加轻微的上下弹跳
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.bounceOut,
    ));
  }

  @override
  void didUpdateWidget(AnimatedDayCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当句子数量发生变化时触发动画
    if (widget.currentDaySentences != _previousSentences && widget.currentDaySentences > _previousSentences) {
      // 不立即更新 _previousSentences，等动画放大后停留期间再更新
      _triggerAnimation();
    }
  }

  void _triggerAnimation() {
    // 首先滚动到指定位置，确保卡片在可视区域内
    _scrollToCardPosition().then((_) {
      // 滚动完成后，开始动画
      _startAnimation();
    });
  }

  Future<void> _scrollToCardPosition() async {
    // 延迟执行，确保UI已经构建完成
    await Future.delayed(Duration.zero);

    // 获取滚动控制器（通过 Get.find 获取 PlanController）
    final planController = Get.find<PlanController>();
    final scrollController = planController.scrollController;

    if (scrollController.hasClients && _cardKey.currentContext != null) {
      // 获取卡片在屏幕上的位置
      final renderBox = _cardKey.currentContext!.findRenderObject() as RenderBox;
      final cardPosition = renderBox.localToGlobal(Offset.zero);
      final cardSize = renderBox.size;

      // 获取屏幕高度和顶部偏移
      final screenHeight = MediaQuery.of(context).size.height;
      final topPadding = MediaQuery.of(context).padding.top;
      final availableHeight = screenHeight - topPadding;

      // 计算卡片的可见区域
      final cardTop = cardPosition.dy;
      final cardBottom = cardTop + cardSize.height;

      // 计算可视区域的边界（考虑顶部padding）
      final visibleTop = topPadding;
      final visibleBottom = screenHeight;

      // 检查卡片是否完全在可视区域内
      final isFullyVisible = cardTop >= visibleTop && cardBottom <= visibleBottom;

      // 如果卡片已经完整显示，不需要滚动
      if (isFullyVisible) {
        return;
      }

      // 计算目标滚动位置，使卡片居中显示
      final targetOffset = scrollController.offset + cardPosition.dy - (availableHeight / 2) + (cardSize.height / 2);

      // 确保滚动位置在有效范围内
      final clampedOffset = targetOffset.clamp(0.0, scrollController.position.maxScrollExtent);

      // 执行滚动动画并等待完成
      await scrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _startAnimation() {
    // 获取卡片的位置和大小
    _getCardPosition();

    // 重置所有动画控制器
    _scaleController.reset();
    _bounceController.reset();

    // 延迟一帧后开始动画，确保位置信息已获取
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 开始缩放动画
      _scaleController.forward().then((_) {
        // 缩放完成后，立即开始弹跳动画
        _bounceController.forward();

        // 停留300毫秒后更新文本
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            // 改变句子数量文本
            _previousSentences = widget.currentDaySentences;
            // 触发重建以显示新的句子数量
            setState(() {});
            // 强制更新 Overlay 中的内容
            _overlayEntry?.markNeedsBuild();

            // 再停留300毫秒后开始结束动画
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                // 然后开始缩小动画，使用弹性效果
                _scaleController.reverse().then((_) {
                  // 动画结束后移除 Overlay
                  _removeOverlay();
                });
              }
            });
          }
        });
      });
    });
  }

  void _getCardPosition() {
    // 延迟一帧获取位置，确保组件已经渲染
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_cardKey.currentContext != null) {
        _renderBox = _cardKey.currentContext!.findRenderObject() as RenderBox;
        _cardPosition = _renderBox!.localToGlobal(Offset.zero);
        _cardSize = _renderBox!.size;
      }
    });
  }

  void _showOverlay() {
    if (_cardPosition == null || _cardSize == null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: _cardPosition!.dx,
        top: _cardPosition!.dy,
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: _cardSize!.width,
            height: _cardSize!.height,
            child: _buildOverlayContent(),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _removeOverlay();
    _scaleController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _bounceAnimation]),
      builder: (context, child) {
        // 当缩放动画开始时，显示 Overlay
        if (_scaleAnimation.value > 1.0 && _overlayEntry == null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showOverlay();
          });
        }

        // 原始组件在动画期间保持透明，但延迟一点时间再变透明
        double opacity = 1.0;
        if (_scaleAnimation.value > 1.05) {
          // 延迟到1.05才开始变透明
          opacity = 0.0;
        }

        return Opacity(
          opacity: opacity,
          child: Container(
            key: _cardKey,
            child: _buildDayCardContent(),
          ),
        );
      },
    );
  }

  Widget _buildDayCardContent() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.whs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 时间轴圆点
          Padding(
            padding: EdgeInsets.only(right: 10.whs),
            child: Center(
              child: TimelineDot(
                isCompleted: widget.day.status == 2,
                isCurrent: true,
                color: Colors.green,
              ),
            ),
          ),

          // 日计划卡片
          Expanded(
            child: Container(
              padding: EdgeInsets.all(16.whs),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.whs),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildTodayContent(),
            ),
          ),
        ],
      ),
    );
  }

  // 为 Overlay 创建的内容构建方法，只缩放白色卡片部分
  Widget _buildOverlayContent() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.whs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 时间轴圆点 - 不缩放
          Padding(
            padding: EdgeInsets.only(right: 10.whs),
            child: Center(
              child: TimelineDot(
                isCompleted: false,
                isCurrent: true,
                color: Colors.green,
              ),
            ),
          ),

          // 日计划卡片 - 只缩放这部分
          Expanded(
            child: AnimatedBuilder(
              animation: Listenable.merge([_scaleAnimation, _bounceAnimation]),
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  alignment: Alignment.centerLeft, // 改为左侧垂直中心点
                  child: Transform.translate(
                    offset: Offset(
                      0, // 不需要水平偏移，因为中心点已经在左侧
                      -20.whs * (_scaleAnimation.value - 1.0) + 5.whs * _bounceAnimation.value, // 添加弹跳效果
                    ),
                    child: Transform.rotate(
                      angle: 0.02 * _bounceAnimation.value, // 轻微的旋转效果
                      child: Container(
                        padding: EdgeInsets.all(16.whs),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.whs),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1 + _bounceAnimation.value * 0.15), // 增强动态阴影
                              blurRadius: 12 + _bounceAnimation.value * 8, // 增强动态模糊
                              offset: Offset(0, 4 + _bounceAnimation.value * 2), // 动态偏移
                            ),
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05 + _bounceAnimation.value * 0.1), // 第二层阴影
                              blurRadius: 6 + _bounceAnimation.value * 4,
                              offset: Offset(0, 2 + _bounceAnimation.value * 1),
                            ),
                          ],
                        ),
                        child: _buildTodayContent(),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayContent() {
    // 如果是已完成状态，使用简化版本
    if (widget.day.status == 2) {
      return _buildCompletedContent();
    }

    // 当前天的完整版本
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 日期信息
        Text(
          '${widget.day.studyTimestamp != null ? DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(widget.day.studyTimestamp!)) : ''} - Today',
          style: Get.textTheme.bodyMedium,
        ),
        Gap(4.whs),

        // 日标题
        Text(
          'Day${widget.day.weekDayNumber}',
          style: Get.textTheme.headlineMedium,
        ),
        Gap(12.whs),
        Row(
          children: [
            Flexible(
              child: Text(
                "$_previousSentences/${widget.day.targetSentences} sent.",
                style: Get.textTheme.titleMedium,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        Gap(8.whs),
        AspectRatio(
          aspectRatio: 16 / 9,
          child: Container(
            clipBehavior: Clip.hardEdge,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.whs),
              color: Colors.grey[200],
            ),
            child: AspectRatio(
              aspectRatio: 2,
              child: Stack(
                children: [
                  Positioned.fill(child: ImageLoader(widget.resource?.resourceCover ?? '')),
                  Center(child: ImageLoader(R.play, color: Colors.white)),
                  Positioned.fill(child: Container(color: Colors.black.withValues(alpha: 0.1))),
                  Positioned(
                    bottom: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        widget.resource?.resourceName ?? '',
                        style: Get.textTheme.titleMedium?.copyWith(color: Colors.white),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Gap(10.whs),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: widget.onStartLearning,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('开始学习'),
          ),
        ),
      ],
    );
  }

  /// 构建已完成卡片的内容（简化版本）
  Widget _buildCompletedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主要内容区域
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.day.studyTimestamp != null
                        ? DateFormat('MM,dd').format(DateTime.fromMillisecondsSinceEpoch(widget.day.studyTimestamp!))
                        : '',
                    style: TextStyle(
                      fontSize: 12.whs,
                      color: Colors.grey[600],
                    ),
                  ),
                  Gap(4.whs),
                  Text(
                    'Day${widget.day.weekDayNumber}',
                    style: Get.textTheme.headlineMedium,
                  ),
                  Gap(4.whs),
                  Text(
                    "$_previousSentences/${widget.day.targetSentences} sent.",
                    style: Get.textTheme.bodyLarge,
                  ),
                  Text(
                    widget.resource?.resourceName ?? '',
                    style: Get.textTheme.bodyLarge,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // 右侧封面（带蒙版和打勾图标）
            Container(
              width: 86.whs,
              height: 86.whs,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.whs),
                child: Stack(
                  children: [
                    // 背景图片
                    Positioned.fill(
                      child: ImageLoader(
                        widget.resource?.resourceCover ?? '',
                        size: 86.whs,
                      ),
                    ),
                    // 蒙版
                    Positioned.fill(
                      child: Container(
                        color: Colors.black.withValues(alpha: 0.3),
                      ),
                    ),
                    // 右上角打勾图标
                    Center(
                      child: Container(
                        width: 24.whs,
                        height: 24.whs,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Get.theme.primaryColor,
                        ),
                        child: Icon(
                          Icons.check,
                          size: 16.whs,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        Gap(12.whs),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: widget.onStartLearning,
            child: const Text('学习'),
          ),
        ),
      ],
    );
  }
}

/// 时间轴圆点组件
class TimelineDot extends StatelessWidget {
  final bool isCompleted;
  final bool isRest;
  final bool isCurrent;
  final Color? color;
  final Widget? child;

  const TimelineDot({
    super.key,
    this.isCompleted = false,
    this.isRest = false,
    this.isCurrent = false,
    this.color,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    // 统一容器尺寸为32x32，确保对齐
    return Container(
      width: 32.whs,
      height: 32.whs,
      child: Center(
        child: _buildDot(),
      ),
    );
  }

  Widget _buildDot() {
    // 休息日：纯色圆
    if (isRest) {
      return Container(
        width: 20.whs,
        height: 20.whs,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color ?? Colors.blue.shade300,
        ),
      );
    }

    // 完成过的：纯色圆
    if (isCompleted) {
      return Container(
        width: 20.whs,
        height: 20.whs,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color ?? Colors.green,
        ),
      );
    }

    // 当天的：有打勾图标
    if (isCurrent) {
      return Container(
        width: 20.whs,
        height: 20.whs,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color ?? Colors.green,
        ),
        child: Icon(
          Icons.check,
          size: 14.whs,
          color: Colors.white,
        ),
      );
    }

    // 其他情况：默认样式（渐变圆环）
    return Container(
      width: 28.whs,
      height: 28.whs,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            (color ?? Colors.grey[400]!).withValues(alpha: 0.3),
            (color ?? Colors.grey[400]!).withValues(alpha: 0.1),
            Colors.transparent,
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
      ),
      child: Center(
        child: Container(
          width: 16.whs,
          height: 16.whs,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            border: Border.all(
              color: color ?? Colors.grey[300]!,
              width: 2,
            ),
          ),
          child: child,
        ),
      ),
    );
  }
}

class PlanView extends GetView<PlanController> {
  const PlanView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF6F6F6),
      body: Padding(
        padding: EdgeInsets.only(top: 54.whs),
        child: Column(
          children: [
            Expanded(
              child: Obx(() {
                final plan = controller.currentPlan.value;
                if (plan == null) {
                  return _buildEmptyState();
                }
                return _buildPlanContent(plan);
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64.whs,
            color: Colors.grey[400],
          ),
          Gap(16.whs),
          Text(
            '暂无学习计划',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          Gap(8.whs),
          Text(
            '点击下方按钮生成您的专属学习计划',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          Gap(24.whs),
          // 生成计划按钮
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.whs),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: controller.isLoading.value ? null : controller.showSelectPlanDaysSheet,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Get.theme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.whs),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.whs),
                  ),
                ),
                child: controller.isLoading.value
                    ? SizedBox(
                        height: 16.whs,
                        width: 16.whs,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('生成学习计划'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanContent(LearningPlanResp plan) {
    return SingleChildScrollView(
      controller: controller.scrollController,
      padding: EdgeInsets.symmetric(horizontal: 20.whs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 计划标题
          _buildPlanTitle(plan),
          Gap(24.whs),

          // 学习阶段列表
          if (plan.stages != null) ...[
            ..._buildStagesWithRestDay(plan),
          ],
        ],
      ),
    );
  }

  /// 构建包含休息日的阶段列表
  List<Widget> _buildStagesWithRestDay(LearningPlanResp plan) {
    // 直接返回正常的阶段列表，休息日已经包含在数据中
    return plan.stages!.map((stage) => _buildStageSection(plan, stage)).toList();
  }

  Widget _buildPlanTitle(LearningPlanResp plan) {
    return GestureDetector(
      onTap: controller.showAdjustPlanSheet,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${plan.totalLearnDays}-Day 开口说英语',
            style: Get.textTheme.headlineMedium,
          ),
          Gap(16.whs),
          Icon(
            Icons.edit,
            size: 20.whs,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  Widget _buildStageSection(LearningPlanResp plan, PlanStageResp stage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 周列表
        ...stage.weeks.map((week) => _buildWeekSection(plan, week)),
        Gap(32.whs),
      ],
    );
  }

  Widget _buildWeekSection(LearningPlanResp plan, PlanWeekResp week) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 周标题
        _buildWeekHeader(plan, week),
        Gap(16.whs),

        // 日计划时间轴
        _buildDaysTimeline(week),
        Gap(24.whs),
      ],
    );
  }

  Widget _buildWeekHeader(LearningPlanResp plan, PlanWeekResp week) {
    // 计算周的时间范围
    String dateRange = _calculateWeekDateRange(week);

    // 计算本周的进度
    int currentProgress = _calculateWeekProgress(plan, week);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Week${week.weekNumber}',
          style: TextStyle(
            fontSize: 18.whs,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Gap(4.whs),
        Text(
          dateRange,
          style: Get.textTheme.bodyMedium,
        ),
        Gap(8.whs),
        // 进度指示器
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ...List.generate(
                week.days.length,
                (index) => Container(
                      margin: EdgeInsets.only(right: 4.whs),
                      width: 8.whs,
                      height: 8.whs,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index < currentProgress ? Colors.green : Colors.grey[300],
                      ),
                    )),
            Gap(8.whs),
            Text(
              '$currentProgress/${week.days.length}',
              style: TextStyle(
                fontSize: 12.whs,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _calculateWeekDateRange(PlanWeekResp week) {
    if (week.days.isEmpty) {
      return 'No dates available';
    }

    // 过滤出有 studyDate 的天数
    final daysWithDate = week.days.where((day) => day.studyTimestamp != null && day.studyTimestamp! > 0).toList();

    if (daysWithDate.isEmpty) {
      return 'No dates available';
    }

    // 解析日期并找到最早和最晚的日期
    DateTime? earliestDate;
    DateTime? latestDate;

    for (final day in daysWithDate) {
      try {
        final date = DateTime.fromMillisecondsSinceEpoch(day.studyTimestamp!);
        if (earliestDate == null || date.isBefore(earliestDate)) {
          earliestDate = date;
        }
        if (latestDate == null || date.isAfter(latestDate)) {
          latestDate = date;
        }
      } catch (e) {
        // 如果日期解析失败，跳过这一天
        continue;
      }
    }

    if (earliestDate == null || latestDate == null) {
      return 'Invalid date format';
    }

    // 格式化日期显示
    String formatDate(DateTime date) {
      return DateFormat('MM,dd').format(date);
    }

    final startDate = formatDate(earliestDate);
    final endDate = formatDate(latestDate);

    // 如果开始和结束日期相同，只显示一个日期
    if (earliestDate.isAtSameMomentAs(latestDate)) {
      return startDate;
    }

    return '$startDate - $endDate';
  }

  /// 计算本周的进度
  int _calculateWeekProgress(LearningPlanResp plan, PlanWeekResp week) {
    // 统计本周已完成的天数（状态为2），不包括休息日
    int completedDays = 0;

    for (final day in week.days) {
      // 只有状态为2（已完成）且不是休息日的天数才能算作进度
      if (day.status == 2 && day.type != 2) {
        completedDays++;
      }
    }

    return completedDays;
  }

  Widget _buildDaysTimeline(PlanWeekResp week) {
    return Stack(
      children: [
        // 时间轴虚线
        Positioned(
          left: 16.whs,
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                  style: BorderStyle.solid,
                ),
              ),
            ),
          ),
        ),

        // 日计划卡片
        Column(
          children: week.days.map((day) => _buildDayCard(day)).toList(),
        ),
      ],
    );
  }

  Widget _buildDayCard(PlanDayResp day) {
    // 判断是否为休息日
    final isRestDay = day.type == 2;

    // 如果是休息日，显示休息日卡片
    if (isRestDay) {
      return _buildRestDayCard(day);
    }

    // 判断是否为当前天数
    final plan = controller.currentPlan.value;
    final isCurrentDay = plan?.currentDayTimestamp == day.studyTimestamp;

    // 如果是当前天或者状态为2（已完成），使用动画组件包装整个卡片
    if (isCurrentDay || day.status == 2) {
      return AnimatedDayCard(
        key: ValueKey('day_card_${day.id}'), // 使用统一的 key 格式
        day: day,
        resource: day.resources?.first,
        currentDaySentences: controller.currentDaySentences.value,
        onStartLearning: () => controller.startLearning(day),
      );
    }

    // 非当前天使用普通卡片
    return Container(
      key: ValueKey('day_card_${day.id}'), // 使用统一的 key 格式，而不是 controller.getDayKey
      margin: EdgeInsets.only(bottom: 16.whs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 时间轴圆点
          Padding(
            padding: EdgeInsets.only(right: 10.whs),
            child: Center(
              child: TimelineDot(
                isCompleted: day.status == 2,
                isCurrent: false,
              ),
            ),
          ),

          // 日计划卡片
          Expanded(
            child: Container(
              padding: EdgeInsets.all(16.whs),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.whs),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildResourcePreview(day.resources?.first, day, isCurrentDay: false),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建休息日卡片
  Widget _buildRestDayCard(PlanDayResp day) {
    final weekDayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    // 根据studyTimestamp计算周几
    String weekDayName = '';
    if (day.studyTimestamp != null) {
      final dateTime = DateTime.fromMillisecondsSinceEpoch(day.studyTimestamp!);
      final weekday = dateTime.weekday; // 1=Monday, 7=Sunday
      weekDayName = weekDayNames[weekday - 1];
    }

    return Container(
      key: ValueKey('rest_day_card_${day.id}'),
      margin: EdgeInsets.only(bottom: 16.whs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 休息日时间轴圆点
          Padding(
            padding: EdgeInsets.only(right: 10.whs),
            child: Center(
              child: TimelineDot(
                isRest: true,
                isCurrent: false,
                color: Get.theme.primaryColor,
              ),
            ),
          ),

          // 休息日卡片
          Expanded(
            child: Container(
              padding: EdgeInsets.all(16.whs),
              decoration: BoxDecoration(
                color: Get.theme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16.whs),
                border: Border.all(
                  color: Get.theme.primaryColor,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        weekDayName,
                        style: TextStyle(
                          fontSize: 12.whs,
                          color: Colors.grey[600],
                        ),
                      ),
                      Gap(4.whs),
                      Text(
                        '休息日',
                        style: Get.textTheme.headlineMedium?.copyWith(
                          color: Get.theme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Gap(4.whs),
                      Text(
                        '休息一下，恢复能量！',
                        style: Get.textTheme.bodyMedium?.copyWith(
                          color: Get.theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    Icons.local_drink_outlined,
                    size: 48.whs,
                    color: Get.theme.primaryColor,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResourcePreview(PlanResourceResp? resource, PlanDayResp day, {bool isCurrentDay = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              day.studyTimestamp != null ? DateFormat('MM,dd').format(DateTime.fromMillisecondsSinceEpoch(day.studyTimestamp!)) : '',
              style: TextStyle(
                fontSize: 12.whs,
                color: Colors.grey[600],
              ),
            ),
            Gap(4.whs),
            Text(
              'Day${day.weekDayNumber}',
              style: Get.textTheme.headlineMedium,
            ),
            Gap(4.whs),
            Text(
              isCurrentDay ? "${day.currentSentences}/${day.targetSentences} sent." : "${day.currentSentences}/${day.targetSentences} sent.",
              style: Get.textTheme.bodyLarge,
            ),
            Text(
              day.resources?.firstOrNull?.resourceName ?? '',
              style: Get.textTheme.bodyLarge,
            ),
          ],
        ),
        ClipRRect(
          borderRadius: BorderRadius.circular(8.whs),
          child: ImageLoader(resource?.resourceCover ?? '', size: 86.whs),
        ),
      ],
    );
  }
}
