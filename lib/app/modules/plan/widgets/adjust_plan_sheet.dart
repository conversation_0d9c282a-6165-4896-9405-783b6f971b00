import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/resource_lib_home_resp/resource_lib_home_item_resp.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';
import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/widgets/loading_button.dart';

class AdjustPlanSheet {
  // 周一到周日的名称（中文）
  static const List<String> weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

  // 存储选中的资源
  static final Rx<ResourceLibHomeItemResp?> _selectedResource = Rx<ResourceLibHomeItemResp?>(null);

  /// 显示调整计划弹窗
  static void show(
    BuildContext context, {
    required LearningPlanResp plan,
    Function(LearningPlanResp?)? onPlanUpdated,
  }) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (BuildContext context) {
        return [
          WoltModalSheetPage(
            id: 'base',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            forceMaxHeight: true,
            trailingNavBarWidget: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: EdgeInsets.only(left: 16.whs),
              child: Text(
                '调整计划',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            child: _buildAdjustPlanContent(plan),
          ),
          // 学习日选择页面
          WoltModalSheetPage(
            id: 'studyDays',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            forceMaxHeight: true,
            trailingNavBarWidget: IconButton(
              onPressed: () => WoltModalSheet.of(context).showPageWithId('base'),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: EdgeInsets.only(left: 16.whs),
              child: Text(
                '学习日',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            child: _buildStudyDaysSelectionContent(plan, onPlanUpdated),
          ),
          // 每日学习时间选择页面
          WoltModalSheetPage(
            id: 'dailySentences',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            trailingNavBarWidget: IconButton(
              onPressed: () => WoltModalSheet.of(context).showPageWithId('base'),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: EdgeInsets.only(left: 16.whs),
              child: Text(
                '每日学习时间',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            child: _buildDailyStudyTimeSelectionContent(plan, onPlanUpdated),
          ),
          // 资源选择确认页面
          WoltModalSheetPage(
            id: 'resource',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            forceMaxHeight: true,
            trailingNavBarWidget: IconButton(
              onPressed: () => WoltModalSheet.of(context).showPageWithId('base'),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: EdgeInsets.only(left: 16.whs),
              child: Text(
                '选择资源',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            stickyActionBar: _buildResourceSelectionStickyActionBar(onPlanUpdated),
            child: _buildResourceSelectionContent(),
          ),
          // 重新生成计划确认页面
          WoltModalSheetPage(
            id: 'resetPlan',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            trailingNavBarWidget: IconButton(
              onPressed: () => WoltModalSheet.of(context).showPageWithId('base'),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: EdgeInsets.only(left: 16.whs),
              child: Text(
                '重新生成计划',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            child: _buildResetPlanConfirmationContent(onPlanUpdated),
          ),
          // 选择计划天数页面
          WoltModalSheetPage(
            id: 'selectDays',
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            trailingNavBarWidget: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
            leadingNavBarWidget: Padding(
              padding: EdgeInsets.only(left: 16.whs),
              child: Text(
                '选择学习天数',
                style: Get.textTheme.headlineMedium,
              ),
            ),
            child: _buildSelectPlanDaysContent(onPlanUpdated),
          ),
        ];
      },
    );
  }

  /// 构建调整计划主页面内容
  static Widget _buildAdjustPlanContent(
    LearningPlanResp plan,
  ) {
    return Builder(
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 选项列表
              _buildOptionRow(
                label: '学习日',
                value: _getStudyDaysText(plan.studyDaysOfWeek),
                onTap: () {
                  // 导航到学习日选择页面
                  WoltModalSheet.of(context).showNext();
                },
              ),
              const SizedBox(height: 16),
              _buildOptionRow(
                label: '每日学习时间',
                value: '${plan.dailySentences ?? 15} 句/天',
                onTap: () {
                  // 导航到每日学习时间选择页面
                  WoltModalSheet.of(context).showPageWithId('dailySentences');
                },
              ),
              const SizedBox(height: 16),
              _buildOptionRow(
                label: '学习资源',
                value: plan.stages?.firstOrNull?.weeks.firstOrNull?.days.firstOrNull?.resources?.firstOrNull?.resourceName ?? '',
                onTap: () async {
                  // 打开资源库页面进行选择
                  final result = await _openResourceSelection(context);
                  if (result != null) {
                    // 将返回的资源信息转换为ResourceLibHomeItemResp对象
                    final resource = ResourceLibHomeItemResp(
                      id: result['resourceId'],
                      name: result['name'],
                      cover: result['cover'],
                    );
                    // 导航到资源选择确认页面
                    WoltModalSheet.of(context).showPageWithId('resource');
                    // 存储选中的资源，供确认页面使用
                    _selectedResource.value = resource;
                  }
                },
              ),
              Gap(16.whs),
              _buildOptionRow(
                label: '重置计划',
                value: '重新生成计划',
                onTap: () {
                  // 导航到重新生成计划确认页面
                  WoltModalSheet.of(context).showPageWithId('resetPlan');
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 打开资源选择页面
  static Future<Map<String, dynamic>?> _openResourceSelection(BuildContext context) async {
    final result = await Get.toNamed(
      Routes.RESOURCELIB,
      arguments: {
        'isSelectionMode': true,
      },
    );
    return result as Map<String, dynamic>?;
  }

  /// 构建学习日选择页面内容
  static Widget _buildStudyDaysSelectionContent(LearningPlanResp plan, Function(LearningPlanResp?)? onPlanUpdated) {
    final selectedStudyDays = <int>[].obs;

    // 初始化选中的学习日
    selectedStudyDays.value = List<int>.from(plan.studyDaysOfWeek);

    return Builder(
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 周一到周日选择列表
              ...List.generate(weekDays.length, (index) {
                final dayIndex = index + 1; // 1-7 表示周一到周日
                return Obx(() => _buildDaySelectionItem(
                      dayName: weekDays[index],
                      dayIndex: dayIndex,
                      isSelected: selectedStudyDays.contains(dayIndex),
                      onTap: () => _toggleDaySelection(selectedStudyDays, dayIndex),
                    ));
              }),
              const SizedBox(height: 30),
              // 确认按钮
              LoadingButton.text(
                text: '确认',
                buttonColor: Get.theme.primaryColor,
                onAsyncPressed: () async {
                  // 调用更新学习日接口
                  final success = await _updateStudyDays(selectedStudyDays);
                  if (success) {
                    Navigator.of(context).pop();
                    // 获取最新的计划数据并回调
                    final updatedPlan = await _getCurrentPlan();
                    onPlanUpdated?.call(updatedPlan);
                  }
                  return success;
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建选项行
  static Widget _buildOptionRow({
    required String label,
    required String value,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建单个日期选择项
  static Widget _buildDaySelectionItem({
    required String dayName,
    required int dayIndex,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Get.theme.primaryColor.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Get.theme.primaryColor : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                dayName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Get.theme.primaryColor : Colors.black87,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Get.theme.primaryColor,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  /// 切换日期选择状态
  static void _toggleDaySelection(RxList<int> selectedStudyDays, int dayIndex) {
    if (selectedStudyDays.contains(dayIndex)) {
      selectedStudyDays.remove(dayIndex);
    } else {
      selectedStudyDays.add(dayIndex);
    }
  }

  /// 获取学习日文本显示
  static String _getStudyDaysText(List<int>? studyDaysOfWeek) {
    if (studyDaysOfWeek == null || studyDaysOfWeek.isEmpty) {
      return '未设置';
    } else {
      final selectedDayNames = studyDaysOfWeek.map((dayIndex) => weekDays[dayIndex - 1]).toList();
      return selectedDayNames.join(', ');
    }
  }

  /// 更新学习日接口调用
  static Future<bool> _updateStudyDays(RxList<int> selectedStudyDays) async {
    try {
      // 对学习日进行排序后传给后端
      final sortedStudyDays = selectedStudyDays.toList()..sort();
      final response = await Net.getRestClient().updateStudyDays({
        'studyDaysOfWeek': sortedStudyDays,
      });

      if (response.data != null) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// 构建每日学习时间选择页面内容
  static Widget _buildDailyStudyTimeSelectionContent(LearningPlanResp plan, Function(LearningPlanResp?)? onPlanUpdated) {
    final selectedDailySentences = (plan.dailySentences ?? 15).obs;

    return Builder(
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 当前选择的值显示
              Obx(() => Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Get.theme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Get.theme.primaryColor,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${selectedDailySentences.value} 句/天',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Get.theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  )),
              const SizedBox(height: 30),
              // 拖动条
              Obx(() => Column(
                    children: [
                      SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: Get.theme.primaryColor,
                          inactiveTrackColor: Colors.grey[300],
                          thumbColor: Get.theme.primaryColor,
                          overlayColor: Get.theme.primaryColor.withOpacity(0.2),
                          trackHeight: 4,
                          thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                          overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
                        ),
                        child: Slider(
                          value: selectedDailySentences.value.toDouble(),
                          min: 10,
                          max: 50,
                          divisions: 8, // 10, 15, 20, 25, 30, 35, 40, 45, 50
                          onChanged: (value) {
                            selectedDailySentences.value = value.round();
                          },
                        ),
                      ),
                      const SizedBox(height: 10),
                      // 拖动条刻度标签
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '10句',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '50句',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  )),
              const SizedBox(height: 20),
              // 推荐选项提示
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue[200]!,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: Colors.blue[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '建议每日学习 15-25 句，保持学习节奏的同时不会感到压力',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              // 确认按钮
              LoadingButton.text(
                text: '确认',
                buttonColor: Get.theme.primaryColor,
                onAsyncPressed: () async {
                  // 调用更新每日学习句数接口
                  final success = await _updateDailySentences(selectedDailySentences.value);
                  if (success) {
                    Navigator.of(context).pop();
                    // 获取最新的计划数据并回调
                    final updatedPlan = await _getCurrentPlan();
                    onPlanUpdated?.call(updatedPlan);
                  }
                  return success;
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 更新每日学习句数接口调用
  static Future<bool> _updateDailySentences(int dailySentences) async {
    try {
      final response = await Net.getRestClient().updateDailySentences({
        'dailySentences': dailySentences,
      });

      if (response.data != null) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// 构建资源选择确认页面内容
  static Widget _buildResourceSelectionContent() {
    return Builder(
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 资源封面和名称
              Obx(() {
                final resource = _selectedResource.value;
                if (resource == null) {
                  return const Center(
                    child: Text('未选择资源'),
                  );
                }

                return Column(
                  children: [
                    // 封面图片
                    Container(
                      width: double.infinity,
                      height: 200,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: resource.cover != null && resource.cover!.isNotEmpty
                            ? ImageLoader(resource.cover!)
                            : Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  size: 50,
                                  color: Colors.grey,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // 资源名称
                    Text(
                      resource.name ?? '未知资源',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                );
              }),
            ],
          ),
        );
      },
    );
  }

  /// 构建资源选择确认页面的粘性操作栏
  static Widget _buildResourceSelectionStickyActionBar(Function(LearningPlanResp?)? onPlanUpdated) {
    return Builder(
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: LoadingButton.text(
            text: '选择此资源',
            buttonColor: Get.theme.primaryColor,
            borderRadius: 12,
            onAsyncPressed: () async {
              final resource = _selectedResource.value;
              if (resource != null) {
                try {
                  await Net.getRestClient().updateResourceInPlan({
                    'resourceId': resource.id,
                  });
                  Navigator.of(context).pop();
                  // 获取最新的计划数据并回调
                  final updatedPlan = await _getCurrentPlan();
                  onPlanUpdated?.call(updatedPlan);
                  return true;
                } catch (e) {
                  return false;
                }
              }
              return false;
            },
          ),
        );
      },
    );
  }

  /// 构建重新生成计划确认页面内容
  static Widget _buildResetPlanConfirmationContent(Function(LearningPlanResp?)? onPlanUpdated) {
    return Builder(
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 标题
              Text(
                '是否重新生成？',
                style: Get.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              // 确认按钮
              LoadingButton.text(
                text: '重新生成计划',
                buttonColor: Get.theme.primaryColor,
                borderRadius: 12,
                onAsyncPressed: () async {
                  // 导航到选择天数页面
                  WoltModalSheet.of(context).showPageWithId('selectDays');
                  return true;
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 获取最新的学习计划
  static Future<LearningPlanResp?> _getCurrentPlan() async {
    try {
      final response = await Net.getRestClient().currentPlan();
      return response.data;
    } catch (e) {
      return null;
    }
  }

  /// 显示选择计划天数弹窗（单独显示）
  static void showSelectPlanDaysSheet(BuildContext context, {Function(LearningPlanResp?)? onPlanUpdated}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _SelectPlanDaysSheet(
        onDaysSelected: (int days) async {
          // 调用生成计划接口
          await _generateFixedPlan(days);
          // 获取最新计划数据并回调
          final updatedPlan = await _getCurrentPlan();
          onPlanUpdated?.call(updatedPlan);
        },
      ),
    );
  }

  /// 构建选择计划天数页面内容
  static Widget _buildSelectPlanDaysContent(Function(LearningPlanResp?)? onPlanUpdated) {
    return Builder(
      builder: (context) {
        return Padding(
          padding: EdgeInsets.all(20.whs),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(8.whs),
              Text(
                '请选择您想要的学习计划天数',
                style: Get.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Gap(24.whs),

              // 天数选择选项
              _buildDaysOptionForPage(context, 7, '7天', '适合初学者，快速体验', onPlanUpdated),
              Gap(12.whs),
              _buildDaysOptionForPage(context, 14, '14天', '标准计划，循序渐进', onPlanUpdated),
              Gap(12.whs),
              _buildDaysOptionForPage(context, 28, '28天', '完整计划，全面提升', onPlanUpdated),
              Gap(24.whs),
            ],
          ),
        );
      },
    );
  }

  /// 构建天数选择选项（用于WoltModalSheetPage）
  static Widget _buildDaysOptionForPage(BuildContext context, int days, String title, String subtitle, Function(LearningPlanResp?)? onPlanUpdated) {
    return GestureDetector(
      onTap: () async {
        Navigator.of(context).pop();
        // 调用生成计划接口
        await _generateFixedPlan(days);
        // 获取最新计划数据并回调
        final updatedPlan = await _getCurrentPlan();
        onPlanUpdated?.call(updatedPlan);
      },
      child: Container(
        padding: EdgeInsets.all(16.whs),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12.whs),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 天数图标
            Container(
              width: 48.whs,
              height: 48.whs,
              decoration: BoxDecoration(
                color: Get.theme.primaryColor,
                borderRadius: BorderRadius.circular(12.whs),
              ),
              child: Center(
                child: Text(
                  '$days',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            Gap(16.whs),
            // 文字信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Gap(4.whs),
                  Text(
                    subtitle,
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              size: 16.whs,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  /// 生成固定学习计划
  static Future<void> _generateFixedPlan(int totalLearnDays) async {
    try {
      final studyDaysOfWeek = [1, 2, 3, 4, 5, 6, 7]..sort();
      await Net.getRestClient().generateFixedPlan({
        'currentLevel': 'A0',
        'targetLevel': 'B1',
        'dailyStudyMinutes': 20,
        'studyDaysOfWeek': studyDaysOfWeek,
        'dailySentences': 35,
        'forceRegenerate': true,
        'totalLearnDays': totalLearnDays,
      });
    } catch (e) {
      // 处理错误
    }
  }
}

/// 内部的选择计划天数组件
class _SelectPlanDaysSheet extends StatelessWidget {
  final Function(int days) onDaysSelected;

  const _SelectPlanDaysSheet({
    required this.onDaysSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20.whs),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '选择学习计划天数',
                    style: Get.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              Gap(8.whs),
              Text(
                '请选择您想要的学习计划天数',
                style: Get.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Gap(24.whs),

              // 天数选择选项
              _buildDaysOption(context, 7, '7天', '适合初学者，快速体验'),
              Gap(12.whs),
              _buildDaysOption(context, 14, '14天', '标准计划，循序渐进'),
              Gap(12.whs),
              _buildDaysOption(context, 28, '28天', '完整计划，全面提升'),
              Gap(24.whs),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDaysOption(BuildContext context, int days, String title, String subtitle) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        onDaysSelected(days);
      },
      child: Container(
        padding: EdgeInsets.all(16.whs),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12.whs),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 天数图标
            Container(
              width: 48.whs,
              height: 48.whs,
              decoration: BoxDecoration(
                color: Get.theme.primaryColor,
                borderRadius: BorderRadius.circular(12.whs),
              ),
              child: Center(
                child: Text(
                  '$days',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            Gap(16.whs),
            // 文字信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Gap(4.whs),
                  Text(
                    subtitle,
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            // 箭头图标
            Icon(
              Icons.arrow_forward_ios,
              size: 16.whs,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
