import 'package:get/get.dart';
import 'package:lsenglish/model/local_history_model/local_history_model.dart';
import 'package:lsenglish/utils/login.dart';
import 'package:lsenglish/utils/sp.dart';

class HistoryController extends GetxController {
  var localHistorys = <LocalHistoryModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    if (isLogin()) {
      localHistorys.value = SPUtil().getHistoryList();
    } else {
      localHistorys.value = SPUtil().getHistoryList();
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
