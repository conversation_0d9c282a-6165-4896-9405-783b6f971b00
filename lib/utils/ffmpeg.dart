import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_session.dart';
import 'package:ffmpeg_kit_flutter_new/ffprobe_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:path/path.dart' as path;

//出现错误先检查是否可以覆盖原文件
class FFmpegUtils {
  FFmpegUtils._privateConstructor();
  static final FFmpegUtils _instance = FFmpegUtils._privateConstructor();
  factory FFmpegUtils() {
    return _instance;
  }

  void getMediaInfo(String filePath) async {
    final result = await FFmpegKit.execute('-i $filePath');
    logger("FFmpegUtils FFmpeg process exited with rc $result");
  }

  Future<bool> extractSubtitles(String inputPath, String outputPath) async {
    logger("FFmpegUtils inputPath=$inputPath outputPath=$outputPath");
    //TODO 使用ffmpeg导出的字幕有延时  暂时使用itsoffset来处理  小熊没问题 看看其他视频怎样
    final session = await FFmpegKit.execute('-itsoffset 0.3 -i $inputPath -map 0:s:0 -y $outputPath');
    final returnCode = await session.getReturnCode();
    if (ReturnCode.isSuccess(returnCode)) {
      logger("FFmpegUtils Subtitles extracted successfully.");
    } else {
      logger("FFmpegUtils Failed to extract subtitles. returnCode = $returnCode");
    }
    return ReturnCode.isSuccess(returnCode);
  }

  double calculateFrameRate(String rFrameRate) {
    List<String> parts = rFrameRate.split('/');
    double numerator = double.parse(parts[0]);
    double denominator = double.parse(parts[1]);
    return numerator / denominator;
  }

  Future<Map<String, dynamic>> getVideoInfo(String filePath) async {
    final String command = '-v error -select_streams v:0 -show_entries stream=width,height,r_frame_rate -of json $filePath';
    final session = await FFprobeKit.execute(command);
    final returnCode = await session.getReturnCode();
    if (ReturnCode.isSuccess(returnCode)) {
      final output = await session.getOutput();
      final jsonOutput = json.decode(output ?? "");
      final stream = jsonOutput['streams'][0];
      final width = stream['width'];
      final height = stream['height'];
      final rFrameRate = stream['r_frame_rate'];

      return {
        'width': width,
        'height': height,
        'frameRate': calculateFrameRate(rFrameRate),
      };
    } else {
      return {};
    }
  }

  bool isVideoOver4K(int width, int height) {
    const int maxWidth = 3840;
    const int maxHeight = 2160;
    return width >= maxWidth || height >= maxHeight;
  }

  Future<bool> isAudioFile(String filePath) async {
    var command = "-v error -show_entries stream=codec_type -of csv=p=0 $filePath";
    logger("FFmpegUtils isAudioFile command=$command");

    final session = await FFprobeKit.execute(command);
    final output = await session.getOutput();
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode)) {
      final lines = output?.split('\n') ?? [];
      final hasVideo = lines.any((line) => line.contains('video'));
      final hasAudio = lines.any((line) => line.contains('audio'));

      if (!hasVideo && hasAudio) {
        logger("FFmpegUtils isAudioFile: The file is an audio.");
        return true;
      }
    }

    logger("FFmpegUtils isAudioFile: The file is not an audio.");
    return false;
  }

  Future<bool> isVideoFile(String filePath) async {
    var command = "-v error -select_streams v:0 -show_entries stream=codec_type -of csv=s=,:p=0 $filePath";
    logger("FFmpegUtils isVideoFile command=$command");
    final session = await FFprobeKit.execute(command);
    final output = await session.getOutput();
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode) && output?.contains('video') == true) {
      logger("FFmpegUtils isVideoFile: The file is a video.");
      return true;
    } else {
      logger("FFmpegUtils isVideoFile: The file is not a video.");
      return false;
    }
  }

  Future<double> getMediaDuration(String filePath) async {
    var command = "-v error -show_entries format=duration -of csv=p=0 $filePath";
    logger("FFmpegUtils getMediaDuration command=$command");
    final session = await FFprobeKit.execute(command);
    final output = await session.getOutput();
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode) && output != null) {
      try {
        return double.parse(output.trim());
      } catch (e) {
        logger("FFmpegUtils getMediaDuration: Error parsing duration - $e");
        return 0.0;
      }
    } else {
      logger("FFmpegUtils getMediaDuration: Failed to get duration.");
      return 0.0;
    }
  }

  Future<bool> convertVideoToAudio(String inputFilePath, String outputFilePath) async {
    var command = "-y -i $inputFilePath -vn -acodec aac -ar 16000 -ac 1 -ab 128k $outputFilePath";
    logger("FFmpegUtils convertVideoToAudio command=$command");
    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    final output = await session.getOutput();
    if (ReturnCode.isSuccess(returnCode)) {
      logger("FFmpegUtils convertVideoToAudio: Conversion successful.");
      return true;
    } else {
      logger("FFmpegUtils convertVideoToAudio: Conversion failed.$output");
      return false;
    }
  }

  Future<bool> reduceNoise(String inputFilePath) async {
    // 生成带时间戳的输出文件路径
    // 分离路径和文件名
    final directory = path.dirname(inputFilePath);
    final extension = path.extension(inputFilePath);
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // 生成新的文件名
    final outputFilePath = path.join(directory, 'output_$timestamp$extension');
    var command = "-i $inputFilePath -af afftdn $outputFilePath";
    logger("FFmpegUtils reduceNoise command=$command");

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    final output = await session.getOutput();

    if (ReturnCode.isSuccess(returnCode)) {
      logger("FFmpegUtils reduceNoise: Noise reduction successful.");
      // 删除原始文件
      await File(inputFilePath).delete();

      // 重命名输出文件为原始文件名
      await File(outputFilePath).rename(inputFilePath);
      return true;
    } else {
      logger("FFmpegUtils reduceNoise: Noise reduction failed. $output");
      await File(outputFilePath).delete();
      return false;
    }
  }

// frame= 1664 fps= 62 q=38.0 size=   20224KiB time=00:01:09.40 bitrate=2387.2kbits/s speed= 2.6x
// FFmpeg 输出中的这些信息表示当前视频处理的状态。解析如下：

// - **frame=1664**: 当前已处理的帧数。
// - **fps=62**: 每秒处理的帧数。
// - **q=38.0**: 当前帧的质量因子。通常范围是 0-51，数字越低质量越好。
// - **size=20224KiB**: 当前输出文件的大小，以 KiB 为单位。
// - **time=00:01:09.40**: 已处理的视频时间位置。
// - **bitrate=2387.2kbits/s**: 当前输出的比特率。
// - **speed=2.6x**: 当前处理速度，相对于实时的倍数（2.6 倍于实时速度）。

  Future<FFmpegSession> compressVideoWithProgress(
    String inputFilePath,
    String outputFilePath,
    Function(double) onProgress,
  ) async {
    final totalDuration = await getMediaDuration(inputFilePath);
    var command = "-i $inputFilePath -vf scale=-1:1080 -c:v mpeg4 -c:a copy -y $outputFilePath -progress -";

    var session = await FFmpegKit.executeAsync(command, null, (log) {
      final message = log.getMessage();
      if (message.contains("time=")) {
        final timeStr = message.split("time=")[1].split(" ")[0];
        final currentDuration = _parseDuration(timeStr);
        final progress = currentDuration.inSeconds / totalDuration;
        logger("compressVideoWithProgress progress=$progress");
        onProgress(progress);
      }
    });
    return session;
  }

  Duration _parseDuration(String duration) {
    final parts = duration.split(':');
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    final secondsAndMillis = parts[2].split('.');
    final seconds = int.parse(secondsAndMillis[0]);
    final milliseconds = secondsAndMillis.length > 1 ? int.parse(secondsAndMillis[1]) : 0;

    return Duration(
      hours: hours,
      minutes: minutes,
      seconds: seconds,
      milliseconds: milliseconds,
    );
  }

// -i input.mp4: 输入视频文件。
// -ss 00:00:01: 指定时间点（这里是 1 秒）截取帧。
// -vframes 1: 只截取一帧。
// output.jpg: 输出的封面图路径。
  Future<bool> getVideoThumbnail(String videoPath, String outputThumbnailPath, {Duration duration = Duration.zero}) async {
    // 将 Duration 转换为 ffmpeg 时间格式
    String formattedDuration = durationToTimeString(duration);

    // 构建 ffmpeg 命令
    var command = "-y -ss $formattedDuration -i $videoPath -frames:v 1 $outputThumbnailPath";
    logger("FFmpegUtils getVideoThumbnail command=$command");

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode)) {
      logger("FFmpegUtils getVideoThumbnail successfully.");
    } else {
      logger("FFmpegUtils getVideoThumbnail Failed. returnCode = $returnCode");
    }
    return ReturnCode.isSuccess(returnCode);
  }

// Helper function to format Duration to ffmpeg time string
  String durationToTimeString(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return '${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds';
  }
}
