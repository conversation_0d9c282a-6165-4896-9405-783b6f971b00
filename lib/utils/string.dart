// 用来分离目标语言和母语的工具
//只要一行句子出现了母语 那就是母语 其他的就是目标语言

import 'util.dart';

Pair<String, String> splitText(
  String source, {
  String targetLang = "en",
  String nativeLang = "zh",
  bool onlyUseTargetLang = true,
}) {
  // ignore: unused_local_variable
  RegExp englishPattern = RegExp(r'[A-Za-z]');
  RegExp otherPattern = RegExp(r'[\u4e00-\u9fa5]');
  String englishText = "";
  String otherText = "";
  String cleanedText = source.replaceAll(RegExp(r'\{.*?\}'), '');

  // Split the text by lines
  List<String> lines = cleanedText.split(RegExp(r'\r?\n'));

  for (var line in lines) {
    bool isOtherText = false;

    for (int i = 0; i < line.length; i++) {
      if (otherPattern.hasMatch(line[i])) {
        isOtherText = true;
        break;
      }
    }

    if (isOtherText) {
      otherText += '$line\n';
    } else {
      englishText += '$line\n';
    }
  }

  return Pair(englishText.trim(), otherText.trim());
}
