import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:charset/charset.dart';

import '../core/exceptions.dart';

/// A response class of HTTP request.
class Response {
  /// The status code of response.
  final int statusCode;

  /// Response body as a string.
  final String body;

  /// Response body as a list of bytes.
  final List<int> bodyBytes;

  const Response({
    required this.statusCode,
    required this.body,
    required this.bodyBytes,
  });
}

/// The base class of any subtitle repository. Deals with the platform directly
/// to get or download the required data and submit it to the provider. You can
/// create your custom by inherited this base class.
abstract class ISubtitleRepository {
  const ISubtitleRepository();

  /// Help to fetch subtitle file data from internet.
  Future<String> fetchFromNetwork(Uri url);

  /// Help to fetch subtitle file data from a specific file.
  Future<String> fetchFromFile(File file);

  /// Simple method enable you to create a http GET request.
  Future<Response> get(
    Uri url, {
    Duration? connectionTimeout,
    Map<String, String>? headers,
    bool Function(X509Certificate cert, String host, int port)? badCertificateCallback,
  }) async {
    // Create a new HTTP client instance
    final client = HttpClient();

    // Set the options of this HTTP client
    client.connectionTimeout = connectionTimeout;
    client.badCertificateCallback = badCertificateCallback;
    final request = await client.getUrl(url);
    if (headers != null) {
      headers.forEach((name, value) {
        request.headers.add(name, value);
      });
    }

    // Start the HTTP request
    final response = await request.close();
    final bodyBytes = await response.fold<List<int>>([], (previous, element) => previous..addAll(element));
    // Decode the body
    // final responseBody = await response.transform(utf8.decoder).join();

    String responseBody;
    try {
      responseBody = utf8.decode(bodyBytes);
    } catch (e) {
      // If UTF-8 decoding fails, try UTF-16
      try {
        responseBody = utf16.decode(bodyBytes);
      } catch (e) {
        // Handle decoding error
        throw Exception("Failed to decode response body");
      }
    }

    // Close the client
    client.close(force: true);

    // Return a resutl
    return Response(
      statusCode: response.statusCode,
      body: responseBody,
      bodyBytes: responseBody.codeUnits,
    );
  }
}

/// Created to load the subtitles as a string from with value need to use futrue.
/// Deals with the platform directly to get or download the required data and submit
/// it to the provider.
///
/// It will throw an [ErrorInternetFetchingSubtitle] if failed to fetch subtitle or the [successHttpStatus] not matched.
class SubtitleRepository extends ISubtitleRepository {
  const SubtitleRepository._();

  static const SubtitleRepository instance = SubtitleRepository._();

  /// Load the subtitles from network by provide the file url.
  @override
  Future<String> fetchFromNetwork(
    Uri url, {
    Duration? connectionTimeout,
    Map<String, String>? headers,
    bool Function(X509Certificate cert, String host, int port)? badCertificateCallback,
    int successHttpStatus = HttpStatus.ok,
  }) async {
    final response = await get(
      url,
      headers: headers,
      connectionTimeout: connectionTimeout,
      badCertificateCallback: badCertificateCallback,
    );
    if (response.statusCode == successHttpStatus) {
      return response.body;
    }

    throw ErrorInternetFetchingSubtitle(response.statusCode, response.body);
  }

  /// Load the subtitles from specific file.
  @override
  Future<String> fetchFromFile(File file) {
    // return file.readAsString();
    return readFileWithAutoEncoding(file.path);
  }

  Future<String> readUtf16LeFile(String filePath) async {
    File file = File(filePath);
    // 读取文件字节
    List<int> fileBytes = await file.readAsBytes();
    // 将字节列表转换为字节缓冲区
    ByteBuffer buffer = Uint8List.fromList(fileBytes).buffer;
    // 创建一个用于读取16位整数的视图
    List<int> charCodes = [];
    for (int i = 0; i < buffer.lengthInBytes; i += 2) {
      charCodes.add(buffer.asByteData().getUint16(i, Endian.little));
    }
    // 将字符代码数组转换为字符串
    return String.fromCharCodes(charCodes);
  }

  Future<String> readFileWithAutoEncoding(String filePath) async {
    File file = File(filePath);
    final bytes = await file.readAsBytes();
    ByteData byteData = bytes.buffer.asByteData();

    if (bytes.length >= 3 && bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF) {
      return utf8.decode(bytes.sublist(3)); // UTF-8 with BOM
    } else if (bytes.length >= 2 && bytes[0] == 0xFF && bytes[1] == 0xFE) {
      return readUtf16LeFile(filePath); // UTF-16LE
    } else if (bytes.length >= 2 && bytes[0] == 0xFE && bytes[1] == 0xFF) {
      List<int> list = [];
      for (int i = 2; i < bytes.length; i += 2) {
        list.add(byteData.getUint16(i, Endian.big));
      }
      return String.fromCharCodes(list); // UTF-16BE
    } else {
      return utf8.decode(bytes); // Default to UTF-8
    }
  }
}
