import 'dart:async';
import 'dart:io';

import '../../../string.dart';
import '../core/exceptions.dart';
import '../core/models.dart';
import 'subtitle_parser.dart';
import 'subtitle_provider.dart';

/// The base class of all subtitles controller object.
abstract class ISubtitleController {
  //! Final fields
  /// Store the subtitle provider.
  final SubtitleProvider? _provider;
  final SubtitleProvider? _nativeProvider;

  /// Store the subtitles objects after decoded.
  final List<Subtitle> subtitles;
  List<Subtitle> nativeSubtitles = [];

  final Duration delay;

  //! Later and Nullable fields
  /// The parser class, maybe still null if you are not initial the controller.
  ISubtitleParser? _parser;

  ISubtitleController({
    SubtitleProvider? provider,
    SubtitleProvider? secondProvider,
    this.delay = Duration.zero,
  })  : _provider = provider,
        _nativeProvider = secondProvider,
        subtitles = List.empty(growable: true);

  //! Getters

  /// Get the parser class
  ISubtitleParser get parser {
    if (initialized) return _parser!;
    throw NotInitializedException();
  }

  /// Return the current subtitle provider
  SubtitleProvider? get provider => _provider;

  /// Check it the controller is initial or not.
  bool get initialized => _parser != null;

  //! Abstract methods
  /// Use this method to customize your search algorithm.
  Subtitle? durationSearch(Duration duration);

  /// To get one or more subtitles in same duration range.
  List<Subtitle> multiDurationSearch(Duration duration);

  bool hasIndexChanged(Map<int, int> indexMap) {
    for (var entry in indexMap.entries) {
      if (entry.key != entry.value) {
        return true; // 如果有任何键值对不相等，索引已改变
      }
    }
    return false; // 所有键值对相等，索引未改变
  }

// 判断ID是否存在于任何字幕的ID列表中
  bool containsId(int id) {
    for (var subtitle in subtitles) {
      if (subtitle.ids.contains(id)) {
        return true;
      }
    }
    return false;
  }

  //! Virual methods
  Future<void> initial() async {
    if (initialized) return;
    if (_provider == null) return;
    final providerObject = await _provider.getSubtitle();
    _parser = SubtitleParser(providerObject);
    subtitles.addAll(_parser!.parsing());
    if (_nativeProvider != null) {
      final secondProviderObject = await _nativeProvider.getSubtitle();
      var parser = SubtitleParser(secondProviderObject);
      nativeSubtitles.addAll(parser.parsing());
    }
    _handleNativeSubtitles();
    _delaySubtitles(delay);
    sort();
  }

  void _handleNativeSubtitles() {
    if (nativeSubtitles.isNotEmpty) {
      for (var i = 0; i < subtitles.length; i++) {
        subtitles[i].targetData = subtitles[i].data;
        if (i < nativeSubtitles.length) {
          subtitles[i].nativeData = nativeSubtitles[i].data;
        }
        subtitles[i].data = "${subtitles[i].targetData}\n${subtitles[i].nativeData}";
      }
    }
  }

  void _delaySubtitles(Duration delay) {
    if (delay != Duration.zero) {
      for (var subtitle in subtitles) {
        // 更新 start 和 end 时间
        subtitle.start += delay;
        subtitle.end += delay;
      }
    }
  }

  delaySubtitles(Duration delay) {
    _delaySubtitles(delay);
    sort();
  }

  /// Sort all subtitles object from smaller duration to larger duration.
  void sort() => subtitles.sort((s1, s2) => s1.compareTo(s2));

  /// Get all subtitles as a single string, you can separate between subtitles
  /// using `separator`, the default is `, `.
  String getAll([String separator = ', ']) => subtitles.join(separator);
}

/// The default class to controller subtitles, you can use it or extends
/// [ISubtitleController] to create your custom.
class SubtitleController extends ISubtitleController {
  SubtitleController({
    SubtitleProvider? provider,
    SubtitleProvider? secondProvider,
    Duration delay = Duration.zero,
  }) : super(provider: provider, secondProvider: secondProvider, delay: delay);
  Subtitle? _lastDeletedSubtitle;
  int? _lastDeletedIndex;

  /// Fetch your current single subtitle value by providing the duration.
  @override
  Subtitle? durationSearch(Duration duration) {
    if (!initialized) throw NotInitializedException();

    const l = 0;
    final r = subtitles.length - 1;
    var index = _binarySearch(l, r, duration);

    if (index > -1) {
      //不应该复制 index是字幕List的index而不是对应在字幕文件中的index
      // subtitles[index].subtitleIndex = index;
      return subtitles[index];
    }

    return null;
  }

  /// Add this method to your SubtitleController class
  Subtitle? findClosestSubtitleForward(Duration duration) {
    if (!initialized) throw NotInitializedException();

    if (subtitles.isEmpty) return null;

    // Try to find the next subtitle after the given duration
    for (var subtitle in subtitles) {
      if (subtitle.start >= duration) {
        return subtitle;
      }
    }

    // If no future subtitle is found, return the last subtitle before the duration
    for (var i = subtitles.length - 1; i >= 0; i--) {
      if (subtitles[i].end <= duration) {
        return subtitles[i];
      }
    }

    return null;
  }

  /// Perform binary search when search about subtitle by duration.
  int _binarySearch(int l, int r, Duration duration) {
    if (r >= l) {
      var mid = l + (r - l) ~/ 2;

      // if (subtitles[mid].inRange(duration)) return mid;
      if (subtitles[mid].inRange(duration)) {
        // 检查是否存在连续的字幕
        if (mid + 1 < subtitles.length && subtitles[mid].end == subtitles[mid + 1].start && duration == subtitles[mid].end) {
          return mid + 1; // 如果当前时间正好在两个字幕的交界处，返回下一个字幕的索引
        }
        return mid; // 正常情况下返回当前字幕的索引
      }

      // If element is smaller than mid, then
      // it can only be present in left subarray
      if (subtitles[mid].isLarg(duration)) {
        return _binarySearch(mid + 1, r, duration);
      }

      // Else the element can only be present
      // in right subarray
      return _binarySearch(l, mid - 1, duration);
    }

    // We reach here when element is not present
    // in array
    return -1;
  }

  @override
  List<Subtitle> multiDurationSearch(Duration duration) {
    var correctSubtitles = List<Subtitle>.empty(growable: true);

    for (var value in subtitles) {
      if (value.inRange(duration)) correctSubtitles.add(value);
    }

    return correctSubtitles;
  }

  void setSubtitles(List<Subtitle> s) {
    subtitles.clear();
    subtitles.addAll(s);
  }

  //把 endIndex 合并到 startIndex，新的 subtitle 的 index 为 startIndex
  void mergeSubtitles(int startIndex, int endIndex) {
    if (!initialized) throw NotInitializedException();

    if (startIndex < 0 || endIndex >= subtitles.length || startIndex >= endIndex) return;

    var mergedData = StringBuffer();
    var start = subtitles[startIndex].start;
    var end = subtitles[endIndex].end;

    List<int> mergedIds = []; // 记录合并的所有旧ID

    for (var i = startIndex; i <= endIndex; i++) {
      mergedData.write(subtitles[i].data);
      if (i != endIndex) mergedData.write('\n');
      mergedIds.addAll(subtitles[i].ids); // 将所有旧ID加入列表
    }
    // Remove merged subtitles and insert the merged one
    subtitles.removeRange(startIndex, endIndex + 1);
    var splitPair = splitText(mergedData.toString());
    subtitles.insert(
        startIndex,
        Subtitle(
          ids: mergedIds, // 使用合并后的ID列表
          start: start,
          end: end,
          data: mergedData.toString(),
          targetData: splitPair.first,
          nativeData: splitPair.second,
        ));

    //按照开始时间来排序
    subtitles.sort((a, b) => a.start.compareTo(b.start));
    //重新设置index  按照顺序来
    for (var i = 0; i < subtitles.length; i++) {
      subtitles[i].subtitleIndex = i;
    }

    sort();
  }

  /// Delete a subtitle at a specific index.
  Subtitle? deleteSubtitle(int index) {
    if (!initialized) throw NotInitializedException();

    if (index < 0 || index >= subtitles.length) return null;
    _lastDeletedSubtitle = subtitles[index];
    _lastDeletedIndex = index;
    return subtitles.removeAt(index);
  }

  void deleteSubtitles(int startIndex, int endIndex) {
    if (!initialized) throw NotInitializedException();

    if (startIndex < 0 || endIndex >= subtitles.length || startIndex > endIndex) {
      return;
    }

    subtitles.removeRange(startIndex, endIndex + 1);
  }

  void undoDelete() {
    if (_lastDeletedSubtitle != null && _lastDeletedIndex != null) {
      subtitles.insert(_lastDeletedIndex!, _lastDeletedSubtitle!);
      _lastDeletedSubtitle = null;
      _lastDeletedIndex = null;
    }
  }

  // 后面一句的开始时间比前面一句的结束时间小，就需要把两个subtitle进行合并
  void mergeOverlappingSubtitles() {
    for (int i = 0; i < subtitles.length - 1; i++) {
      var current = subtitles[i];
      var next = subtitles[i + 1];

      // Check if the next subtitle starts before the current one ends
      if (next.start < current.end) {
        // Merge the current and next subtitles
        var mergedData = '${current.data}\n${next.data}';
        var mergedEnd = next.end > current.end ? next.end : current.end;

        // Create a new merged subtitle
        var splitPair = splitText(mergedData);
        var mergedSubtitle = Subtitle(
          subtitleIndex: i,
          start: current.start,
          end: mergedEnd,
          data: mergedData,
          targetData: splitPair.first,
          nativeData: splitPair.second,
          ids: [1],
        );

        // Replace the current subtitle with the merged one
        subtitles[i] = mergedSubtitle;

        // Remove the next subtitle
        subtitles.removeAt(i + 1);

        // Decrement the index to recheck the current subtitle with the new next one
        i--;
      }
    }

    // Re-sort subtitles to ensure they are in the correct order
    sort();

    // Update indices
    for (var i = 0; i < subtitles.length; i++) {
      subtitles[i].subtitleIndex = i;
    }
  }

  /// Write subtitles to an SRT file.
  Future<void> writeToSrtFile(String filePath) async {
    var file = File(filePath);
    var sink = file.openWrite();

    for (var i = 0; i < subtitles.length; i++) {
      var subtitle = subtitles[i];
      var index = i + 1;
      var start = _formatDuration(subtitle.start);
      var end = _formatDuration(subtitle.end);
      var content = "${subtitle.targetData}\n${subtitle.nativeData}";

      sink.writeln('$index');
      sink.writeln('$start --> $end');
      sink.writeln(content);
      sink.writeln();
    }

    await sink.close();
  }

  String _formatDuration(Duration duration) {
    var hours = duration.inHours.toString().padLeft(2, '0');
    var minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    var seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    var milliseconds = duration.inMilliseconds.remainder(1000).toString().padLeft(3, '0');
    return '$hours:$minutes:$seconds,$milliseconds';
  }
}
