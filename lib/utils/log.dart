import 'package:flutter/foundation.dart';
import 'package:flutter_mxlogger/flutter_mxlogger.dart';
import 'package:lsenglish/utils/oss.dart';
import 'package:archive/archive_io.dart';
import 'dart:io';

enum LogLevel { debug, info, warn, error, fatal }

void loggerDebug(String message, {String name = "mxlogger", String tag = ""}) {
  Logger().debug(message, name: name, tag: tag);
}

void loggerInfo(String message, {String name = "mxlogger", String tag = ""}) {
  Logger().info(message, name: name, tag: tag);
}

void loggerWarn(String message, {String name = "mxlogger", String tag = ""}) {
  Logger().warn(message, name: name, tag: tag);
}

void loggerError(String message, {String name = "mxlogger", String tag = ""}) {
  Logger().error(message, name: name, tag: tag);
}

void loggerFatal(String message, {String name = "mxlogger", String tag = ""}) {
  Logger().fatal(message, name: name, tag: tag);
}

void logger(String message, {String name = "mxlogger", String tag = ""}) {
  Logger().info(message, name: name, tag: tag);
}

class Logger {
  static final Logger _instance = Logger._internal();
  MXLogger? _mxLogger;
  bool _initialized = false;

  factory Logger() {
    return _instance;
  }

  Logger._internal();

  Future<void> init() async {
    if (_initialized) return;
    _mxLogger = await MXLogger.initialize(
      nameSpace: "flutter.mxlogger",
      storagePolicy: MXStoragePolicyType.yyyy_MM_dd,
      cryptKey: "abcuioqbsdguijlk",
      iv: "bccuioqbsdguijiv",
    );
    _mxLogger?.setMaxDiskAge(60 * 60 * 24 * 7); // 7天
    _mxLogger?.setMaxDiskSize(1024 * 1024 * 10); // 10M
    _mxLogger?.setLevel(0);
    _initialized = true;
  }

  Future<void> _logWithLevel(
    LogLevel level,
    String message, {
    String name = "mxlogger",
    String tag = "",
  }) async {
    await init();
    debugPrint('[${level.name.toUpperCase()}] $message');
    switch (level) {
      case LogLevel.debug:
        _mxLogger?.debug(message, name: name, tag: tag);
        break;
      case LogLevel.info:
        _mxLogger?.info(message, name: name, tag: tag);
        break;
      case LogLevel.warn:
        _mxLogger?.warn(message, name: name, tag: tag);
        break;
      case LogLevel.error:
        _mxLogger?.error(message, name: name, tag: tag);
        break;
      case LogLevel.fatal:
        _mxLogger?.fatal(message, name: name, tag: tag);
        break;
    }
  }

  Future<void> debug(String message, {String name = "mxlogger", String tag = ""}) =>
      _logWithLevel(LogLevel.debug, message, name: name, tag: tag);

  Future<void> info(String message, {String name = "mxlogger", String tag = ""}) =>
      _logWithLevel(LogLevel.info, message, name: name, tag: tag);

  Future<void> warn(String message, {String name = "mxlogger", String tag = ""}) =>
      _logWithLevel(LogLevel.warn, message, name: name, tag: tag);

  Future<void> error(String message, {String name = "mxlogger", String tag = ""}) =>
      _logWithLevel(LogLevel.error, message, name: name, tag: tag);

  Future<void> fatal(String message, {String name = "mxlogger", String tag = ""}) =>
      _logWithLevel(LogLevel.fatal, message, name: name, tag: tag);

  Future<List<String>> getLogFiles() async {
    await init();
    final files = _mxLogger?.getLogFiles() ?? [];
    final dir = _mxLogger?.diskcachePath ?? "";
    // 拼接完整路径
    return files.map((e) => e.name).whereType<String>().map((name) => "$dir/$name").toList();
  }

  Future<String?> uploadLogFile() async {
    await init();
    debugPrint('Uploading log file...');
    final files = await getLogFiles();
    debugPrint('files size =  [38;5;5m${files.length} [0m');
    if (files.isEmpty) return "";

    // 1. 压缩所有日志文件
    final zipPath = await _zipLogFiles(files);
    debugPrint('zipPath=$zipPath');

    // 2. 上传 zip 文件
    var ossUrl = await OssUtil().uploadUserLogFile(zipPath);
    return ossUrl;
  }

  Future<String> _zipLogFiles(List<String> filePaths) async {
    final tempDir = Directory.systemTemp;
    final zipFilePath = '${tempDir.path}/logs.zip';
    final outputStream = OutputFileStream(zipFilePath);

    final archive = Archive();
    for (var path in filePaths) {
      final file = File(path);
      if (file.existsSync()) {
        final fileName = file.uri.pathSegments.last;
        archive.addFile(ArchiveFile(
          fileName,
          file.lengthSync(),
          file.readAsBytesSync(),
        ));
      }
    }

    ZipEncoder().encode(archive, output: outputStream);
    await outputStream.close();
    return zipFilePath;
  }
}
