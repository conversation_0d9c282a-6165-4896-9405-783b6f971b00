import 'dart:convert';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/model/local_history_model/local_history_model.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:path/path.dart';
import '../model/subtitle_cover_model.dart';
import '../model/user_login_resp/user_login_resp.dart';

class SPUtil {
  static const _localHistoryKey = "localHistoryKey";
  static const _userInfoKey = "userInfoKey";
  static const _subtitleCoverKey = "subtitleCoverKey";
  SPUtil._privateConstructor();
  static final SPUtil _instance = SPUtil._privateConstructor();
  factory SPUtil() {
    return _instance;
  }

  Future<void> clearSharedPreferences() async {
    GetStorage().erase();
  }

  List<LocalHistoryModel> getHistoryList() {
    var content = GetStorage().read(_localHistoryKey);
    if (content != null && content.isNotEmpty) {
      List<dynamic> jsonResponse = json.decode(content);
      List<LocalHistoryModel> historyList = jsonResponse.map((item) => LocalHistoryModel.fromJson(item)).toList();
      return historyList;
    }
    return [];
  }

  void removeHistory(String videoLocalPath) async {
    List<LocalHistoryModel> list = getHistoryList();
    var index = list.indexWhere((element) => element.videoLocalPath == videoLocalPath);
    if (index != -1) {
      list.removeAt(index);
    }
    GetStorage().write(_localHistoryKey, jsonEncode(list));
  }

  ///通过存储文件的hash值来判断是否存在 因为文件的路径可能会改变或者被用户移动
  ///包括字幕也是 也是通过hash值来存储
  void saveHistory(String videoLocalPath, {String subtitleLocalPath = ""}) async {
    if (videoLocalPath == "") {
      return;
    }
    List<LocalHistoryModel> list = getHistoryList();
    var videoName = basenameWithoutExtension(videoLocalPath);
    var model = LocalHistoryModel(videoLocalPath: videoLocalPath, subtitleLocalPath: subtitleLocalPath);
    var originModel = list.firstWhereOrNull((element) => basenameWithoutExtension(element.videoLocalPath ?? "") == videoName);
    logger("saveHistory originModel = $originModel");
    if (list.isEmpty || originModel == null) {
      list.add(model);
    } else {
      var index = list.indexWhere((element) => basenameWithoutExtension(element.videoLocalPath ?? "") == videoName);
      originModel.videoLocalPath = model.videoLocalPath;
      originModel.subtitleLocalPath = model.subtitleLocalPath;
      list[index] = originModel;
    }
    GetStorage().write(_localHistoryKey, jsonEncode(list));
  }

  Future<LocalHistoryModel?> getHistory(String videoLocalPath) async {
    List<LocalHistoryModel> list = getHistoryList();
    var ele = list.firstWhereOrNull((element) => element.videoLocalPath == videoLocalPath);
    return ele;
  }



  Future<void> saveUserInfo(UserLoginResp user) async {
    await GetStorage().write(_userInfoKey, jsonEncode(user));
  }

  void clearUserInfo() {
    Net.getDio().options.headers['Authorization'] = "";
    GetStorage().write(_userInfoKey, "");
  }

  UserLoginResp? getUserInfo() {
    var content = GetStorage().read(_userInfoKey);
    if (content != null && content.isNotEmpty) {
      return UserLoginResp.fromJson(jsonDecode(content));
    }
    return null;
  }

  void saveSubtitleCover(String videoPath, SubtitleCoverModel subtitleCoverModel) {
    GetStorage().write(videoPath + _subtitleCoverKey, jsonEncode(subtitleCoverModel));
  }

  Future<SubtitleCoverModel?> getSubtitleCover(String videoPath) async {
    var content = GetStorage().read(videoPath + _subtitleCoverKey);
    if (content != null && content.isNotEmpty) {
      return SubtitleCoverModel.fromJson(jsonDecode(content));
    }
    return null;
  }
}
