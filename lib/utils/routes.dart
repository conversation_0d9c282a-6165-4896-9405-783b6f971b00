import 'dart:io';
import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:path/path.dart';

class RoutesUtil {
  RoutesUtil._privateConstructor();
  static final RoutesUtil _instance = RoutesUtil._privateConstructor();
  factory RoutesUtil() {
    return _instance;
  }
  Future<void> goDetailByPath(String localFilePath) async {
    if (localFilePath.isEmpty) {
      return;
    }
    var filePath = localFilePath;
    var needMoveToDocuments = false;
    var videoName = basenameWithoutExtension(localFilePath);
    var videoPath = await FileUtils().findVideoFilePath((await FileUtils().getSaveDir()).path, videoName);
    logger("goDetailByPath findVideoFilePath videoPath=$videoPath");
    needMoveToDocuments = videoPath == null || videoPath == "";
    logger("needMoveToDocuments=$needMoveToDocuments");
    if (Platform.isMacOS) {
      filePath = await FileUtils().copyFileToDocuments(localFilePath);
    } else {
      if (needMoveToDocuments) {
        filePath = await FileUtils().moveFileToDocuments(localFilePath);
      } else {
        filePath = videoPath;
      }
    }
    await Get.toNamed(Routes.DETAIL, arguments: {
      'videoUrlOrPath': filePath,
      'videoName': videoName,
    });
  }

  Future<void> goVideoDetailByRemote(String? videoUrlOrPath, String? videoName, String? resourceId) async {
    await Get.toNamed(Routes.DETAIL, arguments: {
      'videoUrlOrPath': videoUrlOrPath,
      'videoName': videoName,
      'resourceId': resourceId,
    });
  }
}
