import 'dart:core';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:convert/convert.dart';

class CryptTools {
  // ignore: constant_identifier_names
  static const String HMAC_SHA1 = 'HmacSHA1';
  // ignore: constant_identifier_names
  static const String HMAC_SHA256 = 'HmacSHA256';

  static Future<String> hmacEncrypt(String encryptType, String plainText, String encryptKey) async {
    var key = utf8.encode(encryptKey);
    var bytes = utf8.encode(plainText);

    Hmac hmac;
    if (encryptType == HMAC_SHA1) {
      hmac = Hmac(sha1, key);
    } else {
      hmac = Hmac(sha256, key);
    }
    var digest = hmac.convert(bytes);
    return base64.encode(digest.bytes);
  }

  static String md5Encrypt(String pstr) {
    var bytes = utf8.encode(pstr);
    var digest = md5.convert(bytes);
    return hex.encode(digest.bytes);
  }

  static String base64Encode(String plainText) {
    return base64.encode(utf8.encode(plainText));
  }
}

abstract class AbstractSignature {
  final String id;
  final String key;
  final String? url;
  String? originSign;
  String? signa;
  final String ts;
  String requestMethod = 'GET';

  AbstractSignature(this.id, this.key, this.url) : ts = generateTs();

  static String generateTs() {
    return (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
  }

  Future<String> getSigna();

  String generateOriginSign();

  String getId() => id;
  String getKey() => key;
  String getOriginSign() => originSign!;
  void setOriginSign(String originSign) {
    this.originSign = originSign;
  }

  String getTs() => ts;
}

class LfasrSignature extends AbstractSignature {
  LfasrSignature(String appId, String keySecret) : super(appId, keySecret, null);

  @override
  Future<String> getSigna() async {
    if (signa == null) {
      setOriginSign(generateOriginSign());
      signa = await generateSignature();
    }
    return signa!;
  }

  Future<String> generateSignature() async {
    return CryptTools.hmacEncrypt(CryptTools.HMAC_SHA1, getOriginSign(), getKey());
  }

  @override
  String generateOriginSign() {
    return CryptTools.md5Encrypt(getId() + getTs());
  }
}
