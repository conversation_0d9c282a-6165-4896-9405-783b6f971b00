import 'dart:ui';

import 'package:get_storage/get_storage.dart';

void saveLang(String langCode) {
  GetStorage().write('locale', langCode);
}

Locale getLang() {
  final locale = GetStorage().read('locale');
  if (locale == null || locale.isEmpty) {
    // 返回一个默认值，比如中文
    return const Locale('zh', 'CN');
  }
  final parts = locale.split('-');
  if (parts.length == 2) {
    return Locale(parts[0], parts[1]);
  } else {
    return Locale(locale);
  }
}
