import 'dart:io';
import 'package:archive/archive.dart';
import 'package:flutter/foundation.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:path/path.dart' as p;

Future<File> _downloadZipFile(String url, String filename, {String saveSubtitleDirPath = "", bool useUrlFileName = true}) async {
  try {
    if (saveSubtitleDirPath.isEmpty) {
      var dir = await FileUtils().getSaveSubtitleDir();
      saveSubtitleDirPath = dir.path;
    }

    File file = File("$saveSubtitleDirPath/$filename");
    await deleteFileOrDirectory(file.path);
    var response = await Net.getDio().download(url, file.path);
    var contentDisposition = response.headers.value('content-disposition');
    //这里直接使用传入的文件名
    if (contentDisposition != null && useUrlFileName) {
      // Content-Disposition 字段可能是类似于 "attachment; filename="example.zip"" 的形式
      final RegExp regex = RegExp(r'filename="([^"]*)"');
      final Match? match = regex.firstMatch(contentDisposition);
      if (match != null && match.groupCount > 0) {
        final String? downloadFileName = match.group(1);
        final newFile = file.renameSync(p.join(saveSubtitleDirPath, downloadFileName));
        return newFile;
      }
    }
    return file;
  } finally {}
}

Future deleteFileOrDirectory(String path) async {
  final entity = FileSystemEntity.typeSync(path);

  if (entity == FileSystemEntityType.file) {
    try {
      final file = File(path);
      await file.delete();
      if (kDebugMode) {
        print('File deleted: $path');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to delete file: $e');
      }
    }
  } else if (entity == FileSystemEntityType.directory) {
    try {
      final directory = Directory(path);
      await directory.delete(recursive: true);
      if (kDebugMode) {
        print('Directory deleted: $path');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to delete directory: $e');
      }
    }
  } else {
    if (kDebugMode) {
      print('No file or directory found at: $path');
    }
  }
}

Future<String> unzipFile(File downloadedFile, String dirName) async {
  final directoryPath = "${downloadedFile.parent.path}/$dirName";
  final directory = Directory(directoryPath);

  // 确保目录是空的
  if (directory.existsSync()) {
    await directory.delete(recursive: true);
  }

  // 创建目录
  await directory.create(recursive: true);

  // 读取并解码压缩文件
  final bytes = await downloadedFile.readAsBytes();
  final archive = ZipDecoder().decodeBytes(bytes);

  // 提取内容
  for (final file in archive) {
    if (file.isFile) {
      final filename = file.name.split('/').last; // 只保留文件名
      final filePath = '$directoryPath/$filename';
      final outFile = File(filePath);
      await outFile.create(recursive: true);
      await outFile.writeAsBytes(file.content as List<int>);
    }
  }

  // 返回解压后的目录路径
  return directoryPath;
}

// Future<String> unrarFile(File downloadedFile, String dirName) async {
//   final directoryPath = "${downloadedFile.parent.path}/$dirName";
//   final directory = Directory(directoryPath);

//   // 确保目录是空的
//   if (directory.existsSync()) {
//     await directory.delete(recursive: true);
//   }

//   // 创建目录
//   await directory.create(recursive: true);

//   await UnrarFile.extract_rar(downloadedFile.path, directoryPath);
//   // 返回解压后的目录路径
//   return directoryPath;
// }

//filename为下载的zip的名字和解压后的文件夹名字
Future<String> handleZipDownloadAndUnpack(
  String url,
  String filename, {
  String saveSubtitleDirPath = "",
}) async {
  if (url.isEmpty || filename.isEmpty) {
    return "";
  }
  try {
    File downloadedZipFile = await _downloadZipFile(url, filename, saveSubtitleDirPath: saveSubtitleDirPath);
    logger("downloadedZipFile = ${downloadedZipFile.path}");
    bool isZipFileResult = await FileUtils().isZipFile(downloadedZipFile.path);
    logger("isZipFileResult =$isZipFileResult");
    if (isZipFileResult) {
      String zipDirName = await unzipFile(downloadedZipFile, filename);
      downloadedZipFile.deleteSync();
      logger("zipDirName = $zipDirName");
      return zipDirName;
    }
    return "";
    // else {
    //   if (downloadedZipFile.path.endsWith('rar')) {
    //     String zipDirName = await unrarFile(downloadedZipFile, filename);
    //     downloadedZipFile.deleteSync();
    //     logger("rarDirName = $zipDirName");
    //     return zipDirName;
    //   }
    //   return downloadedZipFile.parent.path;
    // }
  } catch (e) {
    logger('handleZipDownloadAndUnpack 发生错误: $e');
    return "";
  }
}
