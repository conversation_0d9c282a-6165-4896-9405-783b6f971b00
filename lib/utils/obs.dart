import 'package:get/get.dart';

import '../model/local_detail_resp/resource_detail_resp.dart';

class ObsUtil {
  ObsUtil._internal();
  static final ObsUtil _instance = ObsUtil._internal();
  factory ObsUtil() => _instance;
  var loginStatus = false.obs;
  var changeNote = false.obs;
  var updateDataCenter = 1.obs;
  var renameLocalFileName = false.obs;
  var watchHistoryPositionChange = [].obs;
  var uploadSubtitle = ResourceDetailResp().obs;
  // 新增：通知 PlanView 更新学习进度，包含具体的天ID和新增句子数
  var updatePlanProgress = Rx<Map<String, dynamic>?>(null);
}
