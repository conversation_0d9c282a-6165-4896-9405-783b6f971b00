import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/extension.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/widgets/base_dialog.dart';

void showChangeResourceNameDialog(String? resourceId, int? resourceType, String? episodeName) {
  if (resourceId == null || resourceType == null || episodeName == null) {
    return;
  }
  if (resourceType != 2) {
    "远程资源不允许修改名称".toast;
    return;
  }
  CommonInputDialog(
    title: "修改你的名称",
    inputContent: episodeName,
    sureCallback: (String content) {
      if (content.isNotEmpty) {
        SmartDialog.showLoading(msg: "修改中...");
        Net.getRestClient().changeResourceName({
          'resourceId': resourceId,
          'resourceType': resourceType,
          'name': content,
        }).then((data) async {
          var videoPath = await FileUtils().findVideoFilePath((await FileUtils().getSaveDir()).path, episodeName);
          FileUtils().renameFile(videoPath ?? "", content);
          ObsUtil().renameLocalFileName.value = true;
          ObsUtil().renameLocalFileName.refresh();
          SmartDialog.dismiss();
        }).catchError((e) {
          SmartDialog.dismiss();
        });
      }
    },
  ).showDialog;
}
