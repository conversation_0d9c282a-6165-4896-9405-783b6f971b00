import 'dart:io';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/util.dart';
import 'package:path/path.dart';
import 'package:tuple/tuple.dart';

import '../app/modules/detail/controllers/detail_util.dart';
import 'subtitle/subtitle.dart';

Future<Tuple2<SubtitleController?, List<Subtitle>>> loadSubtitlesInternal(String subtitlePath) async {
  logger("loadSubtitlesInternal subtitlePath=$subtitlePath");
  if (subtitlePath.isNotEmpty) {
    SubtitleController subtitleController;
    if (subtitlePath.startsWith("http")) {
      var file = await DefaultCacheManager().getSingleFile(subtitlePath);
      logger("loadSubtitlesInternal file=$file");
      subtitleController = SubtitleController(provider: SubtitleProvider.fromFile(file, type: getSubtitleType(extension(subtitlePath))));
      // subtitleController = SubtitleController(provider: SubtitleProvider.fromNetwork(Uri.parse(subtitlePath)));
    } else {
      var file = File(subtitlePath);
      subtitleController = SubtitleController(provider: SubtitleProvider.fromFile(file, type: getSubtitleType(extension(subtitlePath))));
    }
    await subtitleController.initial();
    return Tuple2(subtitleController, subtitleController.subtitles);
  } else {
    return const Tuple2(null, []);
  }
}

Future<Tuple2<SubtitleController?, List<Subtitle>>> loadMultiSubtitlesInternal(Pair<String, String> subtitlePaths) async {
  logger("loadMultiSubtitlesInternal subtitlePaths=${subtitlePaths.first}  second=${subtitlePaths.second}");
  if (subtitlePaths.first.isNotEmpty && subtitlePaths.second.isNotEmpty) {
    SubtitleController subtitleController;
    subtitleController = SubtitleController(
      provider: subtitlePaths.first.startsWith("http")
          ? SubtitleProvider.fromFile(await DefaultCacheManager().getSingleFile(subtitlePaths.first),
              type: getSubtitleType(extension(subtitlePaths.first)))
          : SubtitleProvider.fromFile(File(subtitlePaths.first), type: getSubtitleType(extension(subtitlePaths.first))),
      secondProvider: subtitlePaths.first.startsWith("http")
          ? SubtitleProvider.fromFile(await DefaultCacheManager().getSingleFile(subtitlePaths.second),
              type: getSubtitleType(extension(subtitlePaths.second)))
          : SubtitleProvider.fromFile(File(subtitlePaths.second), type: getSubtitleType(extension(subtitlePaths.first))),
    );
    await subtitleController.initial();
    return Tuple2(subtitleController, subtitleController.subtitles);
  } else {
    return const Tuple2(null, []);
  }
}

void downloadVideoCache(String videoUrlOrPath) {
  if (needAutoDownloadVideoCache(videoUrlOrPath)) {
    Future.microtask(() => {
          DefaultCacheManager().downloadFile(videoUrlOrPath, key: filterVideoUrl(videoUrlOrPath)),
        });
  }
}

bool needAutoDownloadVideoCache(String videoUrlOrPath) {
  return true;
}
