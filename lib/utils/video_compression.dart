import 'dart:async';

import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_session.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/log.dart';

class VideoCompression {
  final String videoPath;
  String outputPath;
  double progress;
  RxString loadingText;
  RxString errorText;
  Completer<void>? completer;
  FFmpegSession? fFmpegSession;
  bool isCanceled;

  VideoCompression({
    required this.videoPath,
    this.outputPath = "",
    this.completer,
    this.isCanceled = false,
    this.progress = 0,
    this.fFmpegSession,
    String loadingText = '准备处理中...',
    String errorText = '',
  })  : loadingText = loadingText.obs,
        errorText = errorText.obs;
}

class VideoCompressionUtil {
  VideoCompressionUtil._internal();
  static final VideoCompressionUtil _instance = VideoCompressionUtil._internal();
  factory VideoCompressionUtil() => _instance;

  var videoCompresses = <VideoCompression?>[].obs;
  final RxList<String> completed = <String>[].obs;
  final List<VideoCompression> _queue = [];
  int _concurrentLimit = 1;
  int _currentRunning = 0;

  void configureConcurrency(int limit) {
    _concurrentLimit = limit;
  }

  bool isCompleted(String? videoPath) {
    return completed.any((item) => item == videoPath);
  }

  void addCompress(VideoCompression videoCompress) {
    if (videoCompresses.any((item) => item?.videoPath == videoCompress.videoPath)) {
      return;
    }
    _queue.add(videoCompress);
    videoCompresses.add(videoCompress);
    videoCompresses.refresh();
    _processQueue();
  }

  void cancelCompress(String? videoPath) {
    final videoCompress = videoCompresses.firstWhereOrNull((item) => item?.videoPath == videoPath);
    if (videoCompress == null) {
      return;
    }
    videoCompress.isCanceled = true;
    videoCompress.completer?.completeError('Operation was canceled');
    FFmpegKit.cancel(videoCompress.fFmpegSession?.getSessionId());
    _queue.remove(videoCompress);
    videoCompresses.remove(videoCompress);
    videoCompresses.refresh();

    _queue.removeWhere((item) => item.videoPath == videoPath);
    videoCompresses.removeWhere((item) => item?.videoPath == videoPath);
    videoCompresses.refresh();
    logger('Canceled video compress for: $videoPath');
  }

  Future<void> retryCompress(String? videoPath) async {
    final videoCompress = videoCompresses.firstWhereOrNull((item) => item?.videoPath == videoPath);
    if (videoCompress == null) {
      return;
    }
    logger("completed = $completed");
    if (videoCompresses.contains(videoCompress)) {
      videoCompress.errorText.value = "";
      _queue.add(videoCompress);
      _processQueue();
    } else {
      logger("分析尚未完成或已取消，无法重试");
    }
  }

  Future<void> finishCompress(String? videoPath) async {
    final videoCompress = videoCompresses.firstWhereOrNull((item) => item?.videoPath == videoPath);
    if (videoCompress == null) {
      return;
    }
    completed.add(videoCompress.videoPath);
    completed.refresh();
    videoCompress.completer?.complete();
    logger("complete------------");
    videoCompresses.remove(videoCompress);
    videoCompresses.refresh();
  }

  Future<void> _processQueue() async {
    if (_queue.isEmpty || _currentRunning >= _concurrentLimit) {
      return;
    }

    _currentRunning++;
    final videoCompress = _queue.removeAt(0);
    videoCompress.completer = Completer<void>();
    try {
      await _startCompress(videoCompress);
      if (videoCompress.isCanceled) {
        return Future.error('Operation was canceled');
      }
    } catch (e) {
      videoCompress.errorText.value = '发生异常$e';
      logger("异常:${videoCompress.videoPath}");
      videoCompress.completer?.completeError(e);
    } finally {
      _currentRunning--;
      _processQueue();
    }
    _processQueue();
  }

  Future<bool> _startCompress(VideoCompression vc) async {
    vc.outputPath = FileUtils().generateTempFilePath(vc.videoPath, "_compress");
    vc.fFmpegSession = await FFmpegUtils().compressVideoWithProgress(vc.videoPath, vc.outputPath, (progress) {
      vc.progress = progress * 100;
      vc.loadingText.value = "压缩进度:${vc.progress.toStringAsFixed(2)}%";
      if (progress >= 1) {
        finishCompress(vc.videoPath);
      }
    });
    return true;
  }
}
