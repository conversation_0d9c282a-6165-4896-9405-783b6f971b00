import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/material.dart' as prefix0;
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../model/data_center_chart_resp/data_center_chart_data.dart';
import 'util.dart';

class ChartCategoryData {
  ChartCategoryData({
    this.category,
    this.y,
  });
  String? category;
  num? y;
}

class ChartData {
  ChartData({
    this.x,
    this.y,
  });
  num? x;
  num? y;
}

TextSpan getTootipsTextSpan(int tabType, DataCenterChartData dataCenterChartData) {
  TextStyle normalStyle = TextStyle(
    fontSize: 18.whs,
    color: const Color(0xff1C1A1A),
  );
  final textSpan = TextSpan(
    children: [
      TextSpan(
        text: tabType == 4 ? "日均" : "总计",
        style: normalStyle,
      ),
      TextSpan(
        text: "\n",
        style: normalStyle,
      ),
      formatMinutes(dataCenterChartData.duration),
      TextSpan(
        text: "\n",
        style: normalStyle,
      ),
      TextSpan(
        text: "${dataCenterChartData.startDateString}",
        style: normalStyle,
      ),
    ],
  );
  return textSpan;
}

Pair<double, double> calculateYAxisLabels(double minY, double maxY) {
  // 向下舍入 minY 到最近的 10 的倍数
  double adjustedMinY = (minY ~/ 10) * 10;

  // 向上舍入 maxY 到下一个 10 的倍数，即使它已经是 10 的倍数
  double adjustedMaxY = ((maxY + 9) ~/ 10) * 10;
  if (maxY % 10 == 0) {
    adjustedMaxY += 10; // 如果 maxY 已经是 10 的倍数，则增加 10
  }
  if (adjustedMaxY < maxY) {
    adjustedMaxY += 10;
  }
  return Pair(adjustedMinY, adjustedMaxY);
}

void commonTooltipsPaint(PaintingContext context, double x, Offset offset, Size size, TextSpan textSpan, {double topOffset = 16.0}) {
  final paint = Paint()
    ..color = gray200
    ..style = PaintingStyle.stroke
    ..strokeWidth = 2;

  double centerX;
  centerX = x;
  drawDashedVerticalLine(context.canvas, centerX, offset.dy - topOffset, size.height, paint, <double>[4, 4]);

  // Tooltips
  final tooltipPaint = Paint()
    ..color = const Color(0xffE8E8E8)
    ..style = PaintingStyle.fill;

  final textPainter = TextPainter(
    text: textSpan,
    textDirection: prefix0.TextDirection.ltr,
  );
  textPainter.layout();

  // 内间距值
  const double padding = 8.0;
  final tooltipWidth = textPainter.width + 2 * padding;
  final tooltipHeight = textPainter.height + 2 * padding;
  double tooltipX = (centerX - tooltipWidth / 2); // 从右侧向左扩展，并加入右偏移
  final tooltipY = offset.dy - tooltipHeight - topOffset;

  // 确保Tooltip不会超出图表的左边界
  if (tooltipX < offset.dx) {
    tooltipX = offset.dx;
  }

  // 确保Tooltip不会超出图表的右边界
  if (tooltipX + tooltipWidth > offset.dx + size.width) {
    tooltipX = offset.dx + size.width - tooltipWidth;
  }

  final curvePath = Path();
  curvePath.addRRect(
    RRect.fromRectAndRadius(Rect.fromLTWH(tooltipX, tooltipY, tooltipWidth, tooltipHeight), const Radius.circular(4.0)),
  );

  context.canvas.drawPath(curvePath, tooltipPaint);
  textPainter.paint(context.canvas, Offset(tooltipX + padding, tooltipY + padding));
}

void drawDashedVerticalLine(Canvas canvas, double centerX, double startY, double endY, Paint paint, List<double> dash) {
  // 创建一个Path对象
  Path path = Path();
  path.moveTo(centerX, startY); // 移动到起始点
  path.lineTo(centerX, endY); // 绘制到终点

  // 使用 dashPath 来创建虚线效果
  // Path dashedPath = dashPath(
  //   path,
  //   dashArray: CircularIntervalList<double>(dash),
  // );

  // 绘制虚线路径
  canvas.drawPath(path, paint);
}

Path dashPath(
  Path source, {
  required CircularIntervalList<double> dashArray,
}) {
  const double intialValue = 0.0;
  final Path path = Path();
  for (final PathMetric measurePath in source.computeMetrics()) {
    double distance = intialValue;
    bool draw = true;
    while (distance < measurePath.length) {
      final double length = dashArray.next;
      if (draw) {
        path.addPath(measurePath.extractPath(distance, distance + length), Offset.zero);
      }
      distance += length;
      draw = !draw;
    }
  }
  return path;
}

class CircularIntervalList<T> {
  CircularIntervalList(this._values);
  final List<T> _values;
  int _index = 0;
  T get next {
    if (_index >= _values.length) {
      _index = 0;
    }
    return _values[_index++];
  }
}

class ShortIndicatorDecoration extends Decoration {
  final double indicatorWidth;
  final double indicatorHeight;
  final Color indicatorColor;

  const ShortIndicatorDecoration({
    this.indicatorWidth = 0.5, // 默认宽度为Tab宽度的50%
    this.indicatorHeight = 5.0, // 默认高度为5.0
    this.indicatorColor = Colors.red, // 默认颜色为红色
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ShortIndicatorPainter(this);
  }
}

class _ShortIndicatorPainter extends BoxPainter {
  final ShortIndicatorDecoration decoration;

  _ShortIndicatorPainter(this.decoration);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final double indicatorWidth = decoration.indicatorWidth * configuration.size!.width;
    final double horizontalOffset = (configuration.size!.width - indicatorWidth) / 2;
    final Rect rect = Offset(offset.dx + horizontalOffset, offset.dy + configuration.size!.height - decoration.indicatorHeight) &
        Size(indicatorWidth, decoration.indicatorHeight);
    final Paint paint = Paint()
      ..color = decoration.indicatorColor
      ..style = PaintingStyle.fill;

    final RRect rRect = RRect.fromRectAndRadius(rect, Radius.circular(decoration.indicatorHeight / 2));
    canvas.drawRRect(rRect, paint);
  }
}

Pair<int?, int?> getChartTimeRange(int tabType, int index) {
  final now = DateTime.now();
  DateTime startTime;
  DateTime? endTime;

  switch (tabType) {
    case 1:
      startTime = DateTime(now.year, now.month, now.day - index);
      endTime = DateTime(now.year, now.month, now.day - index, 23, 59, 59);
      break;
    case 2:
      int currentWeekday = now.weekday;
      startTime = DateTime(now.year, now.month, now.day - currentWeekday + 1 - index * 7);
      endTime = startTime.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
      break;
    case 3:
      startTime = DateTime(now.year, now.month - index, 1);
      endTime = DateTime(now.year, now.month - index + 1, 0, 23, 59, 59);
      break;
    case 4:
      startTime = DateTime(now.year - index, 1, 1);
      endTime = DateTime(now.year - index, 12, 31, 23, 59, 59);
      break;
    default:
      throw ArgumentError('Invalid tabType');
  }

  return Pair(
    startTime.millisecondsSinceEpoch ~/ 1000,
    endTime.millisecondsSinceEpoch ~/ 1000,
  );
}

String getChartTimeRangeString(int tabType, int index) {
  final now = DateTime.now();
  DateTime startTime;
  DateTime? endTime;
  String format(DateTime date) => '${date.year}年${date.month}月${date.day}日';
  String formatWithWeek(DateTime date) => '${date.year}年${date.month}月${date.day}日 周${["一", "二", "三", "四", "五", "六", "日"][date.weekday - 1]}';

  switch (tabType) {
    case 1:
      startTime = now.subtract(Duration(days: index));
      endTime = null;
      if (index == 0) {
        return formatWithWeek(startTime);
      }
      return formatWithWeek(startTime);
    case 2:
      int currentWeekday = now.weekday;
      startTime = now.subtract(Duration(days: currentWeekday - 1 + index * 7));
      endTime = startTime.add(const Duration(days: 6));
      return '${format(startTime)}至${format(endTime)}';
    case 3:
      startTime = DateTime(now.year, now.month - index, 1);
      endTime = DateTime(now.year, now.month - index + 1, 1).subtract(const Duration(days: 1));
      return '${format(startTime)}至${format(endTime)}';
    case 4:
      startTime = DateTime(now.year - index, 1, 1);
      endTime = DateTime(now.year - index, 12, 31);
      return '${startTime.year}年';
    default:
      throw ArgumentError('Invalid tabType');
  }
}

TextSpan formatMinutes(int? minutes, {int numSize = 30, Color numColor = Colors.black, Color unitColor = const Color(0xff98A2B3)}) {
  if (minutes == null) {
    return TextSpan(
      text: '--',
      style: TextStyle(fontSize: 20.whs),
    );
  }
  int hours = minutes ~/ 60;
  int remainingMinutes = minutes % 60;

  return TextSpan(
    children: [
      if (hours > 0) ...[
        TextSpan(
          text: '$hours',
          style: TextStyle(fontSize: numSize.whs, color: numColor, fontWeight: FontWeight.bold),
        ),
        TextSpan(
          text: ' h ',
          style: TextStyle(fontSize: 16.whs, color: unitColor),
        ),
      ],
      TextSpan(
        text: '$remainingMinutes',
        style: TextStyle(fontSize: numSize.whs, color: numColor, fontWeight: FontWeight.bold),
      ),
      TextSpan(
        text: ' min',
        style: TextStyle(fontSize: 16.whs, color: unitColor),
      ),
    ],
  );
}

TextSpan formatLs(int? lsTime, {int numSize = 30}) {
  if (lsTime == null) {
    return TextSpan(
      text: '--',
      style: TextStyle(fontSize: 20.whs),
    );
  }
  return TextSpan(
    children: [
      TextSpan(
        text: '$lsTime',
        style: TextStyle(fontSize: numSize.whs, color: Colors.black, fontWeight: FontWeight.bold),
      ),
      TextSpan(
        text: ' ls',
        style: TextStyle(fontSize: 16.whs, color: gray400),
      ),
    ],
  );
}
