import 'package:lsenglish/utils/datacenter_time_manager.dart';
import 'package:lsenglish/utils/subtitle/subtitle.dart';
import 'package:lsenglish/utils/log.dart';

/// DataCenterTimeManager的扩展方法，为detail_controller提供便捷的集成接口
extension DataCenterTimeManagerExtension on DataCenterTimeManager {
  /// 安全地开始学习会话
  void safeBeginSession({
    required String? resourceId,
    required int? resourceType,
    required int? lsTimes,
  }) {
    try {
      if (resourceId?.isNotEmpty == true) {
        beginSession(
          resourceId: resourceId!,
          resourceType: resourceType ?? 2,
          lsTimes: lsTimes ?? 0,
        );
        logger("DataCenterTimeManager: 学习会话已开始 - $resourceId");
      } else {
        logger("DataCenterTimeManager: resourceId为空，跳过会话开始");
      }
    } catch (e) {
      logger("DataCenterTimeManager: 开始会话失败 - $e");
    }
  }

  /// 安全地记录播放统计
  void safeRecordPlayStat({
    required bool isStart,
    required int index,
    required bool isLSMode,
    required List<Subtitle> subtitles,
  }) {
    try {
      if (index >= 0 && index < subtitles.length) {
        recordStatWithSubtitles(
          isPlay: true,
          isStart: isStart,
          index: index,
          isLSMode: isLSMode,
          now: DateTime.now().millisecondsSinceEpoch,
          subtitles: subtitles,
        );

        final action = isStart ? "开始" : "结束";
        final mode = isLSMode ? "LS" : "普通";
        logger("DataCenterTimeManager: 记录播放统计 - $action播放第$index句 ($mode模式)");
      } else {
        logger("DataCenterTimeManager: 播放统计记录失败 - 索引越界: $index/${subtitles.length}");
      }
    } catch (e) {
      logger("DataCenterTimeManager: 记录播放统计失败 - $e");
    }
  }

  /// 安全地记录录音统计
  void safeRecordRecordStat({
    required bool isStart,
    required int index,
    required bool isLSMode,
    required List<Subtitle> subtitles,
  }) {
    try {
      if (index >= 0 && index < subtitles.length) {
        recordStatWithSubtitles(
          isPlay: false,
          isStart: isStart,
          index: index,
          isLSMode: isLSMode,
          now: DateTime.now().millisecondsSinceEpoch,
          subtitles: subtitles,
        );

        final action = isStart ? "开始" : "结束";
        final mode = isLSMode ? "LS" : "普通";
        logger("DataCenterTimeManager: 记录录音统计 - $action录音第$index句 ($mode模式)");
      } else {
        logger("DataCenterTimeManager: 录音统计记录失败 - 索引越界: $index/${subtitles.length}");
      }
    } catch (e) {
      logger("DataCenterTimeManager: 记录录音统计失败 - $e");
    }
  }

  /// 安全地暂停会话
  void safePauseSession() {
    try {
      pauseSession();
      logger("DataCenterTimeManager: 会话已暂停");
    } catch (e) {
      logger("DataCenterTimeManager: 暂停会话失败 - $e");
    }
  }

  /// 安全地恢复会话
  void safeResumeSession() {
    try {
      resumeSession();
      logger("DataCenterTimeManager: 会话已恢复");
    } catch (e) {
      logger("DataCenterTimeManager: 恢复会话失败 - $e");
    }
  }

  /// 安全地上传学习数据
  Future<void> safeUploadLearningData({
    required String? resourceId,
    required int? resourceType,
    required int? currentLsTimes,
  }) async {
    try {
      if (resourceId?.isNotEmpty == true) {
        await addDataEpisodeAndUpload(
          resourceId: resourceId!,
          resourceType: resourceType ?? 2,
          currentLsTimes: currentLsTimes ?? 0,
        );
        logger("DataCenterTimeManager: 学习数据上传成功");
      } else {
        logger("DataCenterTimeManager: resourceId为空，跳过数据上传");
      }
    } catch (e) {
      logger("DataCenterTimeManager: 上传学习数据失败 - $e");
      // 不抛出异常，避免影响用户体验
    }
  }

  /// 获取学习统计摘要（用于调试）
  void logLearningStatsSummary() {
    try {
      final summary = getLearningStatsSummary();
      logger("DataCenterTimeManager 学习统计摘要:");
      logger("DataCenterTimeManager - 总句子数: ${summary['totalSentences']}");
      logger("DataCenterTimeManager - 总播放次数: ${summary['totalPlayCount']}");
      logger("DataCenterTimeManager - 总录音次数: ${summary['totalRecordCount']}");
      logger("DataCenterTimeManager - 会话时长: ${summary['sessionDuration']}秒");
    } catch (e) {
      logger("DataCenterTimeManager: 获取统计摘要失败 - $e");
    }
  }
}

/// 为detail_controller提供的便捷方法类
class DataCenterHelper {
  static final DataCenterTimeManager _manager = DataCenterTimeManager();

  /// 初始化数据中心
  static Future<void> initialize() async {
    try {
      await _manager.initialize();
      logger("DataCenterHelper: 初始化完成");
    } catch (e) {
      logger("DataCenterHelper: 初始化失败 - $e");
    }
  }

  /// 开始学习会话
  static void beginSession({
    required String? resourceId,
    required int? resourceType,
    required int? lsTimes,
  }) {
    _manager.safeBeginSession(
      resourceId: resourceId,
      resourceType: resourceType,
      lsTimes: lsTimes,
    );
  }

  /// 记录播放事件
  static void recordPlayEvent({
    required bool isStart,
    required int index,
    required bool isLSMode,
    required List<Subtitle> subtitles,
  }) {
    _manager.safeRecordPlayStat(
      isStart: isStart,
      index: index,
      isLSMode: isLSMode,
      subtitles: subtitles,
    );
  }

  /// 记录录音事件
  static void recordRecordEvent({
    required bool isStart,
    required int index,
    required bool isLSMode,
    required List<Subtitle> subtitles,
  }) {
    _manager.safeRecordRecordStat(
      isStart: isStart,
      index: index,
      isLSMode: isLSMode,
      subtitles: subtitles,
    );
  }

  /// 暂停会话
  static void pauseSession() {
    _manager.safePauseSession();
  }

  /// 恢复会话
  static void resumeSession() {
    _manager.safeResumeSession();
  }

  /// 上传学习数据
  static Future<void> uploadLearningData({
    required String? resourceId,
    required int? resourceType,
    required int? currentLsTimes,
  }) async {
    logStatsSummary();
    await _manager.safeUploadLearningData(
      resourceId: resourceId,
      resourceType: resourceType,
      currentLsTimes: currentLsTimes,
    );
  }

  /// 获取统计摘要（调试用）
  static void logStatsSummary() {
    _manager.logLearningStatsSummary();
  }

  /// 销毁资源
  static void dispose() {
    try {
      _manager.dispose();
      logger("DataCenterHelper: 资源已释放");
    } catch (e) {
      logger("DataCenterHelper: 释放资源失败 - $e");
    }
  }
}
