import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';

import 'loading_getxcontroller.dart';

abstract class LoadingGetView<T extends LoadingGetxcontroller>
    extends GetView<T> {
  const LoadingGetView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      switch (controller.viewState.value) {
        case ViewState.loading:
          return Center(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              Gap(8.whs),
              Text(controller.loadingMessage.value),
            ],
          ));
        case ViewState.error:
          return buildError(context) ??
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(controller.errorMessage.value),
                    ElevatedButton(
                      onPressed: controller.resetState,
                      child: const Text('重试'),
                    ),
                  ],
                ),
              );
        case ViewState.empty:
          return buildEmpty(context) ?? const Center(child: Text('无内容'));
        case ViewState.success:
          return buildSuccess(context);
      }
    });
  }

  Widget buildSuccess(BuildContext context);
  Widget? buildError(BuildContext context) => null;
  Widget? buildEmpty(BuildContext context) => null;
}
