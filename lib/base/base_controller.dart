import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

import '../config/config.dart';

class BaseController extends GetxController {
  final List<StreamSubscription?> subscriptions = [];
  @override
  void onInit() {
    super.onInit();
    subscriptions.clear();
    subscriptions.addAll([
      Config().netStateObs.listen((data) {
        onNetChange(data);
      }),
    ]);
  }

  @override
  void onClose() {
    for (final subscription in subscriptions) {
      subscription?.cancel();
    }
    super.onClose();
  }

  void onNetChange(List<ConnectivityResult> data) {
    if (!data.contains(ConnectivityResult.none)) {
      onNetConnected();
    } else {
      onNetDisconnected();
    }
  }

  void onNetConnected() {}

  void onNetDisconnected() {}
}
