import 'package:get/get.dart';

import 'base_controller.dart';

// 定义状态枚举
enum ViewState { loading, error, empty, success }

abstract class LoadingGetxcontroller extends BaseController {
  var viewState = ViewState.loading.obs;
  var errorMessage = ''.obs;
  var loadingMessage = ''.obs;

  void setLoading({String message = '加载中...'}) {
    loadingMessage(message);
    viewState(ViewState.loading);
  }

  void setError({String message = "错误"}) {
    errorMessage(message);
    viewState(ViewState.error);
  }

  void setEmpty() {
    viewState(ViewState.empty);
  }

  void setSuccess() {
    viewState(ViewState.success);
  }

  void resetState() {
    viewState(ViewState.loading);
    loadingMessage('');
    errorMessage('');
  }
}
