import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:flutter/material.dart';
import 'package:lsenglish/video/media_player.dart';

enum MyAudioProcessingState {
  idle,
  loading,
  buffering,
  ready,
  completed,
  error,
}

class MyAudioHandler extends BaseAudioHand<PERSON> with <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, WidgetsBindingObserver {
  StreamSubscription<bool>? _playingubscription;
  StreamSubscription<Duration>? _durationScription;
  final _baseItem = const MediaItem(
    id: 'MyAudioHandler',
    album: "This is Album",
    title: "This is title",
    artist: "This is artist",
    duration: Duration.zero,
  );
  var playing = false;
  Duration _duration = Duration.zero;
  final MediaPlayer _mediaPlayer;
  MyAudioProcessingState myAudioProcessingState = MyAudioProcessingState.idle;
  MyAudioHandler(this._mediaPlayer) {
    mediaItem.add(_baseItem);
  }

  void initPlayer() async {
    WidgetsBinding.instance.removeObserver(this);
    WidgetsBinding.instance.addObserver(this);
    _playingubscription = getPlayer()?.getPlayer.stream.playing.listen((event) async {
      playing = event;
      myAudioProcessingState = MyAudioProcessingState.ready;
      _transformEvent();
    });
    _durationScription = getPlayer()?.getPlayer.stream.duration.listen((event) async {
      _duration = event;
    });
  }

  void ready() {
    playing = true;
    myAudioProcessingState = MyAudioProcessingState.ready;
    _transformEvent();
  }

  void idle() {
    playing = false;
    _playingubscription?.cancel();
    _durationScription?.cancel();
    myAudioProcessingState = MyAudioProcessingState.idle;
    _transformEvent();
    WidgetsBinding.instance.removeObserver(this);
  }

  void buffering() {
    myAudioProcessingState = MyAudioProcessingState.buffering;
    _transformEvent();
  }

  void completed() {
    myAudioProcessingState = MyAudioProcessingState.completed;
    _transformEvent();
  }

  void _transformEvent() {
    var modifiedMediaItem = _baseItem.copyWith(
      duration: _duration,
    );
    mediaItem.add(modifiedMediaItem);
    var ps = PlaybackState(
      controls: [
        MediaControl.rewind,
        if (playing) MediaControl.pause else MediaControl.play,
        MediaControl.fastForward,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
      },
      androidCompactActionIndices: const [0, 1, 2],
      processingState: const {
        MyAudioProcessingState.idle: AudioProcessingState.idle,
        MyAudioProcessingState.loading: AudioProcessingState.loading,
        MyAudioProcessingState.buffering: AudioProcessingState.buffering,
        MyAudioProcessingState.ready: AudioProcessingState.ready,
        MyAudioProcessingState.completed: AudioProcessingState.completed,
      }[myAudioProcessingState]!,
      playing: playing,
      updatePosition: getPlayer()?.getPlayer.state.position ?? Duration.zero,
      bufferedPosition: getPlayer()?.getPlayer.state.buffer ?? Duration.zero,
      speed: 1,
      queueIndex: 0,
    );
    playbackState.add(ps);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      playing = true;
      play();
      _transformEvent();
    } else if (state == AppLifecycleState.resumed) {
      playing = true;
      play();
      _transformEvent();
    }
  }

  MediaPlayer? getPlayer() {
    return _mediaPlayer;
  }

  @override
  Future<void> play() async {
    getPlayer()?.play();
  }

  @override
  Future<void> pause() async {
    getPlayer()?.pause();
  }

  @override
  Future<void> stop() async {
    getPlayer()?.stop();
  }

  @override
  Future<void> seek(Duration position) async {
    getPlayer()?.seek(position);
  }

  @override
  Future<void> skipToQueueItem(int index) {
    return super.skipToQueueItem(index);
  }
}
