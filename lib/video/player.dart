import 'dart:async';
import 'dart:io';

import 'package:audio_session/audio_session.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/log.dart' as log;
import 'package:lsenglish/utils/routes.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/subtitle/src/utils/subtitle_controller.dart';
import 'package:lsenglish/utils/util.dart';
import 'package:lsenglish/utils/video.dart';
import 'package:media_kit/media_kit.dart';
import 'package:path/path.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'audio_handler.dart';

/// LS模式状态枚举
enum LsModeState {
  idle, // 空闲状态 - 没有播放任何句子
  playing, // 播放中 - 正在播放当前句子
  paused, // 暂停状态 - 句子播放完毕后暂停
  seeking, // 跳转中 - 正在跳转到新的句子
}

abstract class IPlayer {
  static MyAudioHandler? audioHandler;
  void init();
  Future<void> play();
  Future<void> pause();
  Future<void> mute();
  Future<void> unmute();
  void stop();
  Future open(String path);
  bool isPlaying();
  Future<void> loadSubtitles(String subtitlePath, {String nativeSubtitlePath});
  Future<void> seekBySubtitleIndex(int index);
  void preSubtitle();
  void nextSubtitle();
  void switchSubtitleMode();
  Subtitle? refreshSubtitle(Duration duration, {bool updateSubtitleIndex = true});
  void delaySubtitle(int milliseconds);
  void enableOnlyPlayLines();
  void disableOnlyPlayLines();
  void switchOnlyPlayLines();
  void enableOpenSingleRepeat();
  void disableOpenSingleRepeat();
  void switchOpenSingleRepeat();
  Future<void> switchSpeed();
  void playOrPause();
  Future<void> seek(Duration duration);
  Future<void> onPosition(Duration duration);
  void toggleFullscreen();
  void showAudioService();
  void dismissAudioService();
  Future<Uint8List?> screenshot();
  Future<String?> screenshot2File();
  void destory();
  Future<void> resetLsModeIndex({bool needPlay = true});
  void resetOnHorizontalDragEndTime();
  Future<void> onVideoSeekChangeEnd();
  Future<void> seekByHistory(int positionInMilliseconds);
  bool currentDurationIsMatchCurrentSubtitleEndInLsMode({int index = -1});
  Future<void> enterNativeFullscreen();
  Future<void> exitNativeFullscreen();
  Future<void> unlockOrientation();
  void onVideoLoadFinish();
  bool isUnlockingOrientation = false;
  //是否已经完成了根据历史进度来跳转
  bool finishSeekByHistory = false;
  RxInt seekByHistoryIndex = 0.obs;
  RxBool pausedInLsMode = false.obs;
  RxBool openLsMode = true.obs;
  RxList<Subtitle> subtitles = <Subtitle>[].obs;
  RxInt currentSubtitleIndex = 0.obs;
  //发生了跳过逻辑时，currentSubtitleIndex被修改，seekSkipSubtitleIndex同步更新
  RxInt seekSkipSubtitleIndex = 0.obs;
  RxInt currentSubtitleMode = SubtitleMode.bilingual.index.obs;
  int currentPositionInMilliseconds = 0;
  RxDouble currentSpeed = 1.0.obs;
  //仅播放台词
  RxBool onlyPlayLines = false.obs;
  //是否打开单句循环播放
  RxBool openSingleRepeat = false.obs;
  //判断当前的视频时间中，是不是没有字幕
  RxBool subtitleNone = false.obs;
  SubtitleController? subtitleController;
  RxBool playing = false.obs;
  //LS模式下是否自动播放录音
  bool autoPlayRecordInLsMode = true;
  //LS模式下是否自动开始录音
  bool autoStartRecordInLsMode = true;
  final _playerConfig = Config().playerConfig;
  int _currentLsModeIndex = -1;
  String subtitlePath = "";
  String _videoPath = "";
  int _currentSingleRepeatNum = 0;
  int _currentSingleRepeatIndex = -1;
  int onHorizontalDragEndTime = 0;
  List<int> skipList = [];
  //第一次进入 服务端获取到的进度
  int positionInit = 0;
  bool skipFindReverse = false;
  final Completer<void> _videoLoadFinish = Completer<void>();

  // LS模式状态管理
  LsModeState _lsModeState = LsModeState.idle;
  Timer? _lsStateTimer; // LS状态管理Timer

  //所有的变量都需要重置
  IPlayer() {
    reset();
  }
  void logger(String msg) {
    // log.logger("PlayerLogger $msg");
  }

  /// 检查LS模式下的状态一致性
  void _checkLsModeStateConsistency(String context) {
    if (openLsMode.value && subtitles.isNotEmpty) {
      var currentDuration = Duration(milliseconds: currentPositionInMilliseconds);
      var subtitle = subtitleController?.durationSearch(currentDuration);
      var expectedIndex = subtitle?.subtitleIndex ?? -1;

      if (_currentLsModeIndex != -1 && expectedIndex != -1 && _currentLsModeIndex != expectedIndex) {
        logger("[$context] LS mode state inconsistency detected:");
        logger("  _currentLsModeIndex: $_currentLsModeIndex");
        logger("  expectedIndex: $expectedIndex");
        logger("  currentSubtitleIndex: ${currentSubtitleIndex.value}");
        logger("  currentPosition: ${currentPositionInMilliseconds}ms");

        // 添加更详细的信息
        if (_currentLsModeIndex < subtitles.length) {
          var currentSubtitle = subtitles[_currentLsModeIndex];
          logger("  current subtitle time range: ${currentSubtitle.start} - ${currentSubtitle.end}");
        }
        if (expectedIndex < subtitles.length) {
          var expectedSubtitle = subtitles[expectedIndex];
          logger("  expected subtitle time range: ${expectedSubtitle.start} - ${expectedSubtitle.end}");
        }
        logger("  pausedInLsMode: ${pausedInLsMode.value}");
        logger("  _lsModeState: $_lsModeState");
      }
    }
  }

  void reset() {
    logger("---------reset---------");
    logger("Resetting all player states:");

    subtitles = <Subtitle>[].obs;
    currentSubtitleIndex = 0.obs;
    seekSkipSubtitleIndex = 0.obs;
    currentSubtitleMode = SubtitleMode.bilingual.index.obs;
    currentPositionInMilliseconds = 0;
    currentSpeed = 1.0.obs;
    onlyPlayLines = false.obs;
    subtitleNone = false.obs;
    openSingleRepeat = false.obs;
    openLsMode = true.obs;
    finishSeekByHistory = false;
    _currentLsModeIndex = -1;
    subtitlePath = "";
    _videoPath = "";
    _currentSingleRepeatNum = 0;
    _currentSingleRepeatIndex = -1;
    playing = false.obs;
    autoPlayRecordInLsMode = true;
    autoStartRecordInLsMode = true;
    positionInit = -1;
    skipFindReverse = false;
    pausedInLsMode = false.obs;
    seekByHistoryIndex = 0.obs;

    // 重置LS模式状态管理
    _lsModeState = LsModeState.idle;
    _lsStateTimer?.cancel();
    _lsStateTimer = null;

    logger("Reset completed - all states initialized");
  }

  static Future<void> ensureInitialized() async {
    void handleReceiveSharingIntent(List<SharedMediaFile> fileList) async {
      var filePath = "";
      for (var i = 0; i < fileList.length; i++) {
        var tempPath = fileList[i].path;
        var videoPath = await FileUtils().findVideoFilePath((await FileUtils().getSaveDir()).path, basenameWithoutExtension(fileList[i].path));
        log.logger("handleReceiveSharingIntent findVideoFilePath videoPath=$videoPath");
        var needMoveToDocuments = videoPath == null || videoPath == "";
        if (needMoveToDocuments) {
          tempPath = await FileUtils().moveFileToDocuments(fileList[i].path);
        }
        if (i == 0) {
          filePath = tempPath;
        }
      }
      RoutesUtil().goDetailByPath(filePath);
    }

    MediaKit.ensureInitialized();
    //TODO 下拉状态栏就出问题
    // IPlayer.audioHandler = await AudioService.init(
    //   builder: () => MyAudioHandler(),
    //   config: const AudioServiceConfig(
    //     androidNotificationChannelId: 'com.mikaelzero.lsenglish',
    //     androidNotificationChannelName: 'Music playback',
    //   ),
    // );
    ReceiveSharingIntent.instance.getMediaStream().listen((value) {
      log.logger("----- ReceiveSharingIntent ------- $value");
      handleReceiveSharingIntent(value);
    }, onError: (err) {
      log.logger("getIntentDataStream error: $err");
    });
    ReceiveSharingIntent.instance.getInitialMedia().then((value) {
      log.logger("----- ReceiveSharingIntent getInitialMedia------- $value");
      handleReceiveSharingIntent(value);
      ReceiveSharingIntent.instance.reset();
    }, onError: (err) {
      log.logger("getIntentDataStream222 error: $err");
    });
  }
}

abstract class BasePlayer extends IPlayer {
  @override
  void init() async {
    _currentSingleRepeatNum = _playerConfig.singleRepeatCount;
  }

  @override
  Future open(String path) async {
    logger("open() called with path: $path");
    _videoPath = path;
    IPlayer.audioHandler?.initPlayer();
  }

  Future<String> getSubtitleByVideo(String videoPath) async {
    var subtitleName = basenameWithoutExtension(videoPath);
    logger("getSubtitleByVideo subtitleName = $subtitleName");
    var subtitlePath = "${(await FileUtils().getSaveSubtitleDir()).path}/$subtitleName.srt";
    logger("getSubtitleByVideo subtitlePath = $subtitlePath");
    var result = await FFmpegUtils().extractSubtitles(videoPath, subtitlePath);
    logger("getSubtitleByVideo result = $result");
    if (result) {
      SPUtil().saveHistory(videoPath, subtitleLocalPath: subtitlePath);
      return subtitlePath;
    }
    return "";
  }

  @override
  Future<void> onPosition(Duration duration) async {
    logger("onPosition() called with duration: $duration, finishSeekByHistory: $finishSeekByHistory");
    currentPositionInMilliseconds = duration.inMilliseconds;
    //应该有个逻辑  因为会有跳转到指定的位置的逻辑 所以在跳转到指定的位置之前 onPosition 里的逻辑不应该被调用
    //选择了字幕之后 也需要调用 seekHistory 否则会导致这里一直为false
    if (!finishSeekByHistory) {
      logger("onPosition() finishSeekByHistory is false, skipping logic. positionInit: $positionInit");
      return;
    }
    IPlayer.audioHandler?.ready();
    if (subtitles.isEmpty) {
      logger("onPosition() subtitles is empty, returning");
      return;
    }

    bool seeked = await _seekNextIndexWhenNeedSkip();
    if (seeked) {
      logger("onPosition() seeked to next index, returning");
      return;
    }
    if (openLsMode.value) {
      logger("onPosition() handling LS mode");
      _checkLsModeStateConsistency("onPosition");
      handleLsMode(duration);
    } else {
      //暂定 如果是ls模式 不应该触发这些逻辑 因为都是一句一句播放
      if (onlyPlayLines.value) {
        logger("onPosition() handling only play lines");
        await handleOnlyPlayLines(duration);
      }
      if (openSingleRepeat.value) {
        logger("onPosition() handling single repeat");
        await handleOpenSingleRepeat(duration);
      } else {
        logger("onPosition() refreshing subtitle");
        refreshSubtitle(duration);
      }
    }
  }

  /// 简化的LS模式处理逻辑
  /// 基于状态机的设计，消除复杂的条件判断
  Future<void> handleLsMode(Duration duration) async {
    logger("handleLsMode() called with duration: $duration, state: $_lsModeState, index: $_currentLsModeIndex");

    try {
      switch (_lsModeState) {
        case LsModeState.idle:
          await _handleIdleState(duration);
          break;
        case LsModeState.playing:
          await _handlePlayingState(duration);
          break;
        case LsModeState.paused:
          // 暂停状态下不需要处理，等待用户操作
          break;
        case LsModeState.seeking:
          // 跳转状态下不需要处理，等待跳转完成
          break;
      }
    } catch (e) {
      logger("handleLsMode() error: $e");
      await _resetLsModeState();
    }
  }

  /// 处理空闲状态 - 寻找当前位置的字幕并开始播放
  Future<void> _handleIdleState(Duration duration) async {
    var subtitle = refreshSubtitle(duration);
    if (subtitle == null) {
      logger("_handleIdleState() no subtitle found, seeking next");
      await seekExistNextSubtitleOrPreSubtitle(duration);
      return;
    }

    // 找到字幕，开始播放
    await _startPlayingSubtitle(subtitle.subtitleIndex);
  }

  /// 处理播放状态 - 检查是否需要暂停
  Future<void> _handlePlayingState(Duration duration) async {
    if (_currentLsModeIndex == -1 || _currentLsModeIndex >= subtitles.length) {
      logger("_handlePlayingState() invalid index, resetting");
      await _resetLsModeState();
      return;
    }

    var currentSubtitle = subtitles[_currentLsModeIndex];
    var currentTime = duration.inMilliseconds;
    var subtitleEnd = currentSubtitle.end.inMilliseconds;

    // 检查是否播放完毕
    if (currentTime >= subtitleEnd) {
      logger("_handlePlayingState() subtitle finished, pausing");
      await _pauseAtSubtitleEnd();
    }
  }

  /// 开始播放指定字幕
  Future<void> _startPlayingSubtitle(int index) async {
    logger("_startPlayingSubtitle() index: $index");

    _lsModeState = LsModeState.playing;
    _currentLsModeIndex = index;
    currentSubtitleIndex.value = index;
    pausedInLsMode.value = false;

    logger("LS State: idle -> playing (index: $index)");
  }

  /// 在字幕结束时暂停
  Future<void> _pauseAtSubtitleEnd() async {
    logger("_pauseAtSubtitleEnd() pausing at index: $_currentLsModeIndex");

    await pause();
    _lsModeState = LsModeState.paused;
    pausedInLsMode.value = true;
    pausedInLsMode.refresh();

    logger("LS State: playing -> paused");
  }

  /// 重置LS模式状态
  Future<void> _resetLsModeState() async {
    logger("_resetLsModeState() resetting all LS state");

    _lsModeState = LsModeState.idle;
    _currentLsModeIndex = -1;
    pausedInLsMode.value = false;
  }

  /// 统一的LS模式播放入口
  /// 供外部模块调用，简化各种播放场景的处理
  Future<void> playSubtitleInLsMode(int index, {bool fromUserAction = true}) async {
    logger("playSubtitleInLsMode() index: $index, fromUser: $fromUserAction");

    if (!openLsMode.value) {
      logger("playSubtitleInLsMode() not in LS mode, ignoring");
      return;
    }

    if (subtitles.isEmpty || index < 0 || index >= subtitles.length) {
      logger("playSubtitleInLsMode() invalid index or empty subtitles");
      return;
    }

    // 如果当前正在播放且是同一个索引，则暂停/继续播放
    if (_lsModeState == LsModeState.playing && _currentLsModeIndex == index) {
      if (isPlaying()) {
        logger("playSubtitleInLsMode() pausing current subtitle");
        await pause();
        _lsModeState = LsModeState.paused;
        pausedInLsMode.value = true;
      } else {
        logger("playSubtitleInLsMode() resuming current subtitle");
        await play();
        _lsModeState = LsModeState.playing;
        pausedInLsMode.value = false;
      }
      return;
    }

    // 如果是暂停状态且是同一个索引，检查是否需要重新开始
    if (_lsModeState == LsModeState.paused && _currentLsModeIndex == index) {
      if (currentDurationIsMatchCurrentSubtitleEndInLsMode(index: index)) {
        logger("playSubtitleInLsMode() restarting subtitle from beginning");
        await _seekToSubtitleInLsMode(index);
        await play();
      } else {
        logger("playSubtitleInLsMode() continuing from current position");
        await play();
        _lsModeState = LsModeState.playing;
        pausedInLsMode.value = false;
      }
      return;
    }

    // 跳转到新的字幕并播放
    await _seekToSubtitleInLsMode(index);
    await play();
  }

  Future<void> handlePauseInLsMode(Duration duration) async {
    if (_currentLsModeIndex == -1 || _currentLsModeIndex >= subtitles.length) {
      logger("handlePauseInLsMode() invalid _currentLsModeIndex: $_currentLsModeIndex");
      return;
    }

    var currentSubtitle = subtitles[_currentLsModeIndex];
    var currentSubtitleStart = currentSubtitle.start.inMilliseconds;
    var currentSubtitleEnd = currentSubtitle.end.inMilliseconds;
    var currentTime = duration.inMilliseconds;

    logger(
        "handlePauseInLsMode() currentTime: $currentTime, currentSubtitleStart: $currentSubtitleStart, currentSubtitleEnd: $currentSubtitleEnd, difference: ${currentSubtitleEnd - currentTime}");

    // 只有当当前时间真正超出字幕范围时才暂停
    // 添加一个小的容错范围（10ms），避免精度问题
    if (currentTime >= currentSubtitleEnd - 10) {
      logger("handlePauseInLsMode() pausing at index: $_currentLsModeIndex, time: $currentTime >= end: $currentSubtitleEnd");
      await pause();
      pausedInLsMode.value = true;
      pausedInLsMode.refresh();

      // 记录暂停后的状态
      logger("handlePauseInLsMode() paused successfully, pausedInLsMode: ${pausedInLsMode.value}");
    } else {
      logger("handlePauseInLsMode() not pausing yet, remaining time: ${currentSubtitleEnd - currentTime}ms");
    }
  }

  Future<void> seekExistNextSubtitleOrPreSubtitle(Duration duration) async {
    logger("seekExistNextSubtitleOrPreSubtitle() called with duration: $duration");
    var subtitle = subtitleController?.findClosestSubtitleForward(duration);
    if (subtitle != null) {
      logger("seekExistNextSubtitleOrPreSubtitle() found subtitle at index ${subtitle.subtitleIndex}, seeking to ${subtitle.start}");
      await seek(subtitle.start);
      // 设置当前字幕索引，避免无限循环
      currentSubtitleIndex.value = subtitle.subtitleIndex;
      _currentLsModeIndex = subtitle.subtitleIndex;
      pausedInLsMode.value = false;
    } else {
      logger("seekExistNextSubtitleOrPreSubtitle() no subtitle found, this might cause issues");
    }
  }

  @override
  bool currentDurationIsMatchCurrentSubtitleEndInLsMode({int index = -1}) {
    if (index == -1) {
      index = _currentLsModeIndex;
    }
    if (index == -1) {
      return false;
    }
    var currentSubtitleEnd = subtitles[index].end.inMilliseconds;
    if (currentPositionInMilliseconds >= currentSubtitleEnd || currentSubtitleEnd - currentPositionInMilliseconds < 10) {
      return true;
    }
    return false;
  }

  @override
  Future<void> resetLsModeIndex({bool needPlay = true}) async {
    logger("resetLsModeIndex called, needPlay: $needPlay, current state: $_lsModeState");

    // 重置LS模式状态
    await _resetLsModeState();

    // 在LS模式下，根据当前位置确定字幕索引
    if (openLsMode.value && subtitles.isNotEmpty) {
      var currentDuration = Duration(milliseconds: currentPositionInMilliseconds);
      var subtitle = subtitleController?.durationSearch(currentDuration);
      if (subtitle != null) {
        logger("resetLsModeIndex updating currentSubtitleIndex to ${subtitle.subtitleIndex}");
        currentSubtitleIndex.value = subtitle.subtitleIndex;
      }
    }

    if (needPlay) {
      logger("resetLsModeIndex calling play()");
      await play();
    }
  }

  @override
  Future<void> onVideoSeekChangeEnd() async {
    logger("onVideoSeekChangeEnd called");

    // 记录拖动结束时间，用于录音逻辑判断
    onHorizontalDragEndTime = DateTime.now().millisecondsSinceEpoch;

    // 在LS模式下重置状态
    if (openLsMode.value) {
      logger("onVideoSeekChangeEnd resetting LS mode state");
      await _resetLsModeState();

      // 根据当前位置更新字幕索引
      if (subtitles.isNotEmpty) {
        var currentDuration = Duration(milliseconds: currentPositionInMilliseconds);
        var subtitle = subtitleController?.durationSearch(currentDuration);
        if (subtitle != null) {
          logger("onVideoSeekChangeEnd found subtitle at index ${subtitle.subtitleIndex}");
          currentSubtitleIndex.value = subtitle.subtitleIndex;
        }
      }
    }
  }

  @override
  void resetOnHorizontalDragEndTime() {
    onHorizontalDragEndTime = 0;
  }

  Future<void> handleOnlyPlayLines(Duration duration) async {
    logger("handleOnlyPlayLines() called with duration: $duration");
    if (openSingleRepeat.value) {
      if (_playerConfig.singleRepeatCount == 0) {
        logger("handleOnlyPlayLines() singleRepeatCount is 0, returning");
        return;
      }
      if (_currentSingleRepeatNum > 0) {
        logger("handleOnlyPlayLines() waiting for single repeat to finish");
        return;
      }
    }
    if (currentSubtitleIndex.value + 1 >= subtitles.length) {
      logger("handleOnlyPlayLines() seeking to next subtitle index: ${currentSubtitleIndex.value + 1}");
      await seekBySubtitleIndex(currentSubtitleIndex.value + 1);
    } else {
      var nextSubtitleStart = subtitles[currentSubtitleIndex.value + 1].start.inMilliseconds;
      var currentSubtitleEnd = subtitles[currentSubtitleIndex.value].end.inMilliseconds;
      logger("handleOnlyPlayLines() nextSubtitleStart: $nextSubtitleStart, currentSubtitleEnd: $currentSubtitleEnd");
      //当当前这句和下一句之间的时间差距大于1.5秒的时候，才进行跳转，否则直接让视频播放，更加顺畅一些
      if (nextSubtitleStart - currentSubtitleEnd >= 1500) {
        logger("handleOnlyPlayLines() seeking to next subtitle due to time gap");
        await seekBySubtitleIndex(currentSubtitleIndex.value + 1);
      }
    }
  }

  Future<void> handleOpenSingleRepeat(Duration duration) async {
    logger("handleOpenSingleRepeat() called with duration: $duration");
    var subtitleEnd = subtitles[currentSubtitleIndex.value].end.inMilliseconds;

    if (_currentSingleRepeatIndex != currentSubtitleIndex.value) {
      logger("handleOpenSingleRepeat() resetting repeat count to: ${_playerConfig.singleRepeatCount}");
      _currentSingleRepeatNum = _playerConfig.singleRepeatCount;
    }
    if (currentPositionInMilliseconds >= subtitleEnd) {
      logger("handleOpenSingleRepeat() reached subtitle end, current repeat count: $_currentSingleRepeatNum");
      //_playerConfig.singleRepeatCount为0 代表无限次数
      if (_playerConfig.singleRepeatCount == 0) {
        logger("handleOpenSingleRepeat() infinite repeat, seeking to current subtitle");
        await seekBySubtitleIndex(currentSubtitleIndex.value);
      } else {
        if (_currentSingleRepeatNum > 0) {
          // 如果还有剩余次数，继续重复当前字幕
          logger("handleOpenSingleRepeat() repeating current subtitle, remaining count: $_currentSingleRepeatNum");
          _currentSingleRepeatIndex = currentSubtitleIndex.value;
          await seekBySubtitleIndex(currentSubtitleIndex.value);
          _currentSingleRepeatNum--;
        }
        if (_currentSingleRepeatNum <= 0) {
          logger("handleOpenSingleRepeat() repeat finished, refreshing subtitle");
          refreshSubtitle(duration);
        }
      }
    } else {
      logger("handleOpenSingleRepeat() refreshing subtitle without updating index");
      refreshSubtitle(duration, updateSubtitleIndex: false);
    }
  }

  @override
  Future<void> seekByHistory(int positionInMilliseconds) async {
    logger("seekByHistory  positionInMilliseconds = $positionInMilliseconds");
    //确保只被调用一次
    if (finishSeekByHistory) {
      logger("seekByHistory already finished, returning");
      return;
    }

    // 标记开始历史跳转，防止onPosition逻辑干扰
    logger("seekByHistory starting, setting finishSeekByHistory = false");

    //如果是历史记录跳转的话 应该定位到当前台词的start时间
    logger("seekByHistory subtitles size = ${subtitles.length} positionInMilliseconds=$positionInMilliseconds");
    if (subtitles.isEmpty) {
      currentSubtitleIndex.value = 0;
      logger("seekByHistory subtitle empty positionInMilliseconds = $positionInMilliseconds");
      await seek(Duration(milliseconds: positionInMilliseconds));
      // 重置LS模式相关状态
      _currentLsModeIndex = -1;
      finishSeekByHistory = true;
      seekByHistoryIndex.value = currentSubtitleIndex.value;
      logger("seekByHistory completed for empty subtitles, finishSeekByHistory = true");
      return;
    }

    var subtitle = subtitleController?.durationSearch(Duration(milliseconds: positionInMilliseconds));
    if (subtitle != null) {
      currentSubtitleIndex.value = subtitle.subtitleIndex;
      logger("seekByHistory has subtitle currentSubtitleIndex = ${currentSubtitleIndex.value} subtitle.start=${subtitle.start}");
      await seek(subtitle.start);
      // 同步LS模式索引
      _currentLsModeIndex = currentSubtitleIndex.value;
      finishSeekByHistory = true;
      seekByHistoryIndex.value = currentSubtitleIndex.value;
      logger("seekByHistory completed with subtitle, finishSeekByHistory = true, _currentLsModeIndex = $_currentLsModeIndex");
      return;
    }

    // 处理跳过逻辑
    await _seekNextIndexWhenNeedSkip();
    logger("seekByHistory default positionInMilliseconds = $positionInMilliseconds");
    await seek(Duration(milliseconds: positionInMilliseconds));
    // 重置LS模式相关状态
    _currentLsModeIndex = -1;
    finishSeekByHistory = true;
    seekByHistoryIndex.value = currentSubtitleIndex.value;
    logger("seekByHistory completed with default seek, finishSeekByHistory = true");
    return;
  }

  @override
  Future<void> loadSubtitles(String subtitlePath, {String nativeSubtitlePath = ""}) async {
    if (subtitlePath.isNotEmpty) {
      this.subtitlePath = subtitlePath;
      // var data = nativeSubtitlePath.isEmpty
      //     ? (await compute(loadSubtitlesInternal, subtitlePath))
      //     : (await compute(loadMultiSubtitlesInternal, Pair(subtitlePath, nativeSubtitlePath)));

      var data = nativeSubtitlePath.isNotEmpty
          ? await loadMultiSubtitlesInternal(Pair(subtitlePath, nativeSubtitlePath))
          : await loadSubtitlesInternal(subtitlePath);
      subtitleController = data.item1;
      subtitles.assignAll(data.item2);
      if (currentSubtitleIndex.value >= subtitles.length) {
        currentSubtitleIndex.value = subtitles.length - 1;
        _currentLsModeIndex = currentSubtitleIndex.value;
      }
    }
    logger("loadSubtitles finishSeekByHistory = $finishSeekByHistory subtitles size = ${subtitles.length}");
    if (!finishSeekByHistory && positionInit > 0) {
      logger("loadSubtitles await _videoLoadFinish");
      await Future.wait([
        _videoLoadFinish.future,
      ]);
      logger("loadSubtitles seekByHistory = $positionInit");
      await seekByHistory(positionInit);
      logger("loadSubtitles play after seekByHistory");
      play();
    } else {
      // 如果没有历史进度或已经完成历史跳转，标记为完成
      if (!finishSeekByHistory) {
        logger("loadSubtitles no history position, marking finishSeekByHistory = true");
        finishSeekByHistory = true;
        seekByHistoryIndex.value = currentSubtitleIndex.value;
      }
      //加载了字幕之后 需要根据当前的时间显示对应的字幕
      logger("loadSubtitles calling onPosition with current position");
      await onPosition(Duration(milliseconds: currentPositionInMilliseconds));
    }
  }

  @override
  void onVideoLoadFinish() {
    if (_videoLoadFinish.isCompleted) {
      return;
    }
    _videoLoadFinish.complete();
  }

  @override
  void preSubtitle() async {
    currentSubtitleIndex.value = currentSubtitleIndex.value - 1;
    await seekBySubtitleIndex(currentSubtitleIndex.value);
  }

  @override
  void nextSubtitle() async {
    currentSubtitleIndex.value = currentSubtitleIndex.value + 1;
    await seekBySubtitleIndex(currentSubtitleIndex.value);
  }

  @override
  Future<void> seekBySubtitleIndex(int index) async {
    logger("seekBySubtitleIndex() called with index: $index");
    if (subtitles.isEmpty) {
      logger("seekBySubtitleIndex() subtitles is empty, returning");
      return;
    }

    var newIndex = index;
    if (newIndex < 0) {
      logger("seekBySubtitleIndex() adjusting index from $newIndex to 0");
      newIndex = 0;
    }
    if (newIndex >= subtitles.length) {
      logger("seekBySubtitleIndex() adjusting index from $newIndex to ${subtitles.length - 1}");
      newIndex = subtitles.length - 1;
    }

    // 在LS模式下使用简化的跳转逻辑
    if (openLsMode.value) {
      await _seekToSubtitleInLsMode(newIndex);
    } else {
      // 非LS模式下的常规跳转
      currentSubtitleIndex.value = newIndex;
      _currentLsModeIndex = newIndex;
      await seek(subtitles[newIndex].start);
    }

    _checkLsModeStateConsistency("seekBySubtitleIndex");
  }

  /// LS模式下的字幕跳转逻辑
  Future<void> _seekToSubtitleInLsMode(int index) async {
    logger("_seekToSubtitleInLsMode() seeking to index: $index");

    // 设置跳转状态
    _lsModeState = LsModeState.seeking;

    // 更新索引
    currentSubtitleIndex.value = index;
    _currentLsModeIndex = index;

    // 重置暂停状态
    pausedInLsMode.value = false;

    // 跳转到字幕开始位置
    await seek(subtitles[index].start);

    // 跳转完成，设置为播放状态
    _lsModeState = LsModeState.playing;

    logger("LS State: seeking -> playing (index: $index)");
  }

  @override
  Subtitle? refreshSubtitle(Duration duration, {bool updateSubtitleIndex = true}) {
    var subtitle = subtitleController?.durationSearch(duration);
    subtitleNone.value = subtitle == null;
    if (subtitle != null && subtitle.subtitleIndex >= 0) {
      //如果打开了openSingleRepeat，那么index的不做处理，否则会导致错乱
      if (currentSubtitleIndex.value != subtitle.subtitleIndex && updateSubtitleIndex) {
        currentSubtitleIndex.value = subtitle.subtitleIndex;
      }
    }
    return subtitle;
  }

  /////不相等说明需要调整到新的为止 需要调用的场景:
  ///1. 根据历史存的进度跳转后
  ///2. 用户主动滑动了page
  ///3. 用户滑动了进度条
  Future<bool> _seekNextIndexWhenNeedSkip() async {
    var skip2Index = _getNextSkipIndex(currentSubtitleIndex.value, reverse: skipFindReverse);
    if (skip2Index == -1) {
      await seekBySubtitleIndex(currentSubtitleIndex.value);
      seekSkipSubtitleIndex.value = currentSubtitleIndex.value;
      return true;
    }
    if (skip2Index == currentSubtitleIndex.value) {
      return false;
    }
    seekSkipSubtitleIndex.value = skip2Index;
    seekSkipSubtitleIndex.refresh();
    await seekBySubtitleIndex(skip2Index);
    return true;
  }

  //reverse 为false代表从0-100的顺序 true为100到0的顺序寻找
  // -1 说明没有找到
  int _getNextSkipIndex(int index, {bool reverse = false}) {
    if (!reverse) {
      // 顺序从 0 到 100
      if (index > subtitles.length - 1) {
        return -1;
      }
      if (skipList.contains(index)) {
        return _getNextSkipIndex(index + 1, reverse: reverse);
      }
      return index;
    } else {
      // 逆序从 100 到 0
      if (index < 0) {
        return -1;
      }
      if (skipList.contains(index)) {
        return _getNextSkipIndex(index - 1, reverse: reverse);
      }
      return index;
    }
  }

  @override
  Future<void> play() async {
    logger("play() called");
  }

  @override
  Future<void> pause() async {
    logger("pause() called");
  }

  @override
  void playOrPause() {
    logger("playOrPause() called, current playing state: ${playing.value}");
  }

  @override
  void delaySubtitle(int milliseconds) {
    logger("delaySubtitle milliseconds = $milliseconds");
    subtitleController?.delaySubtitles(Duration(milliseconds: milliseconds));
    subtitles.value = subtitleController?.subtitles ?? [];
    subtitles.refresh();
  }

  @override
  void switchSubtitleMode() {
    currentSubtitleMode.value = (currentSubtitleMode.value + 1) % modes.length;
  }

  @override
  void showAudioService() {
    IPlayer.audioHandler?.ready();
  }

  @override
  void dismissAudioService() async {
    IPlayer.audioHandler?.idle();

    try {
      final session = await AudioSession.instance;
      // 释放音频焦点
      await session.setActive(false);
    } catch (e) {
      logger("dismissAudioService error $e");
    }
  }

  @override
  void toggleFullscreen() {
    logger("toggleFullscreen isLandscape=${Get.context?.isLandscape}");
    if (Get.context?.isLandscape == true) {
      exitNativeFullscreen();
    } else {
      enterNativeFullscreen();
    }
  }

  @override
  void disableOpenSingleRepeat() {
    openSingleRepeat.value = false;
  }

  @override
  void enableOpenSingleRepeat() {
    openSingleRepeat.value = true;
  }

  @override
  void switchOpenSingleRepeat() {
    if (openSingleRepeat.value) {
      disableOpenSingleRepeat();
    } else {
      enableOpenSingleRepeat();
    }
  }

  @override
  void disableOnlyPlayLines() {
    onlyPlayLines.value = false;
  }

  @override
  void enableOnlyPlayLines() {
    onlyPlayLines.value = true;
  }

  @override
  void switchOnlyPlayLines() {
    if (onlyPlayLines.value) {
      disableOnlyPlayLines();
    } else {
      enableOnlyPlayLines();
    }
    onlyPlayLines.refresh();
  }

  @override
  Future<String?> screenshot2File() async {
    var image = await screenshot();
    if (image == null) {
      return null;
    }
    return FileUtils().saveTmpAudioServiceCover(image);
  }

  @override
  Future<void> enterNativeFullscreen() async {
    logger("enterNativeFullscreen");
    try {
      if (Platform.isAndroid) {
        await Future.wait(
          [
            SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge, overlays: []),
            SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]),
          ],
        );
      } else if (Platform.isIOS) {
        await Future.wait(
          [
            SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge, overlays: []),
            SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeRight]),
          ],
        );
      } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        await const MethodChannel('com.alexmercerind/media_kit_video').invokeMethod(
          'Utils.EnterNativeFullscreen',
        );
      }
    } catch (exception, stacktrace) {
      logger(exception.toString());
      logger(stacktrace.toString());
    }
  }

  @override
  Future<void> exitNativeFullscreen() async {
    logger("exitNativeFullscreen");
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await Future.wait(
          [
            SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge, overlays: SystemUiOverlay.values),
            SystemChrome.setPreferredOrientations(
              [DeviceOrientation.portraitUp],
            ),
          ],
        );
      } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        await const MethodChannel('com.alexmercerind/media_kit_video').invokeMethod(
          'Utils.ExitNativeFullscreen',
        );
      }
      // unlockOrientation();
    } catch (exception, stacktrace) {
      logger(exception.toString());
      logger(stacktrace.toString());
    }
  }

  //TODO 效果不行 总是会先横屏再竖屏 先不使用
  @override
  Future<void> unlockOrientation() async {
    logger("unlockOrientation isUnlockingOrientation=$isUnlockingOrientation");
    if (isUnlockingOrientation) {
      return;
    }
    isUnlockingOrientation = true;
    await Future.delayed(const Duration(seconds: 3));
    if (Get.context?.isPortrait == true) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge, overlays: SystemUiOverlay.values);
      SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    }
    isUnlockingOrientation = false;
  }

  @override
  void destory() {
    logger("Player onClose");
    // 清理Timer
    _lsStateTimer?.cancel();
    _lsStateTimer = null;
    SPUtil().saveHistory(_videoPath);
    reset();
  }
}
