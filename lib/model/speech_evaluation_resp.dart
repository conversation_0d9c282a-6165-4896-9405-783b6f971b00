import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';

part 'speech_evaluation_resp.g.dart';

/// 语音评测响应模型
@JsonSerializable()
class SpeechEvaluationResp {
  final int? id;
  final String? resourceId;
  final int? resourceType;
  final String? content;
  final String? audioUrl;
  final int? startTime;
  final int? endTime;
  SpeechEvaluationResult? evalResult;
  final String? createdAt;
  final String? updatedAt;
  // 本地音频路径，用于优先播放本地录音文件
  String? localAudioPath;

  SpeechEvaluationResp({
    this.id,
    this.resourceId,
    this.resourceType,
    this.content,
    this.audioUrl,
    this.startTime,
    this.endTime,
    this.evalResult,
    this.createdAt,
    this.updatedAt,
    this.localAudioPath,
  });

  factory SpeechEvaluationResp.fromJson(Map<String, dynamic> json) => _$SpeechEvaluationRespFromJson(json);

  Map<String, dynamic> toJson() => _$SpeechEvaluationRespToJson(this);
}
