import 'package:json_annotation/json_annotation.dart';

part 'local_history_model.g.dart';

@JsonSerializable()
class LocalHistoryModel {
  String? videoLocalPath;
  String? subtitleLocalPath;

  LocalHistoryModel({
    this.videoLocalPath,
    this.subtitleLocalPath,
  });

  factory LocalHistoryModel.fromJson(Map<String, dynamic> json) {
    return _$LocalHistoryModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$LocalHistoryModelToJson(this);
}
