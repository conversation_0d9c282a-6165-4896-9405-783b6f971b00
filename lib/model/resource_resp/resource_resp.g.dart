// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resource_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResourceResp _$ResourceRespFromJson(Map<String, dynamic> json) => ResourceResp(
      id: json['id'] as String?,
      videoUrl: json['videoUrl'] as String?,
      cover: json['cover'] as String?,
      duration: (json['duration'] as num?)?.toInt(),
      type: json['type'] as String?,
      publishedAt: json['publishedAt'] as String?,
      author: json['author'] as String?,
      defaultLangTitle: json['defaultLangTitle'] as String?,
      isFeaturedContent: json['isFeaturedContent'] as bool?,
      priority: (json['priority'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ResourceRespToJson(ResourceResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'defaultLangTitle': instance.defaultLangTitle,
      'videoUrl': instance.videoUrl,
      'cover': instance.cover,
      'duration': instance.duration,
      'type': instance.type,
      'publishedAt': instance.publishedAt,
      'author': instance.author,
      'isFeaturedContent': instance.isFeaturedContent,
      'priority': instance.priority,
    };
