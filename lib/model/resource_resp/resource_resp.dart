import 'package:json_annotation/json_annotation.dart';

part 'resource_resp.g.dart';

@JsonSerializable()
class ResourceResp {
  String? id;
  String? defaultLangTitle;
  String? videoUrl;
  String? cover;
  int? duration;
  String? type;
  String? publishedAt;
  String? author;
  bool? isFeaturedContent;
  int? priority;

  ResourceResp({
    this.id,
    this.videoUrl,
    this.cover,
    this.duration,
    this.type,
    this.publishedAt,
    this.author,
    this.defaultLangTitle,
    this.isFeaturedContent,
    this.priority,
  });

  factory ResourceResp.fromJson(Map<String, dynamic> json) {
    return _$ResourceRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ResourceRespToJson(this);
}
