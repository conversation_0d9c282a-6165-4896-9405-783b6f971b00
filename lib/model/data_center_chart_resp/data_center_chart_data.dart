import 'package:json_annotation/json_annotation.dart';

part 'data_center_chart_data.g.dart';

@JsonSerializable()
class DataCenterChartData {
  int? duration;
  int? startDate;
  int? endDate;
  String? startDateString;
  String? endDateString;

  DataCenterChartData({
    this.duration,
    this.startDate,
    this.endDate,
    this.startDateString,
    this.endDateString,
  });

  factory DataCenterChartData.fromJson(Map<String, dynamic> json) => _$DataCenterChartDataFromJson(json);

  Map<String, dynamic> toJson() => _$DataCenterChartDataToJson(this);
}
