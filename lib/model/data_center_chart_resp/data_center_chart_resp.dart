import 'package:json_annotation/json_annotation.dart';

import 'data_center_chart_data.dart';

part 'data_center_chart_resp.g.dart';

@JsonSerializable()
class DataCenterChartResp {
  int? totalLearnDuration;
  List<DataCenterChartData>? datas;

  DataCenterChartResp({this.totalLearnDuration, this.datas});

  factory DataCenterChartResp.fromJson(Map<String, dynamic> json) {
    return _$DataCenterChartRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DataCenterChartRespToJson(this);
}
