// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_center_chart_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DataCenterChartData _$DataCenterChartDataFromJson(Map<String, dynamic> json) =>
    DataCenterChartData(
      duration: (json['duration'] as num?)?.toInt(),
      startDate: (json['startDate'] as num?)?.toInt(),
      endDate: (json['endDate'] as num?)?.toInt(),
      startDateString: json['startDateString'] as String?,
      endDateString: json['endDateString'] as String?,
    );

Map<String, dynamic> _$DataCenterChartDataToJson(
        DataCenterChartData instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'startDateString': instance.startDateString,
      'endDateString': instance.endDateString,
    };
