// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'w.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

W _$WFromJson(Map<String, dynamic> json) => W(
      cw: (json['cw'] as List<dynamic>?)
          ?.map((e) => Cw.fromJson(e as Map<String, dynamic>))
          .toList(),
      wb: (json['wb'] as num?)?.toInt(),
      we: (json['we'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WToJson(W instance) => <String, dynamic>{
      'cw': instance.cw,
      'wb': instance.wb,
      'we': instance.we,
    };
