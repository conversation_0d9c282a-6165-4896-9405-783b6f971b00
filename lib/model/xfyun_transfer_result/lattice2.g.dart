// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lattice2.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Lattice2 _$Lattice2FromJson(Map<String, dynamic> json) => Lattice2(
      lid: json['lid'] as String?,
      end: json['end'] as String?,
      begin: json['begin'] as String?,
      json1best: json['json_1best'] == null
          ? null
          : Json1best.fromJson(json['json_1best'] as Map<String, dynamic>),
      spk: json['spk'] as String?,
    );

Map<String, dynamic> _$La<PERSON>2To<PERSON>son(Lattice2 instance) => <String, dynamic>{
      'lid': instance.lid,
      'end': instance.end,
      'begin': instance.begin,
      'json_1best': instance.json1best,
      'spk': instance.spk,
    };
