import 'package:json_annotation/json_annotation.dart';

import 'rt.dart';

part 'st.g.dart';

@JsonSerializable()
class St {
  String? sc;
  String? pa;
  // 输出词语识别结果集合
  List<Rt>? rt;
  // 单个句子的开始时间，单位毫秒
  String? bg;
  String? rl;
  // 单个句子的结束时间，单位毫秒
  // 分离的角色编号，取值正整数，需开启角色分离的功能才返回对应的分离角色编号
  String? ed;

  St({this.sc, this.pa, this.rt, this.bg, this.rl, this.ed});

  factory St.fromJson(Map<String, dynamic> json) => _$StFromJson(json);

  Map<String, dynamic> toJson() => _$StToJson(this);
}
