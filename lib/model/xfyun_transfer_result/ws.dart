import 'package:json_annotation/json_annotation.dart';

import 'cw.dart';

part 'ws.g.dart';

@JsonSerializable()
class Ws {
	List<Cw>? cw;
  // 词语开始的帧数（注一帧 10ms），位置是相对 bg，仅支持中、英文语种
	int? wb;
  // 词语结束的帧数（注一帧 10ms），位置是相对 bg，仅支持中、英文语种
	int? we;

	Ws({this.cw, this.wb, this.we});

	factory Ws.fromJson(Map<String, dynamic> json) => _$WsFromJson(json);

	Map<String, dynamic> toJson() => _$WsToJson(this);
}
