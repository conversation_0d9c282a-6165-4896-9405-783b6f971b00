import 'package:json_annotation/json_annotation.dart';

import 'json1best.dart';

part 'lattice2.g.dart';

@JsonSerializable()
class Lattice2 {
	String? lid;
	String? end;
	String? begin;
	@Json<PERSON>ey(name: 'json_1best') 
	Json1best? json1best;
	String? spk;

	Lattice2({this.lid, this.end, this.begin, this.json1best, this.spk});

	factory Lattice2.fromJson(Map<String, dynamic> json) {
		return _$Lattice2FromJson(json);
	}

	Map<String, dynamic> toJson() => _$Lattice2ToJson(this);
}
