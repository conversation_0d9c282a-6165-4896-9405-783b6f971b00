import 'package:json_annotation/json_annotation.dart';

import 'lattice.dart';
import 'lattice2.dart';

part 'xfyun_transfer_result.g.dart';

@JsonSerializable()
class XfyunTransferResult {
	List<Lattice>? lattice;
	List<Lattice2>? lattice2;

	XfyunTransferResult({this.lattice, this.lattice2});

	factory XfyunTransferResult.fromJson(Map<String, dynamic> json) {
		return _$XfyunTransferResultFromJson(json);
	}

	Map<String, dynamic> toJson() => _$XfyunTransferResultToJson(this);
}
