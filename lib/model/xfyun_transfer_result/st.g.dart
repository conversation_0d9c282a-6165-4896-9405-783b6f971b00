// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'st.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

St _$StFrom<PERSON>son(Map<String, dynamic> json) => St(
      sc: json['sc'] as String?,
      pa: json['pa'] as String?,
      rt: (json['rt'] as List<dynamic>?)
          ?.map((e) => Rt.fromJson(e as Map<String, dynamic>))
          .toList(),
      bg: json['bg'] as String?,
      rl: json['rl'] as String?,
      ed: json['ed'] as String?,
    );

Map<String, dynamic> _$StToJson(St instance) => <String, dynamic>{
      'sc': instance.sc,
      'pa': instance.pa,
      'rt': instance.rt,
      'bg': instance.bg,
      'rl': instance.rl,
      'ed': instance.ed,
    };
