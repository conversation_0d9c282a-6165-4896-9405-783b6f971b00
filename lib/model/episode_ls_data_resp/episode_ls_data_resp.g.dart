// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'episode_ls_data_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EpisodeLsDataResp _$EpisodeLsDataRespFromJson(Map<String, dynamic> json) =>
    EpisodeLsDataResp(
      duration: (json['duration'] as num?)?.toInt(),
      episode: json['episode'] == null
          ? null
          : Episode.fromJson(json['episode'] as Map<String, dynamic>),
      episodeLsData: (json['episodeLsData'] as List<dynamic>?)
          ?.map((e) => EpisodeLsData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EpisodeLsDataRespToJson(EpisodeLsDataResp instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'episode': instance.episode,
      'episodeLsData': instance.episodeLsData,
    };
