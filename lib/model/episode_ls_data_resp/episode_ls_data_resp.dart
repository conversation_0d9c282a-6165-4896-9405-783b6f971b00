import 'package:json_annotation/json_annotation.dart';

import '../data_center_home_resp/episode.dart';
import 'episode_ls_data.dart';

part 'episode_ls_data_resp.g.dart';

@JsonSerializable()
class EpisodeLsDataResp {
  int? duration;
  Episode? episode;
  List<EpisodeLsData>? episodeLsData;

  EpisodeLsDataResp({this.duration, this.episode, this.episodeLsData});

  factory EpisodeLsDataResp.fromJson(Map<String, dynamic> json) {
    return _$EpisodeLsDataRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EpisodeLsDataRespToJson(this);
}
