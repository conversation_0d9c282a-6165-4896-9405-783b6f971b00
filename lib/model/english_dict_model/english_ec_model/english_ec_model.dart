import 'package:json_annotation/json_annotation.dart';

import 'source.dart';
import 'word.dart';

part 'english_ec_model.g.dart';

@JsonSerializable()
class EnglishEcModel {
  Source? source;
  List<Word>? word;

  EnglishEcModel({this.source, this.word});
  factory EnglishEcModel.fromJson(Map<String, dynamic> json) {
    return _$EnglishEcModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnglishEcModelToJson(this);
}
