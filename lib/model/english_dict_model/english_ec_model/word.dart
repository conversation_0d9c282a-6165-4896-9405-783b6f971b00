import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/english_dict_model/english_ec_model/wfs.dart';

import 'return_phrase.dart';
import 'trs.dart';

part 'word.g.dart';

@JsonSerializable()
class Word {
  String? usphone;
  String? ukphone;
  String? ukspeech;
  List<Trs>? trs;
  List<Wfs>? wfs;
  @JsonKey(name: 'return-phrase')
  ReturnPhrase? returnPhrase;
  String? usspeech;

  Word({
    this.usphone,
    this.ukphone,
    this.ukspeech,
    this.trs,
    this.wfs,
    this.returnPhrase,
    this.usspeech,
  });

  factory Word.fromJson(Map<String, dynamic> json) => _$WordFromJson(json);

  Map<String, dynamic> toJson() => _$WordToJson(this);
}
