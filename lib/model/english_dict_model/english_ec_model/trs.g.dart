// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trs.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Trs _$Trs<PERSON>rom<PERSON>son(Map<String, dynamic> json) => Trs(
      tr: (json['tr'] as List<dynamic>?)
          ?.map((e) => TrsSub.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TrsToJson(Trs instance) => <String, dynamic>{
      'tr': instance.tr,
    };
