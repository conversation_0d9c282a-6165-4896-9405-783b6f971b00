// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Word _$WordFromJson(Map<String, dynamic> json) => Word(
      usphone: json['usphone'] as String?,
      ukphone: json['ukphone'] as String?,
      ukspeech: json['ukspeech'] as String?,
      trs: (json['trs'] as List<dynamic>?)
          ?.map((e) => Trs.fromJson(e as Map<String, dynamic>))
          .toList(),
      wfs: (json['wfs'] as List<dynamic>?)
          ?.map((e) => Wfs.fromJson(e as Map<String, dynamic>))
          .toList(),
      returnPhrase: json['return-phrase'] == null
          ? null
          : ReturnPhrase.fromJson(
              json['return-phrase'] as Map<String, dynamic>),
      usspeech: json['usspeech'] as String?,
    );

Map<String, dynamic> _$WordToJson(Word instance) => <String, dynamic>{
      'usphone': instance.usphone,
      'ukphone': instance.ukphone,
      'ukspeech': instance.ukspeech,
      'trs': instance.trs,
      'wfs': instance.wfs,
      'return-phrase': instance.returnPhrase,
      'usspeech': instance.usspeech,
    };
