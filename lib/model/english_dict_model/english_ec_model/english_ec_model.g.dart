// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'english_ec_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnglishEcModel _$EnglishEcModelFromJson(Map<String, dynamic> json) =>
    EnglishEcModel(
      source: json['source'] == null
          ? null
          : Source.fromJson(json['source'] as Map<String, dynamic>),
      word: (json['word'] as List<dynamic>?)
          ?.map((e) => Word.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EnglishEcModelToJson(EnglishEcModel instance) =>
    <String, dynamic>{
      'source': instance.source,
      'word': instance.word,
    };
