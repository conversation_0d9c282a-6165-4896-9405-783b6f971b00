import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/english_dict_model/english_collins_model/english_collins_model.dart';
import 'package:lsenglish/model/english_dict_model/english_ec_model/english_ec_model.dart';
import 'package:lsenglish/model/english_dict_model/english_ee_model/english_ee_model.dart';
import 'package:lsenglish/model/english_dict_model/english_meta_model/english_meta_model.dart';
import 'package:lsenglish/model/english_dict_model/english_simple_model/english_simple_model.dart';
import 'package:lsenglish/model/english_dict_model/english_syno_model/english_syno_model.dart';

import 'english_blng_model/english_blng_model.dart';
import 'english_rel_word_model/english_rel_word_model.dart';
import 'english_web_trans_model/english_web_trans_model.dart';

part 'english_dict_model.g.dart';

@JsonSerializable()
class EnglishDictModel {
  EnglishEeModel? ee;
  @JsonKey(name: 'web_trans')
  EnglishWebTransModel? webTrans;
  EnglishSynoModel? syno;
  @JsonKey(name: 'blng_sents_part')
  EnglishBlngModel? blngSentsPart;
  String? input;
  EnglishCollinsModel? collins;
  EnglishMetaModel? meta;
  @JsonKey(name: 'rel_word')
  EnglishRelWordModel? relWord;
  String? le;
  EnglishSimpleModel? simple;
  String? lang;
  EnglishEcModel? ec;

  EnglishDictModel({
    this.ee,
    this.webTrans,
    this.syno,
    this.blngSentsPart,
    this.input,
    this.collins,
    this.meta,
    this.relWord,
    this.le,
    this.simple,
    this.lang,
    this.ec,
  });

  factory EnglishDictModel.fromJson(Map<String, dynamic> json) {
    return _$EnglishDictModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnglishDictModelToJson(this);
}
