import 'aligns.dart';

class Char {
  String? s;
  String? e;
  Aligns? aligns;
  String? id;

  Char({this.s, this.e, this.aligns, this.id});

  factory Char.fromJson(Map<String, dynamic> json) => Char(
        s: json['@s'] as String?,
        e: json['@e'] as String?,
        aligns: json['aligns'] == null
            ? null
            : Aligns.fromJson(json['aligns'] as Map<String, dynamic>),
        id: json['@id'] as String?,
      );

  Map<String, dynamic> toJson() => {
        '@s': s,
        '@e': e,
        'aligns': aligns?.toJson(),
        '@id': id,
      };
}
