import 'blng_sents_part.dart';

class EnglishCollinsModel {
  BlngSentsPart? blngSentsPart;

  EnglishCollinsModel({this.blngSentsPart});

  factory EnglishCollinsModel.fromJson(Map<String, dynamic> json) {
    return EnglishCollinsModel(
      blngSentsPart: json['blng_sents_part'] == null
          ? null
          : BlngSentsPart.fromJson(
              json['blng_sents_part'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'blng_sents_part': blngSentsPart?.toJson(),
      };
}
