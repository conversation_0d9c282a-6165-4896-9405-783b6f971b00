import 'src.dart';
import 'tran.dart';

class AlignedWords {
  Src? src;
  Tran? tran;

  AlignedWords({this.src, this.tran});

  factory AlignedWords.fromJson(Map<String, dynamic> json) => AlignedWords(
        src: json['src'] == null
            ? null
            : Src.fromJson(json['src'] as Map<String, dynamic>),
        tran: json['tran'] == null
            ? null
            : Tran.fromJson(json['tran'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'src': src?.toJson(),
        'tran': tran?.toJson(),
      };
}
