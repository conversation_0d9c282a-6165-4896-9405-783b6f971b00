import 'sc.dart';
import 'tc.dart';

class Aligns {
  List<Sc>? sc;
  List<Tc>? tc;

  Aligns({this.sc, this.tc});

  factory Aligns.fromJson(Map<String, dynamic> json) => Aligns(
        sc: (json['sc'] as List<dynamic>?)
            ?.map((e) => Sc.fromJson(e as Map<String, dynamic>))
            .toList(),
        tc: (json['tc'] as List<dynamic>?)
            ?.map((e) => Tc.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'sc': sc?.map((e) => e.toJson()).toList(),
        'tc': tc?.map((e) => e.toJson()).toList(),
      };
}
