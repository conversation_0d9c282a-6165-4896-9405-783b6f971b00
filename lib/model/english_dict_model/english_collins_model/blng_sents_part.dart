import 'sentence_pair.dart';
import 'trs_classify.dart';

class BlngSentsPart {
  int? sentenceCount;
  List<SentencePair>? sentencePair;
  String? more;
  List<TrsClassify>? trsClassify;

  BlngSentsPart({
    this.sentenceCount,
    this.sentencePair,
    this.more,
    this.trsClassify,
  });

  factory BlngSentsPart.fromJson(Map<String, dynamic> json) => BlngSentsPart(
        sentenceCount: json['sentence-count'] as int?,
        sentencePair: (json['sentence-pair'] as List<dynamic>?)
            ?.map((e) => SentencePair.fromJson(e as Map<String, dynamic>))
            .toList(),
        more: json['more'] as String?,
        trsClassify: (json['trs-classify'] as List<dynamic>?)
            ?.map((e) => TrsClassify.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'sentence-count': sentenceCount,
        'sentence-pair': sentencePair?.map((e) => e.toJson()).toList(),
        'more': more,
        'trs-classify': trsClassify?.map((e) => e.toJson()).toList(),
      };
}
