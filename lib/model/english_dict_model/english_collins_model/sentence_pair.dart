import 'aligned_words.dart';

class SentencePair {
  String? sentence;
  String? sentenceEng;
  String? sentenceTranslation;
  String? speechSize;
  AlignedWords? alignedWords;
  String? source;
  String? url;
  String? sentenceSpeech;

  SentencePair({
    this.sentence,
    this.sentenceEng,
    this.sentenceTranslation,
    this.speechSize,
    this.alignedWords,
    this.source,
    this.url,
    this.sentenceSpeech,
  });

  factory SentencePair.fromJson(Map<String, dynamic> json) => SentencePair(
        sentence: json['sentence'] as String?,
        sentenceEng: json['sentence-eng'] as String?,
        sentenceTranslation: json['sentence-translation'] as String?,
        speechSize: json['speech-size'] as String?,
        alignedWords: json['aligned-words'] == null
            ? null
            : AlignedWords.fromJson(
                json['aligned-words'] as Map<String, dynamic>),
        source: json['source'] as String?,
        url: json['url'] as String?,
        sentenceSpeech: json['sentence-speech'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'sentence': sentence,
        'sentence-eng': sentenceEng,
        'sentence-translation': sentenceTranslation,
        'speech-size': speechSize,
        'aligned-words': alignedWords?.toJson(),
        'source': source,
        'url': url,
        'sentence-speech': sentenceSpeech,
      };
}
