import 'package:json_annotation/json_annotation.dart';

import 'web_translation.dart';

part 'web_trans.g.dart';

@JsonSerializable()
class WebTrans {
  @JsonKey(name: 'web-translation')
  List<WebTranslation>? webTranslation;

  WebTrans({this.webTranslation});

  factory WebTrans.fromJson(Map<String, dynamic> json) {
    return _$WebTransFromJson(json);
  }

  Map<String, dynamic> toJson() => _$WebTransToJson(this);
}
