// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tran.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Tran _$Tran<PERSON>rom<PERSON>(Map<String, dynamic> json) => Tran(
      summary: json['summary'] == null
          ? null
          : Summary.fromJson(json['summary'] as Map<String, dynamic>),
      value: json['value'] as String?,
      support: (json['support'] as num?)?.toInt(),
      url: json['url'] as String?,
    );

Map<String, dynamic> _$TranTo<PERSON>son(Tran instance) => <String, dynamic>{
      'summary': instance.summary,
      'value': instance.value,
      'support': instance.support,
      'url': instance.url,
    };
