import 'package:json_annotation/json_annotation.dart';

import 'tran.dart';

part 'web_translation.g.dart';

@JsonSerializable()
class WebTranslation {
  @J<PERSON><PERSON>ey(name: '@same')
  String? same;
  String? key;
  @Json<PERSON>ey(name: 'key-speech')
  String? keySpeech;
  List<Tran>? trans;

  WebTranslation({this.same, this.key, this.keySpeech, this.trans});

  factory WebTranslation.fromJson(Map<String, dynamic> json) {
    return _$WebTranslationFromJson(json);
  }

  Map<String, dynamic> toJson() => _$WebTranslationToJson(this);
}
