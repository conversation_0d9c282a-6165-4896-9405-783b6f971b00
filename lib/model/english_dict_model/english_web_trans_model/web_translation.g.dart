// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'web_translation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WebTranslation _$WebTranslationFromJson(Map<String, dynamic> json) =>
    WebTranslation(
      same: json['@same'] as String?,
      key: json['key'] as String?,
      keySpeech: json['key-speech'] as String?,
      trans: (json['trans'] as List<dynamic>?)
          ?.map((e) => Tran.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WebTranslationToJson(WebTranslation instance) =>
    <String, dynamic>{
      '@same': instance.same,
      'key': instance.key,
      'key-speech': instance.keySpeech,
      'trans': instance.trans,
    };
