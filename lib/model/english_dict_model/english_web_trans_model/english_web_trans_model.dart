import 'package:json_annotation/json_annotation.dart';

import 'web_trans.dart';

part 'english_web_trans_model.g.dart';

@JsonSerializable()
class EnglishWebTransModel {
  @JsonKey(name: 'web_trans')
  WebTrans? webTrans;

  EnglishWebTransModel({this.webTrans});

  factory EnglishWebTransModel.fromJson(Map<String, dynamic> json) {
    return _$EnglishWebTransModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnglishWebTransModelToJson(this);
}
