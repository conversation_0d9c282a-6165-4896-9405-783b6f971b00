import 'package:json_annotation/json_annotation.dart';

part 'meta.g.dart';

@JsonSerializable()
class Meta {
  String? input;
  String? guessLanguage;
  String? isHasSimpleDict;
  String? le;
  String? lang;
  List<String>? dicts;

  Meta({
    this.input,
    this.guessLanguage,
    this.isHasSimpleDict,
    this.le,
    this.lang,
    this.dicts,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);

  Map<String, dynamic> toJson() => _$MetaToJson(this);
}
