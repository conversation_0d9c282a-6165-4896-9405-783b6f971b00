// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meta.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Meta _$Meta<PERSON>rom<PERSON>son(Map<String, dynamic> json) => Meta(
      input: json['input'] as String?,
      guessLanguage: json['guessLanguage'] as String?,
      isHasSimpleDict: json['isHasSimpleDict'] as String?,
      le: json['le'] as String?,
      lang: json['lang'] as String?,
      dicts:
          (json['dicts'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$<PERSON>a<PERSON>o<PERSON>(Meta instance) => <String, dynamic>{
      'input': instance.input,
      'guessLanguage': instance.guessLanguage,
      'isHasSimpleDict': instance.isHasSimpleDict,
      'le': instance.le,
      'lang': instance.lang,
      'dicts': instance.dicts,
    };
