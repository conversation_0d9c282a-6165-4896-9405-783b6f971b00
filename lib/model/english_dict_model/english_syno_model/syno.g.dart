// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'syno.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Syno _$SynoFromJson(Map<String, dynamic> json) => Syno(
      synos: (json['synos'] as List<dynamic>?)
          ?.map((e) => Syno.fromJson(e as Map<String, dynamic>))
          .toList(),
      word: json['word'] as String?,
    );

Map<String, dynamic> _$SynoToJson(Syno instance) => <String, dynamic>{
      'synos': instance.synos,
      'word': instance.word,
    };
