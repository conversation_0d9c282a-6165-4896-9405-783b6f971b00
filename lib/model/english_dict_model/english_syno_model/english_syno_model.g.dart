// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'english_syno_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnglishSynoModel _$EnglishSynoModelFromJson(Map<String, dynamic> json) =>
    EnglishSynoModel(
      ws: (json['ws'] as List<dynamic>?)
          ?.map((e) => W.fromJson(e as Map<String, dynamic>))
          .toList(),
      pos: json['pos'] as String?,
    );

Map<String, dynamic> _$EnglishSynoModelToJson(EnglishSynoModel instance) =>
    <String, dynamic>{
      'ws': instance.ws,
      'pos': instance.pos,
    };
