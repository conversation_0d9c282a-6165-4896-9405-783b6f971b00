import 'package:json_annotation/json_annotation.dart';

import 'w.dart';

part 'english_syno_model.g.dart';

@JsonSerializable()
class EnglishSynoModel {
  List<W>? ws;
  String? pos;

  EnglishSynoModel({this.ws, this.pos});

  factory EnglishSynoModel.fromJson(Map<String, dynamic> json) {
    return _$EnglishSynoModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnglishSynoModelToJson(this);
}
