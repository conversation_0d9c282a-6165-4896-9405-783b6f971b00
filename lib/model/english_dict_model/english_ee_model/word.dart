import 'package:json_annotation/json_annotation.dart';

import 'return_phrase.dart';
import 'tr.dart';

part 'word.g.dart';

@JsonSerializable()
class Word {
  List<Tr>? trs;
  String? phone;
  String? speech;
  @J<PERSON><PERSON>ey(name: 'return-phrase')
  ReturnPhrase? returnPhrase;

  Word({this.trs, this.phone, this.speech, this.returnPhrase});

  factory Word.fromJson(Map<String, dynamic> json) => _$WordFromJson(json);

  Map<String, dynamic> toJson() => _$WordToJson(this);
}
