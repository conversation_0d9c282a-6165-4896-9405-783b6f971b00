// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tr.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Tr _$Tr<PERSON>rom<PERSON>son(Map<String, dynamic> json) => Tr(
      pos: json['pos'] as String?,
      tr: (json['tr'] as List<dynamic>?)
          ?.map((e) => Tr.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TrToJson(Tr instance) => <String, dynamic>{
      'pos': instance.pos,
      'tr': instance.tr,
    };
