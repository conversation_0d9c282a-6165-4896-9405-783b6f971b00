// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Word _$WordFromJson(Map<String, dynamic> json) => Word(
      trs: (json['trs'] as List<dynamic>?)
          ?.map((e) => Tr.fromJson(e as Map<String, dynamic>))
          .toList(),
      phone: json['phone'] as String?,
      speech: json['speech'] as String?,
      returnPhrase: json['return-phrase'] == null
          ? null
          : ReturnPhrase.fromJson(
              json['return-phrase'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WordToJson(Word instance) => <String, dynamic>{
      'trs': instance.trs,
      'phone': instance.phone,
      'speech': instance.speech,
      'return-phrase': instance.returnPhrase,
    };
