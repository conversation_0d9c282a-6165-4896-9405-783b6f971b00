// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'english_dict_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnglishDictModel _$EnglishDictModelFromJson(Map<String, dynamic> json) =>
    EnglishDictModel(
      ee: json['ee'] == null
          ? null
          : EnglishEeModel.fromJson(json['ee'] as Map<String, dynamic>),
      webTrans: json['web_trans'] == null
          ? null
          : EnglishWebTransModel.fromJson(
              json['web_trans'] as Map<String, dynamic>),
      syno: json['syno'] == null
          ? null
          : EnglishSynoModel.fromJson(json['syno'] as Map<String, dynamic>),
      blngSentsPart: json['blng_sents_part'] == null
          ? null
          : EnglishBlngModel.fromJson(
              json['blng_sents_part'] as Map<String, dynamic>),
      input: json['input'] as String?,
      collins: json['collins'] == null
          ? null
          : EnglishCollinsModel.fromJson(
              json['collins'] as Map<String, dynamic>),
      meta: json['meta'] == null
          ? null
          : EnglishMetaModel.fromJson(json['meta'] as Map<String, dynamic>),
      relWord: json['rel_word'] == null
          ? null
          : EnglishRelWordModel.fromJson(
              json['rel_word'] as Map<String, dynamic>),
      le: json['le'] as String?,
      simple: json['simple'] == null
          ? null
          : EnglishSimpleModel.fromJson(json['simple'] as Map<String, dynamic>),
      lang: json['lang'] as String?,
      ec: json['ec'] == null
          ? null
          : EnglishEcModel.fromJson(json['ec'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EnglishDictModelToJson(EnglishDictModel instance) =>
    <String, dynamic>{
      'ee': instance.ee,
      'web_trans': instance.webTrans,
      'syno': instance.syno,
      'blng_sents_part': instance.blngSentsPart,
      'input': instance.input,
      'collins': instance.collins,
      'meta': instance.meta,
      'rel_word': instance.relWord,
      'le': instance.le,
      'simple': instance.simple,
      'lang': instance.lang,
      'ec': instance.ec,
    };
