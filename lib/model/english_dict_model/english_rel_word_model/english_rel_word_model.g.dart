// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'english_rel_word_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnglishRelWordModel _$EnglishRelWordModelFromJson(Map<String, dynamic> json) =>
    EnglishRelWordModel(
      word: json['word'] as String?,
      stem: json['stem'] as String?,
      rels: (json['rels'] as List<dynamic>?)
          ?.map((e) => Rel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EnglishRelWordModelToJson(
        EnglishRelWordModel instance) =>
    <String, dynamic>{
      'word': instance.word,
      'stem': instance.stem,
      'rels': instance.rels,
    };
