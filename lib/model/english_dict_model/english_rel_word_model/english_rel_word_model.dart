import 'package:json_annotation/json_annotation.dart';

import 'rel.dart';

part 'english_rel_word_model.g.dart';

@JsonSerializable()
class EnglishRelWordModel {
  String? word;
  String? stem;
  List<Rel>? rels;

  EnglishRelWordModel({this.word, this.stem, this.rels});

  factory EnglishRelWordModel.fromJson(Map<String, dynamic> json) {
    return _$EnglishRelWordModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnglishRelWordModelToJson(this);
}
