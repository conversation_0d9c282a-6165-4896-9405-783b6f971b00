// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'english_simple_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnglishSimpleModel _$EnglishSimpleModelFromJson(Map<String, dynamic> json) =>
    EnglishSimpleModel(
      query: json['query'] as String?,
      word: (json['word'] as List<dynamic>?)
          ?.map((e) => Word.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EnglishSimpleModelToJson(EnglishSimpleModel instance) =>
    <String, dynamic>{
      'query': instance.query,
      'word': instance.word,
    };
