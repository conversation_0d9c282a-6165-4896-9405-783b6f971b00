import 'package:json_annotation/json_annotation.dart';

import 'word.dart';

part 'english_simple_model.g.dart';

@JsonSerializable()
class EnglishSimpleModel {
  String? query;
  List<Word>? word;

  EnglishSimpleModel({this.query, this.word});

  factory EnglishSimpleModel.fromJson(Map<String, dynamic> json) {
    return _$EnglishSimpleModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnglishSimpleModelToJson(this);
}
