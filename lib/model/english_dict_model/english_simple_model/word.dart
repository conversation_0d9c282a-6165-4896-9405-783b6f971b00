import 'package:json_annotation/json_annotation.dart';

part 'word.g.dart';

@JsonSerializable()
class Word {
  String? usphone;
  String? ukphone;
  String? ukspeech;
  @JsonKey(name: 'return-phrase')
  String? returnPhrase;
  String? usspeech;

  Word({
    this.usphone,
    this.ukphone,
    this.ukspeech,
    this.returnPhrase,
    this.usspeech,
  });

  factory Word.fromJson(Map<String, dynamic> json) => _$WordFromJson(json);

  Map<String, dynamic> toJson() => _$WordToJson(this);
}
