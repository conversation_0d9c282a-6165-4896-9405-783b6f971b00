import 'package:json_annotation/json_annotation.dart';

import 'sentence_pair.dart';
import 'trs_classify.dart';

part 'english_blng_model.g.dart';

@JsonSerializable()
class EnglishBlngModel {
  @Json<PERSON>ey(name: 'sentence-count')
  int? sentenceCount;
  @JsonKey(name: 'sentence-pair')
  List<SentencePair>? sentencePair;
  String? more;
  @Json<PERSON>ey(name: 'trs-classify')
  List<TrsClassify>? trsClassify;

  EnglishBlngModel({
    this.sentenceCount,
    this.sentencePair,
    this.more,
    this.trsClassify,
  });

  factory EnglishBlngModel.fromJson(Map<String, dynamic> json) {
    return _$EnglishBlngModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnglishBlngModelToJson(this);
}
