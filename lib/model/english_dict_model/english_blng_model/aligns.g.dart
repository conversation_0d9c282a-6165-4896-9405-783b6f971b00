// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aligns.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Aligns _$<PERSON>rom<PERSON>(Map<String, dynamic> json) => Aligns(
      sc: (json['sc'] as List<dynamic>?)
          ?.map((e) => Sc.fromJson(e as Map<String, dynamic>))
          .toList(),
      tc: (json['tc'] as List<dynamic>?)
          ?.map((e) => Tc.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$<PERSON>gn<PERSON>o<PERSON>son(Aligns instance) => <String, dynamic>{
      'sc': instance.sc,
      'tc': instance.tc,
    };
