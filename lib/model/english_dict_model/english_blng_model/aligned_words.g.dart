// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aligned_words.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AlignedWords _$<PERSON>ord<PERSON>(Map<String, dynamic> json) => AlignedWords(
      src: json['src'] == null
          ? null
          : Src.fromJson(json['src'] as Map<String, dynamic>),
      tran: json['tran'] == null
          ? null
          : Tran.fromJson(json['tran'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$<PERSON>ordsTo<PERSON>son(AlignedWords instance) =>
    <String, dynamic>{
      'src': instance.src,
      'tran': instance.tran,
    };
