// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sentence_pair.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SentencePair _$SentencePairFromJson(Map<String, dynamic> json) => SentencePair(
      sentence: json['sentence'] as String?,
      sentenceEng: json['sentence-eng'] as String?,
      sentenceTranslation: json['sentence-translation'] as String?,
      speechSize: json['speech-size'] as String?,
      alignedWords: json['aligned-words'] == null
          ? null
          : AlignedWords.fromJson(
              json['aligned-words'] as Map<String, dynamic>),
      source: json['source'] as String?,
      url: json['url'] as String?,
      sentenceSpeech: json['sentence-speech'] as String?,
    );

Map<String, dynamic> _$SentencePairToJson(SentencePair instance) =>
    <String, dynamic>{
      'sentence': instance.sentence,
      'sentence-eng': instance.sentenceEng,
      'sentence-translation': instance.sentenceTranslation,
      'speech-size': instance.speechSize,
      'aligned-words': instance.alignedWords,
      'source': instance.source,
      'url': instance.url,
      'sentence-speech': instance.sentenceSpeech,
    };
