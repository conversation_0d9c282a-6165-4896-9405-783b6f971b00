// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'english_blng_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnglishBlngModel _$EnglishBlngModelFromJson(Map<String, dynamic> json) =>
    EnglishBlngModel(
      sentenceCount: (json['sentence-count'] as num?)?.toInt(),
      sentencePair: (json['sentence-pair'] as List<dynamic>?)
          ?.map((e) => SentencePair.fromJson(e as Map<String, dynamic>))
          .toList(),
      more: json['more'] as String?,
      trsClassify: (json['trs-classify'] as List<dynamic>?)
          ?.map((e) => TrsClassify.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EnglishBlngModelToJson(EnglishBlngModel instance) =>
    <String, dynamic>{
      'sentence-count': instance.sentenceCount,
      'sentence-pair': instance.sentencePair,
      'more': instance.more,
      'trs-classify': instance.trsClassify,
    };
