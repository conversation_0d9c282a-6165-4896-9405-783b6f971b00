// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'char.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Char _$<PERSON><PERSON><PERSON>(Map<String, dynamic> json) => Char(
      s: json['@s'] as String?,
      e: json['@e'] as String?,
      aligns: json['aligns'] == null
          ? null
          : Aligns.fromJson(json['aligns'] as Map<String, dynamic>),
      id: json['@id'] as String?,
    );

Map<String, dynamic> _$Cha<PERSON><PERSON><PERSON>son(Char instance) => <String, dynamic>{
      '@s': instance.s,
      '@e': instance.e,
      'aligns': instance.aligns,
      '@id': instance.id,
    };
