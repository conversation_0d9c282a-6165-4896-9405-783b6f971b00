import 'package:json_annotation/json_annotation.dart';

import 'src.dart';
import 'tran.dart';

part 'aligned_words.g.dart';

@JsonSerializable()
class AlignedWords {
  Src? src;
  Tran? tran;

  AlignedWords({this.src, this.tran});

  factory AlignedWords.fromJson(Map<String, dynamic> json) {
    return _$AlignedWordsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AlignedWordsToJson(this);
}
