import 'package:json_annotation/json_annotation.dart';

import 'aligns.dart';

part 'char.g.dart';

@JsonSerializable()
class Char {
  @Json<PERSON>ey(name: '@s')
  String? s;
  @<PERSON><PERSON><PERSON>ey(name: '@e')
  String? e;
  Aligns? aligns;
  @Json<PERSON>ey(name: '@id')
  String? id;

  Char({this.s, this.e, this.aligns, this.id});

  factory Char.fromJson(Map<String, dynamic> json) => _$CharFromJson(json);

  Map<String, dynamic> toJson() => _$CharToJson(this);
}
