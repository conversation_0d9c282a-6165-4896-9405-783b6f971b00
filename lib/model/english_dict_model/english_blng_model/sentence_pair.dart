import 'package:json_annotation/json_annotation.dart';

import 'aligned_words.dart';

part 'sentence_pair.g.dart';

@JsonSerializable()
class SentencePair {
  String? sentence;
  @Json<PERSON>ey(name: 'sentence-eng')
  String? sentenceEng;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sentence-translation')
  String? sentenceTranslation;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'speech-size')
  String? speechSize;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'aligned-words')
  AlignedWords? alignedWords;
  String? source;
  String? url;
  @J<PERSON><PERSON>ey(name: 'sentence-speech')
  String? sentenceSpeech;

  SentencePair({
    this.sentence,
    this.sentenceEng,
    this.sentenceTranslation,
    this.speechSize,
    this.alignedWords,
    this.source,
    this.url,
    this.sentenceSpeech,
  });

  factory SentencePair.fromJson(Map<String, dynamic> json) {
    return _$SentencePairFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SentencePairToJson(this);
}
