// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'player.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlayerConfig _$PlayerConfigFromJson(Map<String, dynamic> json) => PlayerConfig(
      subtitleTextBottomHeightRatio:
          (json['subtitleTextBottomHeightRatio'] as num?)?.toDouble() ?? 0.1,
      singleRepeatCount: (json['singleRepeatCount'] as num?)?.toInt() ?? 0,
      showSubtitleWhenRecordEnd:
          json['showSubtitleWhenRecordEnd'] as bool? ?? true,
      autoPlayRecordWhenRecordEnd:
          json['autoPlayRecordWhenRecordEnd'] as bool? ?? true,
      autoRecord: json['autoRecord'] as bool? ?? true,
      autoStopRecord: json['autoStopRecord'] as bool? ?? false,
      openSingleRepeat: json['openSingleRepeat'] as bool? ?? true,
      coverSubtitle: json['coverSubtitle'] as bool? ?? false,
      menuSort: (json['menuSort'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
    );

Map<String, dynamic> _$PlayerConfigToJson(PlayerConfig instance) =>
    <String, dynamic>{
      'subtitleTextBottomHeightRatio': instance.subtitleTextBottomHeightRatio,
      'singleRepeatCount': instance.singleRepeatCount,
      'showSubtitleWhenRecordEnd': instance.showSubtitleWhenRecordEnd,
      'autoPlayRecordWhenRecordEnd': instance.autoPlayRecordWhenRecordEnd,
      'autoRecord': instance.autoRecord,
      'autoStopRecord': instance.autoStopRecord,
      'openSingleRepeat': instance.openSingleRepeat,
      'coverSubtitle': instance.coverSubtitle,
      'menuSort': instance.menuSort,
    };
