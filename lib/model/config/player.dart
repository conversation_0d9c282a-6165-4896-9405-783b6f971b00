import 'package:json_annotation/json_annotation.dart';

part 'player.g.dart';

@JsonSerializable()
class PlayerConfig {
  //字幕文件底部距离与整个播放器的比例
  double subtitleTextBottomHeightRatio;
  // 0 代表 无限次数
  int singleRepeatCount;
  // 录制结束后显示字幕
  bool showSubtitleWhenRecordEnd;
  // 录制结束后自动播放录制声音
  bool autoPlayRecordWhenRecordEnd;
  // 自动开始录制
  bool autoRecord;
  // 自动停止录制
  bool autoStopRecord;
  // 单句循环播放
  bool openSingleRepeat;
  bool coverSubtitle;
  List<int> menuSort;

  PlayerConfig({
    this.subtitleTextBottomHeightRatio = 0.1,
    this.singleRepeatCount = 0,
    this.showSubtitleWhenRecordEnd = true,
    this.autoPlayRecordWhenRecordEnd = true,
    this.autoRecord = true,
    this.autoStopRecord = false,
    this.openSingleRepeat = true,
    this.coverSubtitle = false,
    this.menuSort = const [],
  });

  factory PlayerConfig.fromJson(Map<String, dynamic> json) {
    return _$PlayerConfigFromJson(json);
  }

  Map<String, dynamic> toJson() => _$PlayerConfigToJson(this);
}
