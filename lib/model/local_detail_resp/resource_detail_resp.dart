import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/video_time_interval.dart';

import '../note_model.dart';

part 'resource_detail_resp.g.dart';

@JsonSerializable()
class ResourceDetailResp {
  String? resourceId;
  int? resourceType;
  String? subtitleUrl;
  String? playUrl;
  int? position;
  List<VideoTimeInterval>? skipList;
  List<NoteModel>? notes;
  List<VideoTimeInterval>? sentenceCollects;
  int? currentLsTimes;
  String? nativeLangSubtitleUrl;
  String? originSubtitleUrl;

  ResourceDetailResp({
    this.resourceId,
    this.position,
    this.subtitleUrl,
    this.skipList,
    this.sentenceCollects,
    this.notes,
    this.playUrl,
    this.currentLsTimes,
    this.resourceType,
    this.nativeLangSubtitleUrl,
    this.originSubtitleUrl,
  });

  factory ResourceDetailResp.fromJson(Map<String, dynamic> json) {
    return _$ResourceDetailRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ResourceDetailRespToJson(this);
}
