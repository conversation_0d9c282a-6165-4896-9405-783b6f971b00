// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resource_detail_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResourceDetailResp _$ResourceDetailRespFromJson(Map<String, dynamic> json) =>
    ResourceDetailResp(
      resourceId: json['resourceId'] as String?,
      position: (json['position'] as num?)?.toInt(),
      subtitleUrl: json['subtitleUrl'] as String?,
      skipList: (json['skipList'] as List<dynamic>?)
          ?.map((e) => VideoTimeInterval.fromJson(e as Map<String, dynamic>))
          .toList(),
      sentenceCollects: (json['sentenceCollects'] as List<dynamic>?)
          ?.map((e) => VideoTimeInterval.fromJson(e as Map<String, dynamic>))
          .toList(),
      notes: (json['notes'] as List<dynamic>?)
          ?.map((e) => NoteModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      playUrl: json['playUrl'] as String?,
      currentLsTimes: (json['currentLsTimes'] as num?)?.toInt(),
      resourceType: (json['resourceType'] as num?)?.toInt(),
      nativeLangSubtitleUrl: json['nativeLangSubtitleUrl'] as String?,
      originSubtitleUrl: json['originSubtitleUrl'] as String?,
    );

Map<String, dynamic> _$ResourceDetailRespToJson(ResourceDetailResp instance) =>
    <String, dynamic>{
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'subtitleUrl': instance.subtitleUrl,
      'playUrl': instance.playUrl,
      'position': instance.position,
      'skipList': instance.skipList,
      'notes': instance.notes,
      'sentenceCollects': instance.sentenceCollects,
      'currentLsTimes': instance.currentLsTimes,
      'nativeLangSubtitleUrl': instance.nativeLangSubtitleUrl,
      'originSubtitleUrl': instance.originSubtitleUrl,
    };
