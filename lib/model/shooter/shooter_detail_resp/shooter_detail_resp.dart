import 'package:json_annotation/json_annotation.dart';

import 'shooter_detail_sub.dart';

part 'shooter_detail_resp.g.dart';

@JsonSerializable()
class ShooterDetailResp {
  ShooterDetailSub? sub;
  int? status;

  ShooterDetailResp({this.sub, this.status});

  factory ShooterDetailResp.fromJson(Map<String, dynamic> json) {
    return _$ShooterDetailRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ShooterDetailRespToJson(this);
}
