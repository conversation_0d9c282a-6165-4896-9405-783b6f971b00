import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/shooter/shooter_list_resp/lang.dart';

import 'filelist.dart';

part 'shooter_detail_subs.g.dart';

@JsonSerializable()
class ShooterDetailSubs {
  @J<PERSON><PERSON><PERSON>(name: 'native_name')
  String? nativeName;
  String? subtype;
  Lang? lang;
  List<Filelist>? filelist;
  String? filename;
  String? url;
  @Json<PERSON>ey(name: 'down_count')
  int? downCount;
  @Json<PERSON>ey(name: 'view_count')
  int? viewCount;
  int? size;
  int? id;
  String? title;
  String? videoname;

  ShooterDetailSubs({
    this.nativeName,
    this.subtype,
    this.lang,
    this.filelist,
    this.filename,
    this.url,
    this.downCount,
    this.viewCount,
    this.size,
    this.id,
    this.title,
    this.videoname,
  });

  factory ShooterDetailSubs.fromJson(Map<String, dynamic> json) =>
      _$ShooterDetailSubsFromJson(json);

  Map<String, dynamic> toJson() => _$ShooterDetailSubsToJson(this);
}
