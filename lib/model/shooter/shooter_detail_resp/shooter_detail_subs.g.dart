// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shooter_detail_subs.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShooterDetailSubs _$ShooterDetailSubsFromJson(Map<String, dynamic> json) =>
    ShooterDetailSubs(
      nativeName: json['native_name'] as String?,
      subtype: json['subtype'] as String?,
      lang: json['lang'] == null
          ? null
          : Lang.fromJson(json['lang'] as Map<String, dynamic>),
      filelist: (json['filelist'] as List<dynamic>?)
          ?.map((e) => Filelist.fromJson(e as Map<String, dynamic>))
          .toList(),
      filename: json['filename'] as String?,
      url: json['url'] as String?,
      downCount: (json['down_count'] as num?)?.toInt(),
      viewCount: (json['view_count'] as num?)?.toInt(),
      size: (json['size'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      videoname: json['videoname'] as String?,
    );

Map<String, dynamic> _$ShooterDetailSubsToJson(ShooterDetailSubs instance) =>
    <String, dynamic>{
      'native_name': instance.nativeName,
      'subtype': instance.subtype,
      'lang': instance.lang,
      'filelist': instance.filelist,
      'filename': instance.filename,
      'url': instance.url,
      'down_count': instance.downCount,
      'view_count': instance.viewCount,
      'size': instance.size,
      'id': instance.id,
      'title': instance.title,
      'videoname': instance.videoname,
    };
