import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/shooter/shooter_detail_resp/shooter_detail_subs.dart';

part 'shooter_detail_sub.g.dart';

@JsonSerializable()
class ShooterDetailSub {
  List<ShooterDetailSubs>? subs;
  String? action;
  String? keyword;
  String? result;

  ShooterDetailSub({this.subs, this.action, this.keyword, this.result});

  factory ShooterDetailSub.fromJson(Map<String, dynamic> json) =>
      _$ShooterDetailSubFromJson(json);

  Map<String, dynamic> toJson() => _$ShooterDetailSubToJson(this);
}
