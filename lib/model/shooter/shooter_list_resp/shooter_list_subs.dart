import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/shooter/shooter_list_resp/lang.dart';

part 'shooter_list_subs.g.dart';

@JsonSerializable()
class ShooterListSubs {
  int? id;
  @Json<PERSON>ey(name: 'native_name')
  String? nativeName;
  @JsonKey(name: 'vote_score')
  int? voteScore;
  Lang? lang;
  String? subtype;

  ShooterListSubs({this.id, this.nativeName, this.lang, this.subtype});

  factory ShooterListSubs.fromJson(Map<String, dynamic> json) =>
      _$ShooterListSubsFromJson(json);

  Map<String, dynamic> toJson() => _$ShooterListSubsToJson(this);
}
