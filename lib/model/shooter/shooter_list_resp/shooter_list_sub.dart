import 'package:json_annotation/json_annotation.dart';

part 'shooter_list_sub.g.dart';

@JsonSerializable()
class ShooterListSub {
  dynamic subs;
  String? action;
  String? keyword;
  String? result;

  ShooterListSub({this.subs, this.action, this.keyword, this.result});

  factory ShooterListSub.fromJson(Map<String, dynamic> json) =>
      _$ShooterListSubFromJson(json);

  Map<String, dynamic> toJson() => _$ShooterListSubToJson(this);
}
