import 'package:json_annotation/json_annotation.dart';

import 'shooter_list_sub.dart';

part 'shooter_list_resp.g.dart';

@JsonSerializable()
class ShooterListResp {
  ShooterListSub? sub;
  int? status;

  ShooterListResp({this.sub, this.status});

  factory ShooterListResp.fromJson(Map<String, dynamic> json) {
    return _$ShooterListRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ShooterListRespToJson(this);
}
