import 'package:json_annotation/json_annotation.dart';

part 'speech_evaluation_result.g.dart';

// 自定义转换器，用于动态解析result字段
class ResultConverter implements JsonConverter<dynamic, dynamic> {
  const ResultConverter();

  @override
  dynamic fromJson(dynamic json) {
    // 这里无法访问params字段，所以保持原始值
    return json;
  }

  @override
  dynamic toJson(dynamic object) {
    return object;
  }
}

// 新增结构体的JsonSerializable生成声明
// ignore_for_file: non_constant_identifier_names

/// 语音评测通用响应体，result字段根据coreType类型动态解析
@JsonSerializable(explicitToJson: true)
class SpeechEvaluationResult {
  /// 终端用户请求id
  final String? tokenId;
  /// 评测文本
  final String? refText;
  /// 在线音频地址，需设置attachAudioUrl
  final String? audioUrl;
  /// 评分结果返回时间
  final String? dtLastResponse;
  /// 评分结果（类型为ParaEvalResult、SentEvalResult、WordEvalResult，需根据coreType判断）
  @ResultConverter()
  final dynamic result;
  /// appKey
  final String? applicationId;
  /// 评分唯一id，建议业务层保存，方便排查错误
  final String? recordId;
  /// 请求参数，需设置getParam
  final Map<String, dynamic>? params;
  /// 错误码，出现该字段时无result字段
  final int? errId;
  /// 错误码信息，出现该字段时无result字段
  final String? error;
  /// 0：中间结果；1：最终结果
  final int? eof;
  /// 中间结果时间戳（仅中间结果有此字段）
  final String? timestamp;

  SpeechEvaluationResult({
    this.tokenId,
    this.refText,
    this.audioUrl,
    this.dtLastResponse,
    this.result,
    this.applicationId,
    this.recordId,
    this.params,
    this.errId,
    this.error,
    this.eof,
    this.timestamp,
  });

  factory SpeechEvaluationResult.fromJson(Map<String, dynamic> json) {
    final instance = _$SpeechEvaluationResultFromJson(json);
    final coreType = json['params']?['request']?['coreType'] ?? '';
    dynamic resultObj;
    if (json['result'] != null) {
      if (coreType == 'sent.eval' || coreType == 'sent.eval.pro') {
        resultObj = SentEvalResult.fromJson(json['result']);
      } else if (coreType == 'para.eval') {
        resultObj = ParaEvalResult.fromJson(json['result']);
      } else if (coreType == 'word.eval' || coreType == 'word.eval.pro') {
        resultObj = WordEvalResult.fromJson(json['result']);
      } else {
        resultObj = json['result'];
      }
    }
    return SpeechEvaluationResult(
      tokenId: instance.tokenId,
      refText: instance.refText,
      audioUrl: instance.audioUrl,
      dtLastResponse: instance.dtLastResponse,
      result: resultObj,
      applicationId: instance.applicationId,
      recordId: instance.recordId,
      params: instance.params,
      errId: instance.errId,
      error: instance.error,
      eof: instance.eof,
      timestamp: instance.timestamp,
    );
  }
  Map<String, dynamic> toJson() => _$SpeechEvaluationResultToJson(this);
}

/// para.eval 段落评测结果
@JsonSerializable(explicitToJson: true)
class ParaEvalResult {
  /// 发音得分
  final dynamic pronunciation;
  /// 语速（词/分）
  final int? speed;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 完整度
  final dynamic integrity;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 音频检测的提示
  final List<EvaluationWarning>? warning;
  /// 段落详情（句子数组）
  final List<ParaEvalSentence>? sentences;
  /// 韵律度得分
  final dynamic rhythm;
  /// 总分
  final dynamic overall;
  /// 流利度
  final dynamic fluency;
  /// 音频时长（字符串，单位：秒）
  final String? duration;
  /// 音频时长（数值，单位：秒）
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;

  ParaEvalResult({
    this.pronunciation,
    this.speed,
    this.kernelVersion,
    this.integrity,
    this.resourceVersion,
    this.warning,
    this.sentences,
    this.rhythm,
    this.overall,
    this.fluency,
    this.duration,
    this.numericDuration,
  });

  factory ParaEvalResult.fromJson(Map<String, dynamic> json) => _$ParaEvalResultFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalResultToJson(this);
}

/// 段落评测-句子详情
@JsonSerializable(explicitToJson: true)
class ParaEvalSentence {
  /// 句子内容
  final String? sentence;
  /// 句子总分
  final dynamic overall;
  /// 句子开始在音轨上的时间（单位：10毫秒）
  final int? start;
  /// 句子结束在音轨上的时间（单位：10毫秒）
  final int? end;
  /// 句子在文本中开始的位置
  final int? beginIndex;
  /// 句子在文本中结束的位置
  final int? endIndex;
  /// 句子中单词的详细信息（需开启paragraph_need_word_score）
  final List<ParaEvalWordDetail>? details;

  ParaEvalSentence({
    this.sentence,
    this.overall,
    this.start,
    this.end,
    this.beginIndex,
    this.endIndex,
    this.details,
  });

  factory ParaEvalSentence.fromJson(Map<String, dynamic> json) => _$ParaEvalSentenceFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalSentenceToJson(this);
}

/// 段落评测-单词详情
@JsonSerializable(explicitToJson: true)
class ParaEvalWordDetail {
  /// 单词
  final String? word;
  /// 单词分数
  final dynamic overall;
  /// 单词重读，0=非重读，1=重读
  final int? prominence;
  /// 0=非标点，1=标点
  final int? charType;
  /// 0=正常，3=漏读，4=重复读
  final int? readType;
  /// 是否已读，0=已读，1=未读
  final int? skipped;
  /// 单词在音轨上的开始时间（单位：10毫秒）
  final int? start;
  /// 单词在音轨上的结束时间（单位：10毫秒）
  final int? end;
  /// 单词文本信息
  @JsonKey(name: 'word_parts')
  final List<EvaluationWordPart>? wordParts;
  /// 单词得分情况（仅readType=4时有）
  final Map<String, dynamic>? scores;

  ParaEvalWordDetail({
    this.word,
    this.overall,
    this.prominence,
    this.charType,
    this.readType,
    this.skipped,
    this.start,
    this.end,
    this.wordParts,
    this.scores,
  });

  factory ParaEvalWordDetail.fromJson(Map<String, dynamic> json) => _$ParaEvalWordDetailFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalWordDetailToJson(this);
}



/// sent.eval/sent.eval.pro 句子评测结果
@JsonSerializable(explicitToJson: true)
class SentEvalResult {
  /// 评测时长（字符串）
  final String? duration;
  /// 流利度
  final dynamic fluency;
  /// 完整度
  final dynamic integrity;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 数值型时长
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;
  /// 总分
  final dynamic overall;
  /// 停顿次数
  @JsonKey(name: 'pause_count')
  final int? pauseCount;
  /// 发音得分
  final dynamic pronunciation;
  /// 句尾语调
  @JsonKey(name: 'rear_tone')
  final String? rearTone;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 节奏/韵律度
  final dynamic rhythm;
  /// 语速
  final int? speed;
  /// 警告信息
  final List<EvaluationWarning>? warning;
  /// 单词详情
  final List<SentEvalWord>? words;
  /// 连读检测（支持连读且实际发连读音，则出现该维度）
  final List<SentEvalLiaison>? liaison;
  /// 不完全爆破检测（支持爆破且实际发爆破音，则出现该维度，且仅支持两个词之间的爆破）
  final List<SentEvalPlosion>? plosion;

  SentEvalResult({
    this.duration,
    this.fluency,
    this.integrity,
    this.kernelVersion,
    this.numericDuration,
    this.overall,
    this.pauseCount,
    this.pronunciation,
    this.rearTone,
    this.resourceVersion,
    this.rhythm,
    this.speed,
    this.warning,
    this.words,
    this.liaison,
    this.plosion,
  });

  factory SentEvalResult.fromJson(Map<String, dynamic> json) => _$SentEvalResultFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalResultToJson(this);
}



@JsonSerializable(explicitToJson: true)
class SentEvalWord {
  final int? charType;
  final int? linkable;
  final int? linkable_type;
  final int? linked;
  final EvaluationPause? pause;
  final List<EvaluationPhoneme>? phonemes;
  final List<EvaluationPhonic>? phonics;
  final SentEvalWordScores? scores;
  final EvaluationSpan? span;
  final String? word;
  final List<EvaluationWordPart>? word_parts;

  SentEvalWord({
    this.charType,
    this.linkable,
    this.linkable_type,
    this.linked,
    this.pause,
    this.phonemes,
    this.phonics,
    this.scores,
    this.span,
    this.word,
    this.word_parts,
  });
  factory SentEvalWord.fromJson(Map<String, dynamic> json) => _$SentEvalWordFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordToJson(this);
}

@JsonSerializable()
class SentEvalWordScores {
  final int? overall;
  final int? prominence;
  final int? pronunciation;
  SentEvalWordScores({this.overall, this.prominence, this.pronunciation});
  factory SentEvalWordScores.fromJson(Map<String, dynamic> json) => _$SentEvalWordScoresFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordScoresToJson(this);
}

/// word.eval/word.eval.pro 单词评测结果
@JsonSerializable(explicitToJson: true)
class WordEvalResult {
  /// 总分
  final dynamic overall;
  /// 发音得分
  final dynamic pronunciation;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 单词详情（words）
  final List<WordEvalWord>? words;
  /// 音频时长（字符串，单位：秒）
  final String? duration;
  /// 数值型时长
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;
  /// 停顿次数
  @JsonKey(name: 'pause_count')
  final int? pauseCount;
  /// 重音分数
  final dynamic stress;
  /// 警告信息
  final List<EvaluationWarning>? warning;
  /// 韵律度得分
  final dynamic rhythm;
  /// 流利度
  final dynamic fluency;
  /// 完整度
  final dynamic integrity;
  /// 语速
  final int? speed;

  WordEvalResult({
    this.overall,
    this.pronunciation,
    this.kernelVersion,
    this.resourceVersion,
    this.words,
    this.duration,
    this.numericDuration,
    this.pauseCount,
    this.stress,
    this.warning,
    this.rhythm,
    this.fluency,
    this.integrity,
    this.speed,
  });

  factory WordEvalResult.fromJson(Map<String, dynamic> json) {
    final instance = _$WordEvalResultFromJson(json);
    List<WordEvalWord>? words;
    if (json['words'] != null) {
      words = (json['words'] as List)
          .map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList();
    } else if (json['details'] != null) {
      words = (json['details'] as List)
          .map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    List<EvaluationWarning>? warning;
    if (json['warning'] != null) {
      warning = (json['warning'] as List)
          .map((e) => EvaluationWarning.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    return WordEvalResult(
      overall: instance.overall,
      pronunciation: instance.pronunciation,
      kernelVersion: instance.kernelVersion,
      resourceVersion: instance.resourceVersion,
      words: words,
      duration: instance.duration,
      numericDuration: instance.numericDuration,
      pauseCount: instance.pauseCount,
      stress: instance.stress,
      warning: warning,
      rhythm: instance.rhythm,
      fluency: instance.fluency,
      integrity: instance.integrity,
      speed: instance.speed,
    );
  }
  Map<String, dynamic> toJson() => _$WordEvalResultToJson(this);
}

@JsonSerializable(explicitToJson: true)
class WordEvalWord {
  final String? word;
  final int? charType;
  final int? readType;
  final EvaluationPause? pause;
  final List<EvaluationPhoneme>? phonemes;
  final List<EvaluationPhonic>? phonics;
  final Map<String, dynamic>? scores;
  final EvaluationSpan? span;
  @JsonKey(name: 'word_parts')
  final List<EvaluationWordPart>? wordParts;

  WordEvalWord({
    this.word,
    this.charType,
    this.readType,
    this.pause,
    this.phonemes,
    this.phonics,
    this.scores,
    this.span,
    this.wordParts,
  });

  factory WordEvalWord.fromJson(Map<String, dynamic> json) => _$WordEvalWordFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalWordToJson(this);
}





/// 通用的评测警告信息类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationWarning {
  /// 错误码
  final int? code;
  /// 音频检测的提示信息
  final String? message;

  EvaluationWarning({this.code, this.message});

  factory EvaluationWarning.fromJson(Map<String, dynamic> json) => _$EvaluationWarningFromJson(json);
  Map<String, dynamic> toJson() => _$EvaluationWarningToJson(this);
}

/// 通用的音素类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationPhoneme {
  /// 音素
  final String? phoneme;
  /// 发音得分
  final int? pronunciation;
  /// 时间跨度
  final EvaluationSpan? span;
  /// 重音标记
  final int? stress_mark;

  EvaluationPhoneme({this.phoneme, this.pronunciation, this.span, this.stress_mark});

  factory EvaluationPhoneme.fromJson(Map<String, dynamic> json) => _$EvaluationPhonemeFromJson(json);
  Map<String, dynamic> toJson() => _$EvaluationPhonemeToJson(this);
}

/// 通用的音标类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationPhonic {
  /// 总分
  final int? overall;
  /// 音素列表
  final List<String>? phoneme;
  /// 拼写
  final String? spell;

  EvaluationPhonic({this.overall, this.phoneme, this.spell});

  factory EvaluationPhonic.fromJson(Map<String, dynamic> json) => _$EvaluationPhonicFromJson(json);
  Map<String, dynamic> toJson() => _$EvaluationPhonicToJson(this);
}

/// 通用的时间跨度类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationSpan {
  /// 开始时间（单位：10毫秒）
  final int? start;
  /// 结束时间（单位：10毫秒）
  final int? end;

  EvaluationSpan({this.start, this.end});

  factory EvaluationSpan.fromJson(Map<String, dynamic> json) => _$EvaluationSpanFromJson(json);
  Map<String, dynamic> toJson() => _$EvaluationSpanToJson(this);
}

/// 通用的单词文本信息类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationWordPart {
  /// 文本内容
  final String? part;
  /// 在文本中开始的位置
  final int? beginIndex;
  /// 在文本中结束的位置
  final int? endIndex;
  /// 0=非标点，1=标点
  final int? charType;

  EvaluationWordPart({this.part, this.beginIndex, this.endIndex, this.charType});

  factory EvaluationWordPart.fromJson(Map<String, dynamic> json) => _$EvaluationWordPartFromJson(json);
  Map<String, dynamic> toJson() => _$EvaluationWordPartToJson(this);
}

/// 通用的停顿信息类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationPause {
  /// 停顿时长（单位：10毫秒）
  final int? duration;
  /// 停顿类型，duration>10该值为1，否则值为0
  final int? type;

  EvaluationPause({this.duration, this.type});

  factory EvaluationPause.fromJson(Map<String, dynamic> json) => _$EvaluationPauseFromJson(json);
  Map<String, dynamic> toJson() => _$EvaluationPauseToJson(this);
}

/// 通用的单词位置信息类（可复用于连读和爆破检测）
@JsonSerializable()
class EvaluationWordInfo {
  /// 单词位置信息
  final int? index;
  /// 单词文本
  final String? word;

  EvaluationWordInfo({this.index, this.word});

  factory EvaluationWordInfo.fromJson(Map<String, dynamic> json) => _$EvaluationWordInfoFromJson(json);
  Map<String, dynamic> toJson() => _$EvaluationWordInfoToJson(this);
}

/// 连读检测信息
@JsonSerializable(explicitToJson: true)
class SentEvalLiaison {
  /// 连读的第一个单词信息
  final EvaluationWordInfo? first;
  /// 连读的第二个单词信息
  final EvaluationWordInfo? second;
  /// 第一个单词的最后一个音素
  @JsonKey(name: 'first_phoneme')
  final String? firstPhoneme;
  /// 第二个单词的第一个音素
  @JsonKey(name: 'second_phoneme')
  final String? secondPhoneme;
  /// 连读类型
  @JsonKey(name: 'linkable_type')
  final int? linkableType;

  SentEvalLiaison({
    this.first,
    this.second,
    this.firstPhoneme,
    this.secondPhoneme,
    this.linkableType,
  });

  factory SentEvalLiaison.fromJson(Map<String, dynamic> json) => _$SentEvalLiaisonFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalLiaisonToJson(this);
}

/// 不完全爆破检测信息
@JsonSerializable(explicitToJson: true)
class SentEvalPlosion {
  /// 爆破音的第一个单词信息
  final EvaluationWordInfo? first;
  /// 爆破音的第二个单词信息
  final EvaluationWordInfo? second;
  /// 第一个单词的最后一个音素
  @JsonKey(name: 'first_phoneme')
  final String? firstPhoneme;
  /// 第二个单词的第一个音素
  @JsonKey(name: 'second_phoneme')
  final String? secondPhoneme;
  /// 连读类型
  @JsonKey(name: 'linkable_type')
  final int? linkableType;

  SentEvalPlosion({
    this.first,
    this.second,
    this.firstPhoneme,
    this.secondPhoneme,
    this.linkableType,
  });

  factory SentEvalPlosion.fromJson(Map<String, dynamic> json) => _$SentEvalPlosionFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalPlosionToJson(this);
}