// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_time_interval.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VideoTimeInterval _$VideoTimeIntervalFromJson(Map<String, dynamic> json) =>
    VideoTimeInterval(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
    );

Map<String, dynamic> _$VideoTimeIntervalToJson(VideoTimeInterval instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };
