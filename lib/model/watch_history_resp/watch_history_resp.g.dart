// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'watch_history_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WatchHistoryResp _$WatchHistoryRespFromJson(Map<String, dynamic> json) =>
    WatchHistoryResp(
      id: json['id'] as String?,
      uid: json['uid'] as String?,
      lastTime: json['lastTime'] as String?,
      position: (json['position'] as num?)?.toInt(),
      resourceId: json['resourceId'] as String?,
      resourceType: (json['resourceType'] as num?)?.toInt(),
      resource: json['resource'] == null
          ? null
          : Resource.fromJson(json['resource'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WatchHistoryRespToJson(WatchHistoryResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'lastTime': instance.lastTime,
      'position': instance.position,
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'resource': instance.resource,
    };
