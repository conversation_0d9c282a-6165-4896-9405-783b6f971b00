import 'package:json_annotation/json_annotation.dart';

import '../resource.dart';

part 'watch_history_resp.g.dart';

@JsonSerializable()
class WatchHistoryResp {
  String? id;
  String? uid;
  String? lastTime;
  int? position;
  String? resourceId;
  int? resourceType;
  Resource? resource;

  WatchHistoryResp({
    this.id,
    this.uid,
    this.lastTime,
    this.position,
    this.resourceId,
    this.resourceType,
    this.resource,
  });

  factory WatchHistoryResp.fromJson(Map<String, dynamic> json) {
    return _$WatchHistoryRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$WatchHistoryRespToJson(this);
}
