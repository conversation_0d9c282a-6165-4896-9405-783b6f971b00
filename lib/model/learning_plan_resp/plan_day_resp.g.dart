// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_day_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanDayResp _$PlanDayRespFromJson(Map<String, dynamic> json) => PlanDayResp(
      id: json['id'] as String,
      weekDayNumber: (json['weekDayNumber'] as num?)?.toInt(),
      planDayNumber: (json['planDayNumber'] as num?)?.toInt(),
      studyTimestamp: (json['studyTimestamp'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      progress: (json['progress'] as num?)?.toInt(),
      currentSentences: (json['currentSentences'] as num?)?.toInt(),
      targetSentences: (json['targetSentences'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
      subtitleStartTimestamp: (json['subtitleStartTimestamp'] as num?)?.toInt(),
      subtitleEndTimestamp: (json['subtitleEndTimestamp'] as num?)?.toInt(),
      recordedRanges: (json['recordedRanges'] as List<dynamic>?)
          ?.map((e) => VideoTimeInterval.fromJson(e as Map<String, dynamic>))
          .toList(),
      resources: (json['resources'] as List<dynamic>?)
          ?.map((e) => PlanResourceResp.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PlanDayRespToJson(PlanDayResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'weekDayNumber': instance.weekDayNumber,
      'planDayNumber': instance.planDayNumber,
      'studyTimestamp': instance.studyTimestamp,
      'status': instance.status,
      'progress': instance.progress,
      'currentSentences': instance.currentSentences,
      'targetSentences': instance.targetSentences,
      'type': instance.type,
      'subtitleStartTimestamp': instance.subtitleStartTimestamp,
      'subtitleEndTimestamp': instance.subtitleEndTimestamp,
      'recordedRanges': instance.recordedRanges,
      'resources': instance.resources,
    };
