// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_plan_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LearningPlanResp _$LearningPlanRespFromJson(Map<String, dynamic> json) =>
    LearningPlanResp(
      id: json['id'] as String,
      startLevel: json['startLevel'] as String,
      targetLevel: json['targetLevel'] as String,
      startTimestamp: (json['startTimestamp'] as num?)?.toInt(),
      endTimestamp: (json['endTimestamp'] as num?)?.toInt(),
      status: (json['status'] as num).toInt(),
      totalLearnDays: (json['totalLearnDays'] as num?)?.toInt(),
      studyDaysOfWeek: (json['studyDaysOfWeek'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      dailySentences: (json['dailySentences'] as num?)?.toInt(),
      stages: (json['stages'] as List<dynamic>?)
          ?.map((e) => PlanStageResp.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentDayTimestamp: (json['currentDayTimestamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$LearningPlanRespToJson(LearningPlanResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startLevel': instance.startLevel,
      'targetLevel': instance.targetLevel,
      'startTimestamp': instance.startTimestamp,
      'endTimestamp': instance.endTimestamp,
      'status': instance.status,
      'totalLearnDays': instance.totalLearnDays,
      'studyDaysOfWeek': instance.studyDaysOfWeek,
      'dailySentences': instance.dailySentences,
      'stages': instance.stages,
      'currentDayTimestamp': instance.currentDayTimestamp,
    };
