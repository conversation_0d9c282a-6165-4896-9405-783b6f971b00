import 'package:json_annotation/json_annotation.dart';
import 'plan_stage_resp.dart';

part 'learning_plan_resp.g.dart';

@JsonSerializable()
class LearningPlanResp {
  final String id;
  final String startLevel;
  final String targetLevel;
  final int? startTimestamp;
  final int? endTimestamp;
  final int status;
  final int? totalLearnDays;
  final List<int> studyDaysOfWeek;
  final int? dailySentences;
  final List<PlanStageResp>? stages;
  final int? currentDayTimestamp;

  LearningPlanResp({
    required this.id,
    required this.startLevel,
    required this.targetLevel,
    required this.startTimestamp,
    this.endTimestamp,
    required this.status,
    this.totalLearnDays,
    required this.studyDaysOfWeek,
    required this.dailySentences,
    required this.stages,
    this.currentDayTimestamp,
  });

  factory LearningPlanResp.fromJson(Map<String, dynamic> json) => _$LearningPlanRespFromJson(json);

  Map<String, dynamic> toJson() => _$LearningPlanRespToJson(this);
}
