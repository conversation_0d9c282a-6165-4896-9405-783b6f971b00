import 'package:json_annotation/json_annotation.dart';

part 'plan_resource_resp.g.dart';

@JsonSerializable()
class PlanResourceResp {
  final String resourceId;
  final String resourceName;
  final String? resourceCover;
  final String? resourceUrl;
  final int? lsCount;

  PlanResourceResp({
    required this.resourceId,
    required this.resourceName,
    this.resourceCover,
    this.resourceUrl,
    this.lsCount,
  });

  factory PlanResourceResp.fromJson(Map<String, dynamic> json) => _$PlanResourceRespFromJson(json);

  Map<String, dynamic> toJson() => _$PlanResourceRespToJson(this);
}
