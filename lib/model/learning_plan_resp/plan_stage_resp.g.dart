// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_stage_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanStageResp _$PlanStageRespFromJson(Map<String, dynamic> json) =>
    PlanStageResp(
      id: json['id'] as String,
      stageDesc: json['stageDesc'] as String,
      objective: json['objective'] as String,
      weeks: (json['weeks'] as List<dynamic>)
          .map((e) => PlanWeekResp.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PlanStageRespToJson(PlanStageResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'stageDesc': instance.stageDesc,
      'objective': instance.objective,
      'weeks': instance.weeks,
    };
