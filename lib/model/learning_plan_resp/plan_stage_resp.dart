import 'package:json_annotation/json_annotation.dart';
import 'plan_week_resp.dart';

part 'plan_stage_resp.g.dart';

@JsonSerializable()
class PlanStageResp {
  final String id;
  final String stageDesc;
  final String objective;
  final List<PlanWeekResp> weeks;

  PlanStageResp({
    required this.id,
    required this.stageDesc,
    required this.objective,
    required this.weeks,
  });

  factory PlanStageResp.fromJson(Map<String, dynamic> json) => _$PlanStageRespFromJson(json);

  Map<String, dynamic> toJson() => _$PlanStageRespToJson(this);
}
