// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_resource_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanResourceResp _$PlanResourceRespFromJson(Map<String, dynamic> json) =>
    PlanResourceResp(
      resourceId: json['resourceId'] as String,
      resourceName: json['resourceName'] as String,
      resourceCover: json['resourceCover'] as String?,
      resourceUrl: json['resourceUrl'] as String?,
      lsCount: (json['lsCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PlanResourceRespToJson(PlanResourceResp instance) =>
    <String, dynamic>{
      'resourceId': instance.resourceId,
      'resourceName': instance.resourceName,
      'resourceCover': instance.resourceCover,
      'resourceUrl': instance.resourceUrl,
      'lsCount': instance.lsCount,
    };
