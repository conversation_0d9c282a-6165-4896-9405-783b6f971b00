import 'package:json_annotation/json_annotation.dart';
import 'plan_day_resp.dart';

part 'plan_week_resp.g.dart';

@JsonSerializable()
class PlanWeekResp {
  final int weekNumber;
  final List<PlanDayResp> days;

  PlanWeekResp({
    required this.weekNumber,
    required this.days,
  });

  factory PlanWeekResp.fromJson(Map<String, dynamic> json) => _$PlanWeekRespFromJson(json);

  Map<String, dynamic> toJson() => _$PlanWeekRespToJson(this);
}
