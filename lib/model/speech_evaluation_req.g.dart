// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'speech_evaluation_req.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SpeechEvaluationReq _$SpeechEvaluationReqFromJson(Map<String, dynamic> json) =>
    SpeechEvaluationReq(
      uid: json['uid'] as String,
      resourceId: json['resourceId'] as String,
      resourceType: (json['resourceType'] as num).toInt(),
      content: json['content'] as String,
      audioUrl: json['audioUrl'] as String,
      startTime: (json['startTime'] as num).toInt(),
      endTime: (json['endTime'] as num).toInt(),
    );

Map<String, dynamic> _$SpeechEvaluationReqToJson(
        SpeechEvaluationReq instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'content': instance.content,
      'audioUrl': instance.audioUrl,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
    };

SpeechEvaluationListReq _$SpeechEvaluationListReqFromJson(
        Map<String, dynamic> json) =>
    SpeechEvaluationListReq(
      resourceId: json['resourceId'] as String,
      resourceType: (json['resourceType'] as num).toInt(),
    );

Map<String, dynamic> _$SpeechEvaluationListReqToJson(
        SpeechEvaluationListReq instance) =>
    <String, dynamic>{
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
    };

SpeechEvaluationUpdateReq _$SpeechEvaluationUpdateReqFromJson(
        Map<String, dynamic> json) =>
    SpeechEvaluationUpdateReq(
      id: (json['id'] as num).toInt(),
      uid: json['uid'] as String,
      resourceId: json['resourceId'] as String,
      resourceType: (json['resourceType'] as num).toInt(),
      content: json['content'] as String,
      audioUrl: json['audioUrl'] as String,
      startTime: (json['startTime'] as num).toInt(),
      endTime: (json['endTime'] as num).toInt(),
    );

Map<String, dynamic> _$SpeechEvaluationUpdateReqToJson(
        SpeechEvaluationUpdateReq instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'content': instance.content,
      'audioUrl': instance.audioUrl,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
    };
