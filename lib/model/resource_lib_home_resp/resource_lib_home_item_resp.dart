import 'package:json_annotation/json_annotation.dart';

part 'resource_lib_home_item_resp.g.dart';

@JsonSerializable()
class ResourceLibHomeItemResp {
  String? id;
  int? contentType;
  String? name;
  String? cover;
  String? videoUrl;

  ResourceLibHomeItemResp({this.id, this.contentType, this.name, this.cover,this.videoUrl});

  factory ResourceLibHomeItemResp.fromJson(Map<String, dynamic> json) {
    return _$ResourceLibHomeItemRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ResourceLibHomeItemRespToJson(this);
}
