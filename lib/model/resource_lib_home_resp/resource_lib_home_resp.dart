import 'package:json_annotation/json_annotation.dart';

import 'category_type_list.dart';
import 'recommend.dart';

part 'resource_lib_home_resp.g.dart';

@JsonSerializable()
class ResourceLibHomeResp {
  List<Recommend>? recommend;
  List<CategoryTypeList>? categoryTypeList;

  ResourceLibHomeResp({this.recommend, this.categoryTypeList});

  factory ResourceLibHomeResp.fromJson(Map<String, dynamic> json) {
    return _$ResourceLibHomeRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ResourceLibHomeRespToJson(this);
}
