import 'package:json_annotation/json_annotation.dart';

part 'recommend.g.dart';

@JsonSerializable()
class Recommend {
  String? id;
  String? cover;
  int? contentType; //1代表剧集 2 代表视频
  String? videoUrl;

  Recommend({this.id, this.cover, this.contentType,this.videoUrl});

  factory Recommend.fromJson(Map<String, dynamic> json) {
    return _$RecommendFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RecommendToJson(this);
}
