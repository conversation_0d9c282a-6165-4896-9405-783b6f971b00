import 'package:json_annotation/json_annotation.dart';

import 'category.dart';

part 'category_type_list.g.dart';

@JsonSerializable()
class CategoryTypeList {
  String? name;
  List<Category>? categories;

  CategoryTypeList({this.name, this.categories});

  factory CategoryTypeList.fromJson(Map<String, dynamic> json) {
    return _$CategoryTypeListFromJson(json);
  }

  Map<String, dynamic> toJson() => _$CategoryTypeListToJson(this);
}
