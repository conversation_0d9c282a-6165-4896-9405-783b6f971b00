import 'package:json_annotation/json_annotation.dart';

import 'output.dart';
import 'usage.dart';

part 'ai_sentence_resp.g.dart';

@JsonSerializable()
class AiSentenceResp {
  Output? output;
  Usage? usage;
  @JsonKey(name: 'request_id')
  String? requestId;

  AiSentenceResp({this.output, this.usage, this.requestId});

  factory AiSentenceResp.fromJson(Map<String, dynamic> json) {
    return _$AiSentenceRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AiSentenceRespToJson(this);
}
