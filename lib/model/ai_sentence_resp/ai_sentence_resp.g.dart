// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_sentence_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AiSentenceResp _$AiSentenceRespFromJson(Map<String, dynamic> json) =>
    AiSentenceResp(
      output: json['output'] == null
          ? null
          : Output.fromJson(json['output'] as Map<String, dynamic>),
      usage: json['usage'] == null
          ? null
          : Usage.fromJson(json['usage'] as Map<String, dynamic>),
      requestId: json['request_id'] as String?,
    );

Map<String, dynamic> _$AiSentenceRespToJson(AiSentenceResp instance) =>
    <String, dynamic>{
      'output': instance.output,
      'usage': instance.usage,
      'request_id': instance.requestId,
    };
