import 'package:json_annotation/json_annotation.dart';

part 'output.g.dart';

@JsonSerializable()
class Output {
  @Json<PERSON>ey(name: 'finish_reason')
  String? finishReason;
  @JsonKey(name: 'session_id')
  String? sessionId;
  String? text;

  Output({this.finishReason, this.sessionId, this.text});

  factory Output.fromJson(Map<String, dynamic> json) {
    return _$OutputFromJson(json);
  }

  Map<String, dynamic> toJson() => _$OutputToJson(this);
}
