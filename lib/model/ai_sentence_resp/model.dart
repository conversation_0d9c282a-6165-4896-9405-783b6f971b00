import 'package:json_annotation/json_annotation.dart';

part 'model.g.dart';

@JsonSerializable()
class Model {
  @JsonKey(name: 'output_tokens')
  int? outputTokens;
  @Json<PERSON>ey(name: 'model_id')
  String? modelId;
  @Json<PERSON>ey(name: 'input_tokens')
  int? inputTokens;

  Model({this.outputTokens, this.modelId, this.inputTokens});

  factory Model.fromJson(Map<String, dynamic> json) => _$ModelFromJson(json);

  Map<String, dynamic> toJson() => _$ModelToJson(this);
}
