import 'package:json_annotation/json_annotation.dart';

part 'note_model.g.dart';

@JsonSerializable()
class NoteModel {
  String? id;
  String? content;
  int? videoStartTime;
  int? videoEndTime;

  NoteModel({
    this.id,
    this.content,
    this.videoStartTime,
    this.videoEndTime,
  });

  factory NoteModel.fromJson(Map<String, dynamic> json) {
    return _$NoteModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NoteModelToJson(this);
}
