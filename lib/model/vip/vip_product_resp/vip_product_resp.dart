import 'package:json_annotation/json_annotation.dart';

part 'vip_product_resp.g.dart';

@JsonSerializable()
class VipProductResp {
	int? id;
	String? name;
	int? type;
	int? isSubscription;
	double? price;
	double? originPrice;
	String? outProductCode;
	String? langCode;
  String? iosProductId;

	VipProductResp({
		this.id, 
		this.name, 
		this.type, 
		this.isSubscription, 
		this.price, 
		this.originPrice, 
		this.outProductCode, 
		this.langCode, 
    this.iosProductId,
	});

	factory VipProductResp.fromJson(Map<String, dynamic> json) {
		return _$VipProductRespFromJson(json);
	}

	Map<String, dynamic> toJson() => _$VipProductRespToJson(this);
}
