// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_product_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VipProductResp _$VipProductRespFromJson(Map<String, dynamic> json) =>
    VipProductResp(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      type: (json['type'] as num?)?.toInt(),
      isSubscription: (json['isSubscription'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      originPrice: (json['originPrice'] as num?)?.toDouble(),
      outProductCode: json['outProductCode'] as String?,
      langCode: json['langCode'] as String?,
      iosProductId: json['iosProductId'] as String?,
    );

Map<String, dynamic> _$VipProductRespToJson(VipProductResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'isSubscription': instance.isSubscription,
      'price': instance.price,
      'originPrice': instance.originPrice,
      'outProductCode': instance.outProductCode,
      'langCode': instance.langCode,
      'iosProductId': instance.iosProductId,
    };
