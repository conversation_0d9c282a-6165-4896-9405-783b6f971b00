// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_vip_info_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserVipInfoResp _$UserVipInfoRespFromJson(Map<String, dynamic> json) =>
    UserVipInfoResp(
      isVip: (json['isVip'] as num?)?.toInt(),
      isSubscription: (json['isSubscription'] as num?)?.toInt(),
      vipId: (json['vipID'] as num?)?.toInt(),
      vipLevel: (json['vipLevel'] as num?)?.toInt(),
      expireDate: json['expireDate'] as String?,
      expireTimestamp: (json['expireTimestamp'] as num?)?.toInt(),
      productId: (json['productID'] as num?)?.toInt(),
      productName: json['productName'] as String?,
      firstCycleAmount: (json['firstCycleAmount'] as num?)?.toInt(),
      nextCycleAmount: (json['nextCycleAmount'] as num?)?.toInt(),
      nextPaidDate: json['nextPaidDate'] as String?,
      nextPaidTimestamp: (json['nextPaidTimestamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$UserVipInfoRespToJson(UserVipInfoResp instance) =>
    <String, dynamic>{
      'isVip': instance.isVip,
      'isSubscription': instance.isSubscription,
      'vipID': instance.vipId,
      'vipLevel': instance.vipLevel,
      'expireDate': instance.expireDate,
      'expireTimestamp': instance.expireTimestamp,
      'productID': instance.productId,
      'productName': instance.productName,
      'firstCycleAmount': instance.firstCycleAmount,
      'nextCycleAmount': instance.nextCycleAmount,
      'nextPaidDate': instance.nextPaidDate,
      'nextPaidTimestamp': instance.nextPaidTimestamp,
    };
