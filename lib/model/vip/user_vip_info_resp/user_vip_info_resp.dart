import 'package:json_annotation/json_annotation.dart';

part 'user_vip_info_resp.g.dart';

@JsonSerializable()
class UserVipInfoResp {
	int? isVip;
	int? isSubscription;
	@<PERSON><PERSON><PERSON><PERSON>(name: 'vipID') 
	int? vipId;
	int? vipLevel;
	String? expireDate;
	int? expireTimestamp;
	@J<PERSON><PERSON><PERSON>(name: 'productID') 
	int? productId;
	String? productName;
	int? firstCycleAmount;
	int? nextCycleAmount;
	String? nextPaidDate;
	int? nextPaidTimestamp;

	UserVipInfoResp({
		this.isVip, 
		this.isSubscription, 
		this.vipId, 
		this.vipLevel, 
		this.expireDate, 
		this.expireTimestamp, 
		this.productId, 
		this.productName, 
		this.firstCycleAmount, 
		this.nextCycleAmount, 
		this.nextPaidDate, 
		this.nextPaidTimestamp, 
	});

	factory UserVipInfoResp.fromJson(Map<String, dynamic> json) {
		return _$UserVipInfoRespFromJson(json);
	}

	Map<String, dynamic> toJson() => _$UserVipInfoRespToJson(this);
}
