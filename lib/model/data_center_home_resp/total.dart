import 'package:json_annotation/json_annotation.dart';

part 'total.g.dart';

@JsonSerializable()
class Total {
  int? totalLearnDuration;
  int? totalLearnDayTimes;
  int? totalLearnVideoSize;

  Total({this.totalLearnDuration, this.totalLearnDayTimes,this.totalLearnVideoSize});

  factory Total.fromJson(Map<String, dynamic> json) => _$TotalFromJson(json);

  Map<String, dynamic> toJson() => _$TotalToJson(this);
}
