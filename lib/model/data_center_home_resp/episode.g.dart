// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'episode.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Episode _$EpisodeFromJson(Map<String, dynamic> json) => Episode(
      id: json['id'] as String?,
      uid: json['uid'] as String?,
      resourceId: json['resourceId'] as String?,
      resourceType: (json['resourceType'] as num?)?.toInt(),
      episodeName: json['episodeName'] as String?,
      status: (json['status'] as num?)?.toInt(),
      targetLsTimes: (json['targetLsTimes'] as num?)?.toInt(),
      targetDesc: json['targetDesc'] as String?,
      currentLsTimes: (json['currentLsTimes'] as num?)?.toInt(),
      totalLearnDuration: (json['totalLearnDuration'] as num?)?.toInt(),
      totalLearnDayTimes: (json['totalLearnDayTimes'] as num?)?.toInt(),
    );

Map<String, dynamic> _$EpisodeToJson(Episode instance) => <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'episodeName': instance.episodeName,
      'status': instance.status,
      'targetLsTimes': instance.targetLsTimes,
      'targetDesc': instance.targetDesc,
      'currentLsTimes': instance.currentLsTimes,
      'totalLearnDuration': instance.totalLearnDuration,
      'totalLearnDayTimes': instance.totalLearnDayTimes,
    };
