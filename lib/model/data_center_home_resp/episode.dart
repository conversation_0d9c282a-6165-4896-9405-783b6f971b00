import 'package:json_annotation/json_annotation.dart';

part 'episode.g.dart';

@JsonSerializable()
class Episode {
  String? id;
  String? uid;
  String? resourceId;
  int? resourceType;
  String? episodeName;
  int? status;
  int? targetLsTimes;
  String? targetDesc;
  int? currentLsTimes;
  int? totalLearnDuration;
  int? totalLearnDayTimes;

  Episode({
    this.id,
    this.uid,
    this.resourceId,
    this.resourceType,
    this.episodeName,
    this.status,
    this.targetLsTimes,
    this.targetDesc,
    this.currentLsTimes,
    this.totalLearnDuration,
    this.totalLearnDayTimes,
  });

  factory Episode.fromJson(Map<String, dynamic> json) {
    return _$EpisodeFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EpisodeToJson(this);
}
