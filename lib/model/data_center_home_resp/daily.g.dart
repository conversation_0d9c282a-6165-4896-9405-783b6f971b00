// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'daily.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Daily _$DailyFromJson(Map<String, dynamic> json) => Daily(
      resourceId: json['resourceId'] as String?,
      resourceType: (json['resourceType'] as num?)?.toInt(),
      episodeName: json['episodeName'] as String?,
      currentLsTimes: (json['currentLsTimes'] as num?)?.toInt(),
      targetLsTimes: (json['targetLsTimes'] as num?)?.toInt(),
      totalLearnDuration: (json['totalLearnDuration'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DailyToJson(Daily instance) => <String, dynamic>{
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'episodeName': instance.episodeName,
      'currentLsTimes': instance.currentLsTimes,
      'targetLsTimes': instance.targetLsTimes,
      'totalLearnDuration': instance.totalLearnDuration,
    };
