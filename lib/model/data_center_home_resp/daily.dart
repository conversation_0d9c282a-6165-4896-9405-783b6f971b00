import 'package:json_annotation/json_annotation.dart';

part 'daily.g.dart';

@JsonSerializable()
class Daily {
  String? resourceId;
  int? resourceType;
  String? episodeName;
  int? currentLsTimes;
  int? targetLsTimes;
  int? totalLearnDuration;

  Daily({
    this.resourceId,
    this.resourceType,
    this.episodeName,
    this.currentLsTimes,
    this.targetLsTimes,
    this.totalLearnDuration,
  });

  factory Daily.fromJson(Map<String, dynamic> json) => _$DailyFromJson(json);

  Map<String, dynamic> toJson() => _$DailyToJson(this);
}
