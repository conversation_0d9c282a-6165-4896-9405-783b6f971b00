import 'package:json_annotation/json_annotation.dart';

import 'episode.dart';
import 'total.dart';

part 'data_center_home_resp.g.dart';

@JsonSerializable()
class DataCenterHomeResp {
  Total? total;
  List<Episode>? episodes;

  DataCenterHomeResp({this.total, this.episodes});

  factory DataCenterHomeResp.fromJson(Map<String, dynamic> json) {
    return _$DataCenterHomeRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DataCenterHomeRespToJson(this);
}
