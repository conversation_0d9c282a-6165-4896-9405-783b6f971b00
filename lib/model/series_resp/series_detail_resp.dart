import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/resource_resp/resource_resp.dart';

part 'series_detail_resp.g.dart';

@JsonSerializable()
class SeriesDetailResp {
  String? id;
  String? title;
  String? statement;
  String? description;
  int? priority;
  String? cover;
  List<ResourceResp>? resources;

  SeriesDetailResp({
    this.id,
    this.title,
    this.statement,
    this.description,
    this.priority,
    this.cover,
    this.resources,
  });

  factory SeriesDetailResp.fromJson(Map<String, dynamic> json) {
    return _$SeriesDetailRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SeriesDetailRespToJson(this);
}
