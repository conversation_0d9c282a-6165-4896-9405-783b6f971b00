import 'package:json_annotation/json_annotation.dart';

part 'video_time_interval.g.dart';

/// 视频时间间隔模型
@JsonSerializable()
class VideoTimeInterval {
  final int? start; // 开始时间，单位为毫秒
  final int? end;   // 结束时间，单位为毫秒

  VideoTimeInterval({
    required this.start,
    required this.end,
  });

  factory VideoTimeInterval.fromJson(Map<String, dynamic> json) => _$VideoTimeIntervalFromJson(json);

  Map<String, dynamic> toJson() => _$VideoTimeIntervalToJson(this);
}
