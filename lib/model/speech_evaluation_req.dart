import 'package:json_annotation/json_annotation.dart';

part 'speech_evaluation_req.g.dart';

/// 语音评测创建请求
@JsonSerializable()
class SpeechEvaluationReq {
  final String uid;
  final String resourceId;
  final int resourceType;
  final String content;
  final String audioUrl;
  final int startTime;
  final int endTime;

  SpeechEvaluationReq({
    required this.uid,
    required this.resourceId,
    required this.resourceType,
    required this.content,
    required this.audioUrl,
    required this.startTime,
    required this.endTime,
  });

  factory SpeechEvaluationReq.fromJson(Map<String, dynamic> json) =>
      _$SpeechEvaluationReqFromJson(json);

  Map<String, dynamic> toJson() => _$SpeechEvaluationReqToJson(this);
}

/// 语音评测列表查询请求
@JsonSerializable()
class SpeechEvaluationListReq {
  final String resourceId;
  final int resourceType;

  SpeechEvaluationListReq({
    required this.resourceId,
    required this.resourceType,
  });

  factory SpeechEvaluationListReq.fromJson(Map<String, dynamic> json) =>
      _$SpeechEvaluationListReqFromJson(json);

  Map<String, dynamic> toJson() => _$SpeechEvaluationListReqToJson(this);
}

/// 语音评测更新请求
@JsonSerializable()
class SpeechEvaluationUpdateReq {
  final int id;
  final String uid;
  final String resourceId;
  final int resourceType;
  final String content;
  final String audioUrl;
  final int startTime;
  final int endTime;

  SpeechEvaluationUpdateReq({
    required this.id,
    required this.uid,
    required this.resourceId,
    required this.resourceType,
    required this.content,
    required this.audioUrl,
    required this.startTime,
    required this.endTime,
  });

  factory SpeechEvaluationUpdateReq.fromJson(Map<String, dynamic> json) =>
      _$SpeechEvaluationUpdateReqFromJson(json);

  Map<String, dynamic> toJson() => _$SpeechEvaluationUpdateReqToJson(this);
}
