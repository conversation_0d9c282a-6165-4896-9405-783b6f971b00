import 'package:json_annotation/json_annotation.dart';

part 'local_sentence_collect_resp.g.dart';

@JsonSerializable()
class LocalSentenceCollectResp {
  String? id;
  DateTime? createat;
  DateTime? updateat;
  String? uid;
  String? localSubtitleId;
  List<int>? indexs;

  LocalSentenceCollectResp({
    this.id,
    this.createat,
    this.updateat,
    this.uid,
    this.localSubtitleId,
    this.indexs,
  });

  factory LocalSentenceCollectResp.fromJson(Map<String, dynamic> json) {
    return _$LocalSentenceCollectRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$LocalSentenceCollectRespToJson(this);
}
