// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_sentence_collect_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LocalSentenceCollectResp _$LocalSentenceCollectRespFromJson(
        Map<String, dynamic> json) =>
    LocalSentenceCollectResp(
      id: json['id'] as String?,
      createat: json['createat'] == null
          ? null
          : DateTime.parse(json['createat'] as String),
      updateat: json['updateat'] == null
          ? null
          : DateTime.parse(json['updateat'] as String),
      uid: json['uid'] as String?,
      localSubtitleId: json['localSubtitleId'] as String?,
      indexs: (json['indexs'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$LocalSentenceCollectRespToJson(
        LocalSentenceCollectResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createat': instance.createat?.toIso8601String(),
      'updateat': instance.updateat?.toIso8601String(),
      'uid': instance.uid,
      'localSubtitleId': instance.localSubtitleId,
      'indexs': instance.indexs,
    };
