// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'langs.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LangsModel _$Lang<PERSON>odelFromJson(Map<String, dynamic> json) => LangsModel(
      targetLanguages: (json['targetLanguages'] as List<dynamic>?)
          ?.map((e) => LangModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      nativeLanguages: (json['nativeLanguages'] as List<dynamic>?)
          ?.map((e) => LangModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$Lang<PERSON>odelToJson(LangsModel instance) =>
    <String, dynamic>{
      'targetLanguages': instance.targetLanguages,
      'nativeLanguages': instance.nativeLanguages,
    };
