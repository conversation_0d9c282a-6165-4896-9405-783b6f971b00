import 'package:json_annotation/json_annotation.dart';

part 'lang.g.dart';

@JsonSerializable()
class LangModel {
  String? name;
  String? code;
  String? countryCode;

  LangModel({
    this.name,
    this.code,
    this.countryCode,
  });

  factory LangModel.fromJson(Map<String, dynamic> json) {
    return _$LangModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$LangModelToJson(this);
}
