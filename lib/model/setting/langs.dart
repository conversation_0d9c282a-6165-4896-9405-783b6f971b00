import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish/model/setting/lang.dart';

part 'langs.g.dart';

@JsonSerializable()
class LangsModel {
  List<LangModel>? targetLanguages;
  List<LangModel>? nativeLanguages;

  LangsModel({
    this.targetLanguages,
    this.nativeLanguages,
  });

  factory LangsModel.fromJson(Map<String, dynamic> json) {
    return _$LangsModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$LangsModelToJson(this);
}
