import 'package:json_annotation/json_annotation.dart';

class StringToDoubleConverter implements JsonConverter<double?, String?> {
  const StringToDoubleConverter();

  // 从 JSON 到 Dart 的转换
  @override
  double? fromJson(String? json) {
    if (json == null) return null;
    return double.tryParse(json);
  }

  // 从 Dart 到 JSON 的转换
  @override
  String? toJson(double? object) {
    if (object == null) return null;
    return object.toString();
  }
}
