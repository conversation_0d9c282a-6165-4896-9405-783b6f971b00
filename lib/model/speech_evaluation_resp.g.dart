// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'speech_evaluation_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SpeechEvaluationResp _$SpeechEvaluationRespFromJson(
        Map<String, dynamic> json) =>
    SpeechEvaluationResp(
      id: (json['id'] as num?)?.toInt(),
      resourceId: json['resourceId'] as String?,
      resourceType: (json['resourceType'] as num?)?.toInt(),
      content: json['content'] as String?,
      audioUrl: json['audioUrl'] as String?,
      startTime: (json['startTime'] as num?)?.toInt(),
      endTime: (json['endTime'] as num?)?.toInt(),
      evalResult: json['evalResult'] == null
          ? null
          : SpeechEvaluationResult.fromJson(
              json['evalResult'] as Map<String, dynamic>),
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      localAudioPath: json['localAudioPath'] as String?,
    );

Map<String, dynamic> _$SpeechEvaluationRespToJson(
        SpeechEvaluationResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'content': instance.content,
      'audioUrl': instance.audioUrl,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'evalResult': instance.evalResult,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'localAudioPath': instance.localAudioPath,
    };
