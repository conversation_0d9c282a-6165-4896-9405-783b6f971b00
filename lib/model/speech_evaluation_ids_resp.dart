import 'package:json_annotation/json_annotation.dart';

part 'speech_evaluation_ids_resp.g.dart';

/// 语音评测ID响应模型
@JsonSerializable()
class SpeechEvaluationIdsResp {
  final List<int>? ids;

  SpeechEvaluationIdsResp({
    this.ids,
  });

  factory SpeechEvaluationIdsResp.fromJson(Map<String, dynamic> json) => _$SpeechEvaluationIdsRespFromJson(json);

  Map<String, dynamic> toJson() => _$SpeechEvaluationIdsRespToJson(this);
} 