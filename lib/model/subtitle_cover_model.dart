import 'package:json_annotation/json_annotation.dart';

part 'subtitle_cover_model.g.dart';

@JsonSerializable()
class SubtitleCoverModel {
  double? initialWidth;
  double? initialHeight;
  double? bottom;

  SubtitleCoverModel({
    this.initialWidth,
    this.initialHeight,
    this.bottom,
  });

  factory SubtitleCoverModel.fromJson(Map<String, dynamic> json) {
    return _$SubtitleCoverModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SubtitleCoverModelToJson(this);
}
