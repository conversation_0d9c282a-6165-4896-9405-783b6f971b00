import 'package:json_annotation/json_annotation.dart';

part 'resource.g.dart';

@JsonSerializable()
class Resource {
  String? name;
  int? position;
  List<String>? localVideoPaths;
  String? cover;
  String? videoURL;

  Resource({this.name, this.position, this.localVideoPaths, this.cover, this.videoURL});

  factory Resource.fromJson(Map<String, dynamic> json) {
    return _$ResourceFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ResourceToJson(this);
}
