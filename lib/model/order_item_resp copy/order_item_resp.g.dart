// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_item_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderItemResp _$OrderItemRespFromJson(Map<String, dynamic> json) =>
    OrderItemResp(
      orderNo: json['orderNo'] as String?,
      outTransactionId: json['outTransactionId'] as String?,
      appleOriginalTransactionId: json['appleOriginalTransactionId'] as String?,
      uid: json['uid'] as String?,
      productId: (json['productID'] as num?)?.toInt(),
      productName: json['productName'] as String?,
      productType: (json['productType'] as num?)?.toInt(),
      userSubscriptionId: (json['userSubscriptionID'] as num?)?.toInt(),
      currency: json['currency'] as String?,
      amount: (json['amount'] as num?)?.toInt(),
      paymentProvider: (json['paymentProvider'] as num?)?.toInt(),
      paidTimestamp: (json['paidTimestamp'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
    );

Map<String, dynamic> _$OrderItemRespToJson(OrderItemResp instance) =>
    <String, dynamic>{
      'orderNo': instance.orderNo,
      'outTransactionId': instance.outTransactionId,
      'appleOriginalTransactionId': instance.appleOriginalTransactionId,
      'uid': instance.uid,
      'productID': instance.productId,
      'productName': instance.productName,
      'productType': instance.productType,
      'userSubscriptionID': instance.userSubscriptionId,
      'currency': instance.currency,
      'amount': instance.amount,
      'paymentProvider': instance.paymentProvider,
      'paidTimestamp': instance.paidTimestamp,
      'status': instance.status,
    };
