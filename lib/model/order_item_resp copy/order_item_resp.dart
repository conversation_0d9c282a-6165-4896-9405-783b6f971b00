import 'package:json_annotation/json_annotation.dart';

part 'order_item_resp.g.dart';

@JsonSerializable()
class OrderItemResp {
  String? orderNo;
  String? outTransactionId;
  String? appleOriginalTransactionId;
  String? uid;
  @J<PERSON><PERSON>ey(name: 'productID')
  int? productId;
  String? productName;
  int? productType;
  @JsonKey(name: 'userSubscriptionID')
  int? userSubscriptionId;
  String? currency;
  int? amount;
  int? paymentProvider;
  int? paidTimestamp;
  int? status;

  OrderItemResp({
    this.orderNo,
    this.outTransactionId,
    this.appleOriginalTransactionId,
    this.uid,
    this.productId,
    this.productName,
    this.productType,
    this.userSubscriptionId,
    this.currency,
    this.amount,
    this.paymentProvider,
    this.paidTimestamp,
    this.status,
  });

  factory OrderItemResp.fromJson(Map<String, dynamic> json) {
    return _$OrderItemRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$OrderItemRespToJson(this);
}
