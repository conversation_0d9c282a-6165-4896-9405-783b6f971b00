// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subtitle_cover_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubtitleCoverModel _$SubtitleCoverModelFromJson(Map<String, dynamic> json) =>
    SubtitleCoverModel(
      initialWidth: (json['initialWidth'] as num?)?.toDouble(),
      initialHeight: (json['initialHeight'] as num?)?.toDouble(),
      bottom: (json['bottom'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$SubtitleCoverModelToJson(SubtitleCoverModel instance) =>
    <String, dynamic>{
      'initialWidth': instance.initialWidth,
      'initialHeight': instance.initialHeight,
      'bottom': instance.bottom,
    };
