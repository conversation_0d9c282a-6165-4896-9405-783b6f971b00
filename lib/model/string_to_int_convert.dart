import 'package:json_annotation/json_annotation.dart';

class StringToIntConverter implements JsonConverter<int?, String?> {
  const StringToIntConverter();

  // 从 JSON 到 Dart 的转换
  @override
  int? fromJson(String? json) {
    if (json == null) return null;
    return int.tryParse(json);
  }

  // 从 Dart 到 JSON 的转换
  @override
  String? toJson(int? object) {
    if (object == null) return null;
    return object.toString();
  }
}
