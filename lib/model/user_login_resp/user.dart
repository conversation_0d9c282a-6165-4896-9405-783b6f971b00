import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  String? id;
  String? username;
  String? nickname;
  int? status;
  String? avatar;
  String? nativeLangCode;
  String? targetLangCode;

  User({this.id, this.username, this.nickname, this.status, this.avatar, this.nativeLangCode, this.targetLangCode});

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);
}
