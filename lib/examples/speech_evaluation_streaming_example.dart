import 'package:flutter/material.dart';
import 'package:lsenglish/utils/speech_evaluation.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';

/// 语音评测流式结果使用示例
class SpeechEvaluationStreamingExample extends StatefulWidget {
  const SpeechEvaluationStreamingExample({Key? key}) : super(key: key);

  @override
  State<SpeechEvaluationStreamingExample> createState() => _SpeechEvaluationStreamingExampleState();
}

class _SpeechEvaluationStreamingExampleState extends State<SpeechEvaluationStreamingExample> {
  final SpeechEvaluation _speechEval = SpeechEvaluation.instance;
  String _refText = "Hello world";
  final List<SpeechEvaluationResult> _intermediateResults = [];
  SpeechEvaluationResult? _finalResult;
  String _status = "未开始";

  @override
  void initState() {
    super.initState();
    _initSpeechEvaluation();
  }

  void _initSpeechEvaluation() async {
    // 初始化语音评测引擎
    await _speechEval.init();

    // 监听中间结果
    _speechEval.addIntermediateResultListener(_onIntermediateResult);
    
    // 监听最终结果
    _speechEval.addFinalResultListener(_onFinalResult);
    
    // 监听录音状态
    _speechEval.isRecording.addListener(_onRecordingStateChanged);
    
    // 监听错误
    _speechEval.errorNotifier.addListener(_onError);
  }

  void _onIntermediateResult() {
    final result = _speechEval.intermediateResultNotifier.value;
    if (result != null) {
      setState(() {
        _intermediateResults.add(result);
        _status = "收到中间结果 ${_intermediateResults.length}";
      });
      debugPrint("中间结果: eof=${result.eof}, result=${result.result}");
    }
  }

  void _onFinalResult() {
    final result = _speechEval.finalResultNotifier.value;
    if (result != null) {
      setState(() {
        _finalResult = result;
        _status = "评测完成";
      });
      debugPrint("最终结果: eof=${result.eof}, result=${result.result}");
    }
  }

  void _onRecordingStateChanged() {
    setState(() {
      _status = _speechEval.isRecording.value ? "录音中..." : "录音停止";
    });
  }

  void _onError() {
    final error = _speechEval.errorNotifier.value;
    if (error != null) {
      setState(() {
        _status = "错误: $error";
      });
    }
  }

  void _startStreamingEvaluation() {
    setState(() {
      _intermediateResults.clear();
      _finalResult = null;
      _status = "开始流式评测";
    });
    
    // 启动流式句子评测
    _speechEval.startSentenceWithRealtime(_refText);
  }

  void _startNormalEvaluation() {
    setState(() {
      _intermediateResults.clear();
      _finalResult = null;
      _status = "开始普通评测";
    });
    
    // 启动普通句子评测（无中间结果）
    _speechEval.startSentence(_refText);
  }

  void _stopEvaluation() {
    _speechEval.stop();
  }

  void _cancelEvaluation() {
    _speechEval.cancel();
    setState(() {
      _intermediateResults.clear();
      _finalResult = null;
      _status = "已取消";
    });
  }

  @override
  void dispose() {
    // 移除监听器
    _speechEval.removeIntermediateResultListener(_onIntermediateResult);
    _speechEval.removeFinalResultListener(_onFinalResult);
    _speechEval.isRecording.removeListener(_onRecordingStateChanged);
    _speechEval.errorNotifier.removeListener(_onError);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('语音评测流式示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 参考文本输入
            TextField(
              decoration: const InputDecoration(
                labelText: '参考文本',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) => _refText = value,
              controller: TextEditingController(text: _refText),
            ),
            const SizedBox(height: 16),
            
            // 状态显示
            Text('状态: $_status', style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            
            // 控制按钮
            Row(
              children: [
                ElevatedButton(
                  onPressed: _speechEval.isEngineInited.value && !_speechEval.isRecording.value
                      ? _startStreamingEvaluation
                      : null,
                  child: const Text('开始流式评测'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _speechEval.isEngineInited.value && !_speechEval.isRecording.value
                      ? _startNormalEvaluation
                      : null,
                  child: const Text('开始普通评测'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _speechEval.isRecording.value ? _stopEvaluation : null,
                  child: const Text('停止'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _speechEval.isRecording.value ? _cancelEvaluation : null,
                  child: const Text('取消'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 结果显示
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 中间结果
                    if (_intermediateResults.isNotEmpty) ...[
                      const Text('中间结果:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      ..._intermediateResults.asMap().entries.map((entry) {
                        final index = entry.key;
                        final result = entry.value;
                        return Card(
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('中间结果 ${index + 1}'),
                                Text('EOF: ${result.eof}'),
                                Text('时间: ${result.dtLastResponse}'),
                                if (result.result != null)
                                  Text('结果: ${result.result.toString()}'),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                      const SizedBox(height: 16),
                    ],
                    
                    // 最终结果
                    if (_finalResult != null) ...[
                      const Text('最终结果:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('EOF: ${_finalResult!.eof}'),
                              Text('时间: ${_finalResult!.dtLastResponse}'),
                              if (_finalResult!.result != null)
                                Text('结果: ${_finalResult!.result.toString()}'),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
