// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars
// ignore: avoid_classes_with_only_static_members
class AppTranslation {
  static Map<String, Map<String, String>> translations = {
    'zh_CN': Locales.zh_CN,
    'en_US': Locales.en_US,
  };
}

class LocaleKeys {
  LocaleKeys._();
  static const net_connectionTimeout = 'net_connectionTimeout';
  static const net_sendTimeout = 'net_sendTimeout';
  static const net_receiveTimeout = 'net_receiveTimeout';
  static const net_badResponse = 'net_badResponse';
  static const net_cancel = 'net_cancel';
  static const net_connectionError = 'net_connectionError';
  static const net_400 = 'net_400';
  static const net_401 = 'net_401';
  static const net_403 = 'net_403';
  static const net_404 = 'net_404';
  static const net_408 = 'net_408';
  static const net_500 = 'net_500';
  static const net_501 = 'net_501';
  static const net_502 = 'net_502';
  static const net_503 = 'net_503';
  static const net_504 = 'net_504';
  static const net_505 = 'net_505';
  static const net_default_code_error = 'net_default_code_error';
  static const toast_submitSuccess = 'toast_submitSuccess';
  static const toast_submitFail = 'toast_submitFail';
  static const toast_saveSuccess = 'toast_saveSuccess';
  static const toast_noData = 'toast_noData';
  static const setting_title = 'setting_title';
  static const setting_switchLanguage = 'setting_switchLanguage';
  static const setting_switchTheme = 'setting_switchTheme';
  static const setting_logout = 'setting_logout';
  static const setting_uploadLog = 'setting_uploadLog';
  static const setting_clearCache = 'setting_clearCache';
  static const setting_about = 'setting_about';
  static const setting_orderList = 'setting_orderList';
  static const setting_voiceTransList = 'setting_voiceTransList';
  static const setting_videoCompressList = 'setting_videoCompressList';
}

class Locales {
  static const zh_CN = {
    'net_connectionTimeout': '网络连接超时，请检查网络设置',
    'net_sendTimeout': '服务器异常，请稍后重试！',
    'net_receiveTimeout': '网络连接超时，请检查网络设置',
    'net_badResponse': '服务器异常，请稍后重试！',
    'net_cancel': '请求已被取消，请重新请求',
    'net_connectionError': '网络异常，请稍后重试！',
    'net_400': '请求语法错误',
    'net_401': '未授权，请登录',
    'net_403': '拒绝访问',
    'net_404': '请求地址404',
    'net_408': '请求超时',
    'net_500': '服务器异常',
    'net_501': '服务未实现',
    'net_502': '网关错误',
    'net_503': '服务不可用',
    'net_504': '网关超时',
    'net_505': 'HTTP版本不受支持',
    'net_default_code_error': '请求失败，错误码',
    'toast_submitSuccess': '提交成功',
    'toast_submitFail': '提交失败',
    'toast_saveSuccess': '保存成功',
    'toast_noData': '暂无数据',
    'setting_title': '设置',
    'setting_switchLanguage': '切换语言',
    'setting_switchTheme': '切换主题',
    'setting_logout': '退出登录',
    'setting_uploadLog': '上传日志',
    'setting_clearCache': '清除缓存',
    'setting_about': '关于',
    'setting_orderList': '订单列表',
    'setting_voiceTransList': '语音转写列表',
    'setting_videoCompressList': '视频压缩列表',
  };
  static const en_US = {
    'net_connectionTimeout': 'Connection Timeout',
    'net_sendTimeout': 'Server exception, please try again later!',
    'net_receiveTimeout':
        'Network connection timeout, please check network settings',
    'net_badResponse':
        'Network connection timed out, please check network settings',
    'net_cancel': 'The request has been canceled, please request again',
    'net_connectionError': 'Network abnormality, please try again later!',
    'net_400': 'Request syntax error',
    'net_401': 'Unauthorized, please log in',
    'net_403': 'Access Denied',
    'net_404': 'Request address 404',
    'net_408': 'Request timed out',
    'net_500': 'Server exception',
    'net_501': 'Service not implemented',
    'net_502': 'Gateway error',
    'net_503': 'Service is not available',
    'net_504': 'Gateway timeout',
    'net_505': 'HTTP version is not supported',
    'net_default_code_error': 'Request failed, error code',
    'toast_submitSuccess': 'SubmitSuccess',
    'toast_submitFail': 'SubmitFail',
    'toast_saveSuccess': 'SaveSuccess',
    'toast_noData': 'No Data',
    'setting_title': 'Setting',
    'setting_switchLanguage': 'Switch Language',
    'setting_switchTheme': 'Switch Theme',
    'setting_logout': 'Logout',
    'setting_uploadLog': 'Upload Log',
    'setting_clearCache': 'Clear Cache',
    'setting_about': 'About',
    'setting_orderList': 'Order List',
    'setting_voiceTransList': 'VoiceTransList',
    'setting_videoCompressList': 'VideoCompressList',
  };
}
