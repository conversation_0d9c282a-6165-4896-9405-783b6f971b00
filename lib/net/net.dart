import 'dart:io';
import 'dart:ui';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import 'client.dart';
import 'interceptor.dart';

class Net {
  static RestClient? _restClient;
  static final Dio _dio = Dio();

  static void configureDio({required String baseUrl, String charlesIp = ""}) {
    _dio.options = BaseOptions(
      baseUrl: baseUrl,
      sendTimeout: const Duration(seconds: 60),
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      contentType: "application/json",
      responseType: ResponseType.json,
    );
    //根据业务端传入的type来设置网络请求日志级别
    if (kDebugMode) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        request: true,
      ));
      if (charlesIp.isNotEmpty) {
        // ignore: body_might_complete_normally_nullable, deprecated_member_use
        (_dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate = (client) {
          client.findProxy = (uri) {
            return 'PROXY $charlesIp:8888';
          };
          client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
        };
      }
    }
    (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
      client.badCertificateCallback = (X509Certificate cert, String host, int port) {
        if (host == "secure.assrt.net") {
          return true; // Trust this host
        }
        return false;
      };
      return null;
    };
    _dio.interceptors.add(ApiInterceptor());
    _dio.options.headers['platform'] = GetPlatform.isIOS
        ? "ios"
        : GetPlatform.isAndroid
            ? "android"
            : GetPlatform.isMacOS
                ? "macos"
                : "";
    Locale deviceLocale = window.locale;
    String languageCode = deviceLocale.languageCode;
    _dio.options.headers['Accept-Language'] = languageCode;
    _dio.options.headers['targetLangCode'] = "en-US";
    _dio.options.headers['nativeLangCode'] = "zh-CN";
  }

  static RestClient getRestClient() {
    _restClient ??= RestClient(_dio);
    return _restClient!;
  }

  static Dio getDio() {
    return _dio;
  }
}
