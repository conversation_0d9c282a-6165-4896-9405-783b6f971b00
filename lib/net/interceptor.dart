import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;
import 'package:lsenglish/app/routes/app_pages.dart';
import 'exception.dart';

class ApiInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.statusCode == 200) {
      final responseBody = response.data;
      if (responseBody is Map) {
        final status = responseBody['code'];
        final message = responseBody['msg'];
        if (status != null && status != 200) {
          if (status == 401) {
            _handleUnauthorizedError();
          } else {
            handler.reject(
              DioException(
                requestOptions: response.requestOptions,
                response: response,
                error: ApiException(message, status),
              ),
              true,
            );
            return;
          }
        }
      }
      super.onResponse(response, handler);
    } else if (response.statusCode == 401) {
      _handleUnauthorizedError();
    } else if ((response.statusCode ?? 0) >= 500) {
      handler.reject(
        DioException(
          requestOptions: response.requestOptions,
          response: response,
          error:
              ApiException('Invalid response status code', response.statusCode),
        ),
        true,
      );
    } else {
      super.onResponse(response, handler);
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      _handleUnauthorizedError();
    }
    super.onError(err, handler);
  }

  void _handleUnauthorizedError() {
    getx.Get.toNamed(Routes.LOGIN);
  }
}
