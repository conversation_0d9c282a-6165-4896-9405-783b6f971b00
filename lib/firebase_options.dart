// // File generated by FlutterFire CLI.
// // ignore_for_file: type=lint
// import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
// import 'package:flutter/foundation.dart'
//     show defaultTargetPlatform, kIsWeb, TargetPlatform;

// /// Default [FirebaseOptions] for use with your Firebase apps.
// ///
// /// Example:
// /// ```dart
// /// import 'firebase_options.dart';
// /// // ...
// /// await Firebase.initializeApp(
// ///   options: DefaultFirebaseOptions.currentPlatform,
// /// );
// /// ```
// class DefaultFirebaseOptions {
//   static FirebaseOptions get currentPlatform {
//     if (kIsWeb) {
//       return web;
//     }
//     switch (defaultTargetPlatform) {
//       case TargetPlatform.android:
//         return android;
//       case TargetPlatform.iOS:
//         return ios;
//       case TargetPlatform.macOS:
//         return macos;
//       case TargetPlatform.windows:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions have not been configured for windows - '
//           'you can reconfigure this by running the FlutterFire CLI again.',
//         );
//       case TargetPlatform.linux:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions have not been configured for linux - '
//           'you can reconfigure this by running the FlutterFire CLI again.',
//         );
//       default:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions are not supported for this platform.',
//         );
//     }
//   }

//   static const FirebaseOptions web = FirebaseOptions(
//     apiKey: 'AIzaSyD412tgX5C6BEOa7suDru6xsmM6A3O4-t8',
//     appId: '1:75452424013:web:055944f6ee660c5cbb6104',
//     messagingSenderId: '75452424013',
//     projectId: 'ls100-36631',
//     authDomain: 'ls100-36631.firebaseapp.com',
//     storageBucket: 'ls100-36631.appspot.com',
//     measurementId: 'G-BHE7WN557M',
//   );

//   static const FirebaseOptions android = FirebaseOptions(
//     apiKey: 'AIzaSyDPelHvCu0Y4C9gSARkjBwnxXDLk0PiKs0',
//     appId: '1:75452424013:android:dbc395fe04739bccbb6104',
//     messagingSenderId: '75452424013',
//     projectId: 'ls100-36631',
//     storageBucket: 'ls100-36631.appspot.com',
//   );

//   static const FirebaseOptions ios = FirebaseOptions(
//     apiKey: 'AIzaSyBPIX-w5VH_V3Qa2Wt-YXOSn0vUAxMbrHY',
//     appId: '1:75452424013:ios:014f8bdabc0caae4bb6104',
//     messagingSenderId: '75452424013',
//     projectId: 'ls100-36631',
//     storageBucket: 'ls100-36631.appspot.com',
//     iosBundleId: 'com.seedtu.ls',
//   );

//   static const FirebaseOptions macos = FirebaseOptions(
//     apiKey: 'AIzaSyBPIX-w5VH_V3Qa2Wt-YXOSn0vUAxMbrHY',
//     appId: '1:75452424013:ios:7286dffa8390522bbb6104',
//     messagingSenderId: '75452424013',
//     projectId: 'ls100-36631',
//     storageBucket: 'ls100-36631.appspot.com',
//     iosBundleId: 'free.lsenglish',
//   );

// }