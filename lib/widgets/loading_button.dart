import 'package:flutter/material.dart';

enum LoadingButtonState {
  idle,
  loading,
}

enum LoadingButtonType {
  elevated,
  outlined,
  text,
}

class LoadingButton extends StatefulWidget {
  /// Content inside the button when the button state is idle.
  final Widget idleStateWidget;

  /// Content inside the button when the button state is loading.
  final Widget loadingStateWidget;

  /// The button type.
  final LoadingButtonType type;

  /// Whether or not to animate the width of the button. Default is `true`.
  final bool useWidthAnimation;

  /// Whether or not to force the `loadingStateWidget` to have equal dimension.
  final bool useEqualLoadingStateWidgetDimension;

  /// The button width.
  final double width;

  /// The button height.
  final double height;

  /// The gap between button and it's content.
  final double contentGap;

  /// The visual border radius of the button.
  final double borderRadius;

  /// The elevation of the button.
  final double elevation;

  /// Color for the button.
  final Color buttonColor;

  /// The initial state of the button.
  final LoadingButtonState state;

  /// Function to run when button is pressed.
  /// Should return true for success, false for failure.
  final Future<bool> Function()? onAsyncPressed;
  final VoidCallback? onPressed;

  LoadingButton({
    Key? key,
    required this.idleStateWidget,
    Widget? loadingStateWidget,
    this.type = LoadingButtonType.elevated,
    this.useWidthAnimation = true,
    this.useEqualLoadingStateWidgetDimension = true,
    this.width = double.infinity,
    this.height = 48.0,
    this.contentGap = 16.0,
    this.borderRadius = 12.0,
    this.elevation = 0.0,
    this.buttonColor = Colors.blue,
    this.onAsyncPressed,
    this.onPressed,
    this.state = LoadingButtonState.idle,
  })  : loadingStateWidget = loadingStateWidget ??
            const SizedBox.square(
              dimension: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
        assert(
          onPressed != null || onAsyncPressed != null,
          'Either onPressed or onAsyncPressed must be provided',
        ),
        super(key: key);

  /// 便捷构造函数 - 使用文本
  /// 
  /// [onAsyncPressed] 应该返回 true 表示成功，false 表示失败。
  /// 当返回 false 时，按钮会自动恢复到原始状态。
  LoadingButton.text({
    Key? key,
    required String text,
    Widget? loadingStateWidget,
    this.type = LoadingButtonType.elevated,
    this.useWidthAnimation = true,
    this.useEqualLoadingStateWidgetDimension = true,
    this.width = double.infinity,
    this.height = 50.0,
    this.contentGap = 16.0,
    this.borderRadius = 12.0,
    this.elevation = 0.0,
    this.buttonColor = Colors.blue,
    this.onAsyncPressed,
    this.onPressed,
    this.state = LoadingButtonState.idle,
  })  : idleStateWidget = Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        loadingStateWidget = loadingStateWidget ??
            const SizedBox.square(
              dimension: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
        assert(
          onPressed != null || onAsyncPressed != null,
          'Either onPressed or onAsyncPressed must be provided',
        ),
        super(key: key);

  @override
  State<LoadingButton> createState() => _LoadingButtonState();
}

class _LoadingButtonState extends State<LoadingButton> with TickerProviderStateMixin {
  final GlobalKey _globalKey = GlobalKey();

  Animation<double>? _anim;
  AnimationController? _animController;
  final Duration _duration = const Duration(milliseconds: 300);
  late double _width;
  late double _height;
  late double _borderRadius;
  late LoadingButtonState _currentState;

  @override
  void initState() {
    super.initState();
    _reset();
    _currentState = widget.state;
  }

  @override
  void dispose() {
    _animController?.dispose();
    super.dispose();
  }

  @override
  void deactivate() {
    _reset();
    super.deactivate();
  }

  @override
  void didUpdateWidget(LoadingButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update internal state when widget state changes externally
    if (oldWidget.state != widget.state) {
      setState(() {
        _currentState = widget.state;
      });
    }
  }

  void _reset() {
    _width = widget.width;
    _height = widget.height;
    _borderRadius = widget.borderRadius;
  }

  @override
  Widget build(BuildContext context) {
    return PhysicalModel(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(_borderRadius),
      child: Center(
        child: SizedBox(
          key: _globalKey,
          height: _height,
          width: _width,
          child: _buildChild(context),
        ),
      ),
    );
  }

  Widget _buildChild(BuildContext context) {
    var padding = EdgeInsets.all(widget.contentGap);
    var buttonColor = widget.buttonColor;
    var shape = RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
    );

    final ButtonStyle elevatedButtonStyle = ElevatedButton.styleFrom(
      padding: padding,
      backgroundColor: buttonColor,
      elevation: widget.elevation,
      shape: shape,
    );

    final ButtonStyle outlinedButtonStyle = OutlinedButton.styleFrom(
      padding: padding,
      shape: shape,
      side: BorderSide(color: buttonColor),
    );

    final ButtonStyle textButtonStyle = TextButton.styleFrom(
      padding: padding,
    );

    switch (widget.type) {
      case LoadingButtonType.elevated:
        return ElevatedButton(
          style: elevatedButtonStyle,
          onPressed: _onButtonPressed(),
          child: _buildChildren(context),
        );
      case LoadingButtonType.outlined:
        return OutlinedButton(
          style: outlinedButtonStyle,
          onPressed: _onButtonPressed(),
          child: _buildChildren(context),
        );
      case LoadingButtonType.text:
        return TextButton(
          style: textButtonStyle,
          onPressed: _onButtonPressed(),
          child: _buildChildren(context),
        );
    }
  }

  Widget _buildChildren(BuildContext context) {
    double contentGap = widget.type == LoadingButtonType.text ? 0.0 : widget.contentGap;
    Widget contentWidget;

    switch (_currentState) {
      case LoadingButtonState.idle:
        contentWidget = widget.idleStateWidget;
        break;
      case LoadingButtonState.loading:
        contentWidget = widget.loadingStateWidget;

        if (widget.useEqualLoadingStateWidgetDimension) {
          // 当按钮变成圆形时，确保进度指示器完全居中
          // 使用按钮的实际高度作为进度指示器的尺寸
          contentWidget = SizedBox.square(
            dimension: _height - (contentGap * 2),
            child: Center(
              child: widget.loadingStateWidget,
            ),
          );
        }
        break;
    }

    return contentWidget;
  }

  VoidCallback? _onButtonPressed() {
    if (widget.onPressed == null && widget.onAsyncPressed == null) {
      return null;
    }

    return _manageLoadingState;
  }

  Future<void> _manageLoadingState() async {
    // If external state control is being used, don't manage state internally
    if (widget.state != LoadingButtonState.idle) {
      // Just call the onPressed function without state management
      if (widget.onAsyncPressed != null) {
        await widget.onAsyncPressed!();
      } else if (widget.onPressed != null) {
        widget.onPressed!();
      }
      return;
    }

    if (_currentState != LoadingButtonState.idle) {
      return;
    }

    if (widget.useWidthAnimation) {
      _toProcessing();
      _forward((status) async {
        if (status == AnimationStatus.completed) {
          // 动画完成后执行异步操作
          try {
            if (widget.onAsyncPressed != null) {
              final success = await widget.onAsyncPressed!();
              if (!success) {
                // 如果操作失败，立即恢复到默认状态
                _toDefault();
                _reverse();
                return;
              }
            } else if (widget.onPressed != null) {
              widget.onPressed!();
            }
            _reverse();
          } catch (e) {
            // 如果发生异常，立即恢复到默认状态
            _toDefault();
            _reverse();
            rethrow; // 重新抛出异常，让调用者知道操作失败
          }
        } else if (status == AnimationStatus.dismissed) {
          _toDefault();
        }
      });
    } else {
      _toProcessing();

      try {
        if (widget.onAsyncPressed != null) {
          final success = await widget.onAsyncPressed!();
          if (!success) {
            // 如果操作失败，立即恢复到默认状态
            _toDefault();
            return;
          }
        } else if (widget.onPressed != null) {
          widget.onPressed!();
        }
        _toDefault();
      } catch (e) {
        // 如果发生异常，立即恢复到默认状态
        _toDefault();
        rethrow; // 重新抛出异常，让调用者知道操作失败
      }
    }
  }

  void _toProcessing() {
    setState(() {
      _currentState = LoadingButtonState.loading;
    });
  }

  void _toDefault() {
    if (mounted) {
      setState(() {
        _currentState = LoadingButtonState.idle;
      });
    } else {
      _currentState = LoadingButtonState.idle;
    }
  }

  void _forward(AnimationStatusListener stateListener) {
    double initialWidth = _globalKey.currentContext!.size!.width;
    double initialBorderRadius = widget.borderRadius;
    double targetWidth = _height;
    double targetBorderRadius = _height / 2;

    _animController = AnimationController(duration: _duration, vsync: this);
    _anim = Tween<double>(begin: 0.0, end: 1.0).animate(_animController!)
      ..addListener(() {
        setState(() {
          _width = initialWidth - ((initialWidth - targetWidth) * _anim!.value);
          _borderRadius = initialBorderRadius - ((initialBorderRadius - targetBorderRadius) * _anim!.value);
        });
      })
      ..addStatusListener(stateListener);

    _animController!.forward();
  }

  void _reverse() {
    _animController!.reverse();
  }
}
