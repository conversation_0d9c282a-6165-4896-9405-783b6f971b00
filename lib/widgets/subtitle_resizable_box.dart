import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/sp.dart';

import '../model/subtitle_cover_model.dart';

class SubtitleResizableBox extends StatefulWidget {
  final double initialWidth;
  final double initialHeight;
  final double parentHeight;
  final double parentWidth;
  final double bottom;
  final String videoPath;

  const SubtitleResizableBox({
    Key? key,
    required this.videoPath,
    required this.initialWidth,
    required this.initialHeight,
    required this.parentHeight,
    required this.parentWidth,
    required this.bottom,
  }) : super(key: key);

  @override
  SubtitleResizableBoxState createState() => SubtitleResizableBoxState();
}

class SubtitleResizableBoxState extends State<SubtitleResizableBox>
    with TickerProviderStateMixin {
  late double width;
  late double height;
  late double top;
  late double left;
  int minOffset = 20;
  Timer? hideTimer;
  late AnimationController _controller;
  bool isInteracting = false;
  SubtitleCoverModel subtitleCoverModel = SubtitleCoverModel();

  @override
  void initState() {
    super.initState();
    loadUIData();
    loadModel();
  }

  void loadUIData() {
    width = subtitleCoverModel.initialWidth ?? widget.initialWidth;
    height = subtitleCoverModel.initialHeight ?? widget.initialHeight;
    top = widget.parentHeight -
        height -
        (subtitleCoverModel.bottom ?? widget.bottom);
    left = (widget.parentWidth - width) / 2;
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed ||
          status == AnimationStatus.dismissed) {
        isInteracting = false;
      }
    });
    _controller.forward();
    // 设置初始自动隐藏
    setAutoHide();
  }

  void loadModel() async {
    subtitleCoverModel = await SPUtil().getSubtitleCover(widget.videoPath) ??
        SubtitleCoverModel();
    setState(() {
      loadUIData();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void updateSize(double dx, double dy, Alignment alignment) {
    setState(() {
      switch (alignment) {
        case Alignment.topLeft:
          if (width - dx > minOffset) {
            width -= dx;
            left += dx;
          }
          if (height - dy > minOffset) {
            height -= dy;
            top += dy;
          }
          break;
        case Alignment.topRight:
          if (width + dx > minOffset) {
            width += dx;
          }
          if (height - dy > minOffset) {
            height -= dy;
            top += dy;
          }
          break;
        case Alignment.bottomLeft:
          if (width - dx > minOffset) {
            width -= dx;
            left += dx;
          }
          if (height + dy > minOffset) {
            height += dy;
          }
          break;
        case Alignment.bottomRight:
          if (width + dx > minOffset) {
            width += dx;
          }
          if (height + dy > minOffset) {
            height += dy;
          }
          break;
      }
    });
    Config().subtitleCoverModel = SubtitleCoverModel(
      initialWidth: width,
      initialHeight: height,
      bottom: widget.parentHeight - height - top,
    );
  }

  void setAutoHide() {
    hideTimer?.cancel();
    hideTimer = Timer(const Duration(seconds: 2), () {
      _controller.reverse();
    });
  }

  void onDragUpdate(DragUpdateDetails details) {
    setState(() {
      left += details.delta.dx;
      top += details.delta.dy;
    });
    if (!isInteracting) {
      isInteracting = true;
      _controller.forward();
    }
    Config().subtitleCoverModel = SubtitleCoverModel(
      initialWidth: width,
      initialHeight: height,
      bottom: widget.parentHeight - height - top,
    );
    setAutoHide();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanUpdate: onDragUpdate,
      onTap: () {
        _controller.forward();
        setAutoHide();
      },
      child: Stack(
        children: [
          Positioned(
            left: left,
            top: top,
            child: ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 2.5, sigmaY: 2.5),
                child: FadeTransition(
                  opacity: _controller,
                  child: Container(
                    width: width,
                    height: height,
                    color: Colors.transparent,
                    child: Stack(
                      children: [
                        buildCornerAdjuster(Alignment.topLeft),
                        buildCornerAdjuster(Alignment.topRight),
                        buildCornerAdjuster(Alignment.bottomLeft),
                        buildCornerAdjuster(Alignment.bottomRight),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildCornerAdjuster(Alignment alignment) {
    return Align(
      alignment: alignment,
      child: GestureDetector(
        onPanUpdate: (details) {
          updateSize(details.delta.dx, details.delta.dy, alignment);
        },
        child: SizedBox(
          width: 20,
          height: 20,
          child: CustomPaint(
            painter: CornerPainter(alignment: alignment),
          ),
        ),
      ),
    );
  }
}

class CornerPainter extends CustomPainter {
  final Alignment alignment;

  CornerPainter({required this.alignment});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 3;

    switch (alignment) {
      case Alignment.topLeft:
        // Horizontal line
        canvas.drawLine(const Offset(0, 0), Offset(size.width, 0), paint);
        // Vertical line
        canvas.drawLine(const Offset(0, 0), Offset(0, size.height), paint);
        break;
      case Alignment.topRight:
        // Horizontal line
        canvas.drawLine(const Offset(0, 0), Offset(size.width, 0), paint);
        // Vertical line
        canvas.drawLine(
            Offset(size.width, 0), Offset(size.width, size.height), paint);
        break;
      case Alignment.bottomLeft:
        // Horizontal line
        canvas.drawLine(
            Offset(0, size.height), Offset(size.width, size.height), paint);
        // Vertical line
        canvas.drawLine(const Offset(0, 0), Offset(0, size.height), paint);
        break;
      case Alignment.bottomRight:
        // Horizontal line
        canvas.drawLine(
            Offset(0, size.height), Offset(size.width, size.height), paint);
        // Vertical line
        canvas.drawLine(
            Offset(size.width, 0), Offset(size.width, size.height), paint);
        break;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
