import 'package:flutter/material.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/model/setting/lang.dart';

class LanguageChooseWidget extends StatefulWidget {
  final Function(LangModel)? onLanguageSelected;

  const LanguageChooseWidget({
    super.key,
    this.onLanguageSelected,
  });

  @override
  State<LanguageChooseWidget> createState() => _LanguageChooseWidgetState();
}

class _LanguageChooseWidgetState extends State<LanguageChooseWidget> {
  @override
  Widget build(BuildContext context) {
    final nativeLanguages = Config().langs.nativeLanguages;

    if (nativeLanguages == null || nativeLanguages.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        itemCount: nativeLanguages.length,
        itemBuilder: (context, index) {
          final language = nativeLanguages[index];
          return ListTile(
            title: Text(language.name ?? ""),
            onTap: () {
              if (widget.onLanguageSelected != null) {
                widget.onLanguageSelected!(language);
              }
            },
          );
        },
      ),
    );
  }
}
