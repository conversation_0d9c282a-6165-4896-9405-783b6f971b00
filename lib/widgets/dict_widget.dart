import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:lsenglish/model/english_dict_model/english_rel_word_model/rel_sub.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

import '../app/modules/detail/controllers/detail_controller.dart';
import '../model/english_dict_model/english_dict_model.dart';
import '../model/english_dict_model/english_ec_model/trs.dart';
import '../model/english_dict_model/english_ec_model/wfs.dart';
// https://www.shanxing.top/archives/40
// 单词音频接口：
// http://dict.youdao.com/dictvoice?type=音频类型&audio=单词
// 请求方法：get
// type=0 : 美音
// type=1 : 英音
// audio : 单词

// collins — 柯林斯英汉双解大辞典
// ec — 英汉翻译字典（英汉释义）
// ee — 英英翻译字典（英英释义）
// exam_dict — 考试专用字典（词汇词根+联想记忆法）
// longman — 朗文当代高级英语辞典
// phrs — 词组短语，如 account 单词对应的“current account”
// rel_word — 同根词，如 account 单词对应的形容词“accountable”
// syno — 近义词，单词每种释义对应的近义词列表
// web_trans — 网络释义

// "blng_sents_part",//双语例句
// auth_sents_part 权威例句
// rel_word 同根词
// syno 同近义词
// ee 英英释义

// ```json
// {
//   "count": 99,
//   "dicts": [
//     ["ec", "collins", "input", "ee", "syno", "rel_word", "blng_sents_part"],
//     ["web_trans"]
//   ]
// }
class DictWidget extends StatefulWidget {
  final String word;
  final bool autoPlayDict;
  const DictWidget({super.key, required this.word, required this.autoPlayDict});

  @override
  State<DictWidget> createState() => _DictWidgetState();

  // 添加一个静态方法来显示字典模态框
  static void showDictModal(BuildContext context, String word, bool autoPlayDict) {
    WoltModalSheet.show<void>(
      context: context,
      pageListBuilder: (modalContext) {
        return [
          WoltModalSheetPage(
            trailingNavBarWidget: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
            child: _DictContent(
              word: word,
              autoPlayDict: autoPlayDict,
            ),
          ),
        ];
      },
    );
  }
}

class _DictWidgetState extends State<DictWidget> {
  String usphone = "";
  String ukphone = "";
  String simple = "";
  List<Wfs> wfs = [];
  List<Trs> trsList = [];
  List<RelSub> relList = [];
  bool isClosing = false;
  bool canHandleDragDismiss = true;
  bool scrollEnd = true;
  FlutterSoundPlayer soundPlayer = FlutterSoundPlayer(logLevel: Level.off);
  // 用于切换美/英音
  bool isUs = true;

  @override
  void initState() {
    super.initState();
    if (Get.isRegistered<DetailController>()) {
      Get.find<DetailController>().detailState.videoKit.pause();
    }
    fetch();
  }

  @override
  void dispose() {
    soundPlayer.closePlayer();
    super.dispose();
  }

  void fetch() async {
    final results = await Future.wait([
      Net.getRestClient().getEnglishDict("2", "mobile", widget.word, "wifi", "5.1",
          "%7B%22count%22%3A99%2C%22dicts%22%3A%5B%5B%22ec%22%2C%22collins%22%2C%22input%22%2C%22ee%22%2C%22syno%22%2C%22rel_word%22%2C%22blng_sents_part%22%5D%2C%5B%22web_trans%22%5D%5D%7D"),
      soundPlayer.openPlayer(),
    ]);

    final dictData = results[0] as EnglishDictModel;
    _initData(dictData);

    await soundPlayer.openPlayer();
  }

  void _initData(EnglishDictModel englishDictModel) {
    logger("englishDictModel.ec?.source?.name = ${englishDictModel.ec?.source?.name}");
    setState(() {
      usphone = englishDictModel.simple?.word?.firstOrNull?.usphone ?? "";
      ukphone = englishDictModel.simple?.word?.firstOrNull?.ukphone ?? "";
      wfs = englishDictModel.ec?.word?.firstOrNull?.wfs ?? [];
      trsList = englishDictModel.ec?.word?.firstOrNull?.trs ?? [];
      relList = englishDictModel.relWord?.rels
              ?.where((rel) => rel.rel != null) // Filter out null RelSub
              .map((rel) => rel.rel!)
              .toList() ??
          [];
    });
    if (widget.autoPlayDict) {
      playWord(isUs ? 0 : 1);
    }
  }

  void playWord(int type) {
    String url = "https://dict.youdao.com/dictvoice?type=$type&audio=${widget.word}";
    logger("playWord $url");
    try {
      soundPlayer.startPlayer(fromURI: url);
    } catch (e) {
      logger("playWord 单词播放失败: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    // 返回一个空的容器，因为 WoltModalSheet.show 会直接显示模态框
    return const SizedBox.shrink();
  }
}

// 创建一个新的 StatefulWidget 来处理字典内容
class _DictContent extends StatefulWidget {
  final String word;
  final bool autoPlayDict;

  const _DictContent({required this.word, required this.autoPlayDict});

  @override
  State<_DictContent> createState() => _DictContentState();
}

class _DictContentState extends State<_DictContent> {
  String usphone = "";
  String ukphone = "";
  String simple = "";
  List<Wfs> wfs = [];
  List<Trs> trsList = [];
  List<RelSub> relList = [];
  FlutterSoundPlayer soundPlayer = FlutterSoundPlayer(logLevel: Level.off);
  // 用于切换美/英音
  bool isUs = true;

  @override
  void initState() {
    super.initState();
    if (Get.isRegistered<DetailController>()) {
      Get.find<DetailController>().detailState.videoKit.pause();
    }
    fetch();
  }

  @override
  void dispose() {
    soundPlayer.closePlayer();
    super.dispose();
  }

  void fetch() async {
    final results = await Future.wait([
      Net.getRestClient().getEnglishDict("2", "mobile", widget.word, "wifi", "5.1",
          "%7B%22count%22%3A99%2C%22dicts%22%3A%5B%5B%22ec%22%2C%22collins%22%2C%22input%22%2C%22ee%22%2C%22syno%22%2C%22rel_word%22%2C%22blng_sents_part%22%5D%2C%5B%22web_trans%22%5D%5D%7D"),
      soundPlayer.openPlayer(),
    ]);

    final dictData = results[0] as EnglishDictModel;
    _initData(dictData);

    await soundPlayer.openPlayer();
  }

  void _initData(EnglishDictModel englishDictModel) {
    logger("englishDictModel.ec?.source?.name = ${englishDictModel.ec?.source?.name}");
    setState(() {
      usphone = englishDictModel.simple?.word?.firstOrNull?.usphone ?? "";
      ukphone = englishDictModel.simple?.word?.firstOrNull?.ukphone ?? "";
      wfs = englishDictModel.ec?.word?.firstOrNull?.wfs ?? [];
      trsList = englishDictModel.ec?.word?.firstOrNull?.trs ?? [];
      relList = englishDictModel.relWord?.rels
              ?.where((rel) => rel.rel != null) // Filter out null RelSub
              .map((rel) => rel.rel!)
              .toList() ??
          [];
    });
    if (widget.autoPlayDict) {
      playWord(isUs ? 0 : 1);
    }
  }

  void playWord(int type) {
    String url = "https://dict.youdao.com/dictvoice?type=$type&audio=${widget.word}";
    logger("playWord $url");
    try {
      soundPlayer.startPlayer(fromURI: url);
    } catch (e) {
      logger("playWord 单词播放失败: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.word, style: Get.textTheme.headlineMedium),
                  // 音标和播放按钮跟随Switch切换
                  GestureDetector(
                    onTap: () => playWord(isUs ? 0 : 1),
                    child: Container(
                      padding: EdgeInsets.all(8.whs),
                      child: Text(
                        isUs ? usphone : ukphone,
                        style: Get.textTheme.bodyMedium,
                      ),
                    ),
                  ),
                ],
              ),
              CupertinoSlidingSegmentedControl<bool>(
                groupValue: isUs,
                onValueChanged: (val) {
                  if (val != null) {
                    setState(() {
                      isUs = val;
                    });
                  }
                },
                thumbColor: Colors.white,
                children: {
                  true: Text(
                    '美',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: isUs ? Colors.black : Colors.black.withAlpha(77),
                    ),
                  ),
                  false: Text(
                    '英',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: !isUs ? Colors.black : Colors.black.withAlpha(77),
                    ),
                  ),
                },
              ),
            ],
          ),
          ListView.builder(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: trsList.length,
            itemBuilder: (BuildContext context, int index) {
              return Text(trsList[index].tr?.firstOrNull?.l?.i?.firstOrNull ?? "", style: const TextStyle(fontSize: 15));
            },
          ),
          Gap(16.whs),
          ListView.builder(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: wfs.length,
            itemBuilder: (BuildContext context, int index) {
              return Text((wfs[index].wf?.name ?? "") + (wfs[index].wf?.value ?? ""), style: const TextStyle(fontSize: 15));
            },
          ),
          Gap(16.whs),
          Text("同根词", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.bold)),
          Gap(16.whs),
          // relList
          ListView.builder(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: relList.length,
            itemBuilder: (BuildContext context, int index) {
              String getWordsText(RelSub relSub) {
                if (relSub.words == null) return "";

                return relSub.words!.map((wordMap) {
                  String word = wordMap['word'] ?? '';
                  String tran = wordMap['tran'] ?? '';
                  return '$word $tran';
                }).join(' ');
              }

              return Text((relList[index].pos ?? "") + getWordsText(relList[index]), style: const TextStyle(fontSize: 15));
            },
          ),
          Gap(16.whs),
          Text("柯林斯", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.bold)),
          Gap(16.whs),
        ],
      ),
    );
  }
}
