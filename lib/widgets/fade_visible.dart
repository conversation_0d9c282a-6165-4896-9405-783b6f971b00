import 'package:flutter/material.dart';

class FadeVisibility extends StatefulWidget {
  final bool visible;
  final Widget child;
  final Duration duration;

  const FadeVisibility({
    Key? key,
    required this.visible,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  State<FadeVisibility> createState() => _FadeVisibilityState();
}

class _FadeVisibilityState extends State<FadeVisibility> {
  double _opacity = 0;
  Widget? _child;

  @override
  void initState() {
    super.initState();
    _child = widget.child;
    _updateOpacity();
  }

  @override
  void didUpdateWidget(covariant FadeVisibility oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.visible != oldWidget.visible) {
      _updateOpacity();
    }
  }

  void _updateOpacity() {
    setState(() {
      _opacity = widget.visible ? 1.0 : 0.0;
    });

    if (!widget.visible) {
      Future.delayed(widget.duration, () {
        // 在动画完成后，如果组件应该是不可见的，我们将其从树中移除
        if (_opacity == 0.0) {
          setState(() {
            _child = null;
          });
        }
      });
    } else {
      // 如果组件应该是可见的，确保它在树中
      setState(() {
        _child = widget.child;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _child == null
        ? SizedBox.shrink()
        : AnimatedOpacity(
            opacity: _opacity,
            duration: widget.duration,
            child: _child,
          );
  }
}
