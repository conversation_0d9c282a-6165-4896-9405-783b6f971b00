import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:intl/date_symbol_data_local.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:intl/intl.dart';
import 'package:lsenglish/utils/routes.dart';
import 'package:path/path.dart' as p;
import 'package:tuple/tuple.dart';

/// 点击一个文件夹，传入文件夹的路径，显示该文件夹下的文件和文件夹
/// 点击一个文件，打开
/// 返回上一层，返回上一层目录路径 [dir.parent.path]
class FileManager extends StatelessWidget {
  const FileManager({super.key, this.targetDirPath = "", this.chooseSubtitle = false});
  final String targetDirPath;
  final bool chooseSubtitle;

  Future<Tuple2<String, List<FileSystemEntity>>> fetchPath() async {
    initializeDateFormatting();
    var localDirPath = targetDirPath.isEmpty ? (await FileUtils().getSaveDir()).path : targetDirPath;
    // 当前路径下的文件夹和文件
    return Tuple2(localDirPath, FileUtils().getCurrentPathFiles(localDirPath));
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: fetchPath(),
      builder: (BuildContext context, AsyncSnapshot<Tuple2<String, List<FileSystemEntity>>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container();
        } else {
          return Scaffold(
              appBar: AppBar(
                title: Text(
                  p.basename(snapshot.data?.item1 ?? ""),
                  style: const TextStyle(color: Colors.black),
                ),
                centerTitle: true,
                backgroundColor: Colors.white,
                elevation: 0.0,
              ),
              body: ListView(
                physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                children: snapshot.data?.item2 == null
                    ? []
                    : snapshot.data!.item2.map((e) {
                        if (FileSystemEntity.isFileSync(e.path)) {
                          return _buildFileItem(e);
                        } else {
                          return _buildFolderItem(e);
                        }
                      }).toList(),
              ));
        }
      },
    );
  }

  Widget _buildFileItem(FileSystemEntity file) {
    String modifiedTime = DateFormat('yyyy-MM-dd HH:mm:ss', 'zh_CN').format(file.statSync().modified.toLocal());

    return InkWell(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(width: 0.5, color: Color(0xffe5e5e5))),
        ),
        child: ListTile(
          title: Text(file.path.substring(file.parent.path.length + 1)),
          subtitle: Text('$modifiedTime  ${FileUtils().getFileSize(file.statSync().size)}', style: const TextStyle(fontSize: 12.0)),
        ),
      ),
      onTap: () async {
        if (FileUtils().isSubtitleFile(file.path)) {
          var result = await Get.toNamed(Routes.SUBTITLE_PREVIEW, arguments: {'subtitlePath': file.path});
          //需要依次关闭所有的filemanager页面
          if (result != null && result['offAllFilemanager'] == true) {
            Get.back(result: {'offAllFilemanager': true, 'subtitlePath': result['subtitlePath']});
          }
        } else if (FileUtils().isVideoFile(file.path)) {
          RoutesUtil().goDetailByPath(file.path);
        } else {
          // OpenFilex.open("/data/data/uni.UNIE7FC6F0/app_crashrecord/1004");
          // if (await Permission.manageExternalStorage.request().isGranted) {
          //   OpenFile.open("/data/data/uni.UNIE7FC6F0/app_crashrecord/1004");
          // }
        }
      },
      onLongPress: () {},
    );
  }

  Widget _buildFolderItem(FileSystemEntity file) {
    String modifiedTime = DateFormat('yyyy-MM-dd HH:mm:ss', 'zh_CN').format(file.statSync().modified.toLocal());

    return InkWell(
      child: Container(
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(width: 0.5, color: Color(0xffe5e5e5))),
        ),
        child: ListTile(
          title: Row(
            children: <Widget>[
              Expanded(child: Text(file.path.substring(file.parent.path.length + 1))),
              Text(
                '${FileUtils().calculateFilesCountByFolder(Directory(file.path))}项',
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
          subtitle: Text(modifiedTime, style: const TextStyle(fontSize: 12.0)),
          trailing: const Icon(Icons.chevron_right),
        ),
      ),
      onTap: () async {
        var result = await Get.to(() => FileManager(targetDirPath: file.path), preventDuplicates: false);
        if (result != null && result['offAllFilemanager'] == true) {
          Get.back(result: {'offAllFilemanager': true, 'subtitlePath': result['subtitlePath']});
        }
      },
      onLongPress: () {},
    );
  }
}
