import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

////////////////////////////////////////////////////////////////////////////////
///垂直滑动的 ViewPage 里嵌套垂直滑动的 ListView

typedef NestPageViewWidgetBuilder = Widget Function(BuildContext context, PageController pageController, ScrollController scrollController, int pageIndex);
typedef ScrollDirectionCallback = void Function(bool isForward);

class NestPageView extends StatefulWidget {
  final NestPageViewWidgetBuilder builder;
  final PageController? pageController;
  final VoidCallback? onUserScrollStart;
  final VoidCallback? onUserScrollEnd;
  final ScrollDirectionCallback? onPageScrollDirection;

  const NestPageView({
    super.key,
    required this.builder,
    this.pageController,
    this.onUserScrollStart,
    this.onUserScrollEnd,
    this.onPageScrollDirection,
  });

  @override
  State<NestPageView> createState() => _NestPageViewState();
}

class _NestPageViewState extends State<NestPageView> {
  late PageController _pageController;
  late ScrollController _listScrollController;
  var useCustomPageController = false;
  ScrollController? _activeScrollController;
  Drag? _drag;
  bool _isScrolling = false;
  double _previousPagePosition = 0.0;
  Map<int, ScrollController> indexScrollController = {};

  Timer? _debounceTimer;
  //内容是否超过了一页
  var isContentExceedingPage = false;
  
  @override
  void initState() {
    super.initState();
    useCustomPageController = widget.pageController != null;
    _pageController = widget.pageController ?? PageController();
    _listScrollController = ScrollController();

    _pageController.addListener(_scrollListener);
    _listScrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _pageController.removeListener(_scrollListener);
    _listScrollController.removeListener(_scrollListener);
    if (!useCustomPageController) {
      _pageController.dispose();
    }
    _listScrollController.dispose();
    // 清理所有页面专用的ScrollController
    for (var controller in indexScrollController.values) {
      controller.dispose();
    }
    indexScrollController.clear();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // 获取或创建页面专用的ScrollController
  ScrollController _getScrollControllerForPage(int pageIndex) {
    if (!indexScrollController.containsKey(pageIndex)) {
      indexScrollController[pageIndex] = ScrollController();
    }
    return indexScrollController[pageIndex]!;
  }

  bool contentExceedingPage(ScrollController listScrollController, PageController pageController) {
    // 检查ScrollController是否有效
    if (!listScrollController.hasClients || !pageController.hasClients) {
      return false;
    }
    
    try {
      // 获取 listScrollController 的最大滚动范围
      double listViewMaxScrollExtent = listScrollController.position.maxScrollExtent;

      // 获取 pageController 的视口高度
      double pageViewHeight = pageController.position.viewportDimension;

      // 判断内容是否超过
      return listViewMaxScrollExtent > pageViewHeight;
    } catch (e) {
      // 如果出现异常，返回false
      return false;
    }
  }

  void _scrollListener() {
    if (_activeScrollController?.position.isScrollingNotifier.value == false) {
      if (_isScrolling) {
        _isScrolling = false;
      }
    } else {
      _isScrolling = true;
    }
    if (_activeScrollController == _pageController) {
      double currentPagePosition = _pageController.page ?? 0.0;
      if (currentPagePosition != _previousPagePosition) {
        bool isForward = currentPagePosition > _previousPagePosition;
        widget.onPageScrollDirection?.call(isForward);
        _previousPagePosition = currentPagePosition;
      }
    }
  }

  void _handleDragStart(DragStartDetails details) {
    if (!isContentExceedingPage) {
      _activeScrollController = _pageController;
      _drag = _pageController.position.drag(details, _disposeDrag);
      return;
    }

    // 获取当前页面的ScrollController
    int currentPage = _getCurrentPageIndex();
    ScrollController currentPageScrollController = _getScrollControllerForPage(currentPage);

    // ///先判断 Listview 是否可见或者可以调用
    // ///一般不可见时 hasClients false ，因为 PageView 也没有 keepAlive
    if (currentPageScrollController.hasClients == true) {
      ///获取 ListView 的  renderBox
      final RenderBox? renderBox = currentPageScrollController.position.context.storageContext.findRenderObject() as RenderBox?;

      ///判断触摸的位置是否在 ListView 内
      ///不在范围内一般是因为 ListView 已经滑动上去了，坐标位置和触摸位置不一致
      if (renderBox?.paintBounds.shift(renderBox.localToGlobal(Offset.zero)).contains(details.globalPosition) == true) {
        _activeScrollController = currentPageScrollController;
        _drag = _activeScrollController?.position.drag(details, _disposeDrag);
        return;
      }
    }

    ///这时候就可以认为是 PageView 需要滑动
    _activeScrollController = _pageController;
    _drag = _pageController.position.drag(details, _disposeDrag);
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    // 获取当前页面的ScrollController
    int currentPage = _getCurrentPageIndex();
    ScrollController currentPageScrollController = _getScrollControllerForPage(currentPage);
    
    if (_activeScrollController == currentPageScrollController) {
      // Check if scrolling up and at the bottom
      if (details.primaryDelta! < 0 &&
          //到了底部，切换到 PageView
          _activeScrollController?.position.pixels == _activeScrollController?.position.maxScrollExtent) {
        //切换相应的控制器
        _activeScrollController = _pageController;
        _drag?.cancel();
        _drag = _pageController.position
            .drag(DragStartDetails(globalPosition: details.globalPosition, localPosition: details.localPosition), _disposeDrag);
      }
      // Check if scrolling down and at the top
      else if (details.primaryDelta! > 0 &&
          //到了顶部，切换到 PageView
          _activeScrollController?.position.pixels == _activeScrollController?.position.minScrollExtent) {
        ///切换相应的控制器
        _activeScrollController = _pageController;
        _drag?.cancel();

        ///参考  Scrollable 里的，
        ///因为是切换控制器，也就是要更新 Drag
        ///拖拽流程要切换到 PageView 里，所以需要  DragStartDetails
        ///所以需要把 DragUpdateDetails 变成 DragStartDetails
        ///提取出 PageView 里的 Drag 相应 details
        _drag = _pageController.position
            .drag(DragStartDetails(globalPosition: details.globalPosition, localPosition: details.localPosition), _disposeDrag);
      }
    }
    _drag?.update(details);
  }

  void _handleDragEnd(DragEndDetails details) async {
    _drag?.end(details);
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      // 获取当前页面的ScrollController
      int currentPage = _getCurrentPageIndex();
      ScrollController currentPageScrollController = _getScrollControllerForPage(currentPage);
      isContentExceedingPage = contentExceedingPage(currentPageScrollController, _pageController);
    });
  }

  void _handleDragCancel() {
    _drag?.cancel();
  }

  ///拖拽结束了，释放  _drag
  void _disposeDrag() {
    _drag = null;
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification notification) {
        if (notification is ScrollStartNotification) {
          _isScrolling = true;
          widget.onUserScrollStart?.call();
        } else if (notification is ScrollEndNotification) {
          if (_isScrolling) {
            _isScrolling = false;
            widget.onUserScrollEnd?.call();
          }
        }
        return true;
      },
      child: RawGestureDetector(
        gestures: <Type, GestureRecognizerFactory>{
          VerticalDragGestureRecognizer: GestureRecognizerFactoryWithHandlers<VerticalDragGestureRecognizer>(() => VerticalDragGestureRecognizer(),
              (VerticalDragGestureRecognizer instance) {
            instance
              ..onStart = _handleDragStart
              ..onUpdate = _handleDragUpdate
              ..onEnd = _handleDragEnd
              ..onCancel = _handleDragCancel;
          })
        },
        behavior: HitTestBehavior.opaque,
        child: widget.builder(context, _pageController, _getScrollControllerForPage(_getCurrentPageIndex()), _getCurrentPageIndex()),
      ),
    );
  }

  // 安全获取当前页面索引
  int _getCurrentPageIndex() {
    try {
      if (_pageController.hasClients) {
        return _pageController.page?.round() ?? 0;
      }
    } catch (e) {
      // 如果出现异常，返回默认值
    }
    return 0;
  }
}
