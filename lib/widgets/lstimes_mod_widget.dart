import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../model/data_center_home_resp/episode.dart';

Future<void> showLsTimeModeDialog(
  String? resourceId,
  int? resourceType, {
  String? episodeName,
  int? currentLsTimes,
  int? targetLsTimes,
}) async {
  if (resourceId == null || resourceType == null) {
    return;
  }
  await Get.dialog(
    LsTimesModWidget(
      episodeName: episodeName,
      resourceId: resourceId,
      resourceType: resourceType,
      currentLsTimes: currentLsTimes,
      targetLsTimes: targetLsTimes,
    ),
  );
}

class LsTimesModWidget extends StatefulWidget {
  final String? episodeName;
  final String resourceId;
  final int resourceType;
  final int? currentLsTimes;
  final int? targetLsTimes;
  const LsTimesModWidget({
    super.key,
    this.episodeName,
    required this.resourceId,
    required this.resourceType,
    this.currentLsTimes,
    this.targetLsTimes,
  });

  @override
  State<LsTimesModWidget> createState() => _LsTimesModWidgetState();
}

class _LsTimesModWidgetState extends State<LsTimesModWidget> {
  var submiting = false;
  var submitFail = false;
  var currentLsTimes = 0;
  var targetLsTimes = 0;
  var episodeName = "";
  var episode = Episode();
  @override
  void initState() {
    super.initState();
    currentLsTimes = widget.currentLsTimes ?? 0;
    episodeName = widget.episodeName ?? "";
    targetLsTimes = widget.targetLsTimes ?? 0;
    if (widget.targetLsTimes == null || widget.currentLsTimes == null || widget.episodeName == null) {
      Net.getRestClient().dataEpisodeLsData(widget.resourceId, widget.resourceType).then((v) {
        episode = v.data.episode ?? Episode();
        setState(() {
          currentLsTimes = episode.currentLsTimes ?? 0;
          targetLsTimes = episode.targetLsTimes ?? 0;
          episodeName = episode.episodeName ?? "";
        });
      });
    }
  }

  void submit() {
    setState(() {
      submiting = true;
    });
    Net.getRestClient().modDataEpisode({
      'resourceId': widget.resourceId,
      'resourceType': widget.resourceType,
      'currentLsTimes': currentLsTimes + 1, //服务端只存进行到了哪个LS次数 这里UI显示的是完成的次数
    }).then((v) {
      Get.back();
    }).catchError((e) {
      setState(() {
        submiting = false;
        submitFail = true;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: Get.width * 0.9,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.whs),
        ),
        child: Padding(
          padding: EdgeInsets.all(20.whs),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () => Get.back(),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                    decoration: ShapeDecoration(
                      color: const Color(0x14747480),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                    child: Padding(
                        padding: EdgeInsets.all(4.whs),
                        child: Icon(
                          Icons.close,
                          size: 24.whs,
                        )),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 10.whs, right: 10.whs),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      episodeName,
                      style: TextStyle(fontSize: 32.whs, fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      "LS学习遍数",
                      style: TextStyle(fontSize: 32.whs, fontWeight: FontWeight.w600),
                    ),
                    Gap(24.whs),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '+ ',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 25.whs),
                        ),
                        Text(
                          '1',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 45.whs,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    Gap(24.whs),
                    GestureDetector(
                      onTap: () => submit(),
                      child: Container(
                        width: double.infinity,
                        height: 56.whs,
                        decoration: ShapeDecoration(
                          color: Get.theme.primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.whs),
                          ),
                        ),
                        child: Stack(
                          children: [
                            Visibility(
                              visible: !submiting,
                              child: Center(
                                child: Text(
                                  submitFail ? '重试' : '完成',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 17.whs,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            Visibility(visible: submiting, child: const Center(child: CircularProgressIndicator())),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Gap(10.whs),
            ],
          ),
        ),
      ),
    );
  }
}
