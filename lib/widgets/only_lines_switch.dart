import 'package:flutter/material.dart';

class OnlyLinesSwitch extends StatefulWidget {
  final bool initialActive;
  final double size;
  final Duration duration;
  final ValueChanged<bool>? onChanged;

  const OnlyLinesSwitch({
    Key? key,
    this.initialActive = false,
    this.size = 24,
    this.duration = const Duration(milliseconds: 300),
    this.onChanged,
  }) : super(key: key);

  @override
  State<OnlyLinesSwitch> createState() => _OnlyLinesSwitchState();
}

class _OnlyLinesSwitchState extends State<OnlyLinesSwitch>
    with SingleTickerProviderStateMixin {
  late bool isActive;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    isActive = widget.initialActive;
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
      value: isActive ? 1.0 : 0.0,
    );
  }

  void _toggle() {
    setState(() {
      isActive = !isActive;
      if (isActive) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onChanged?.call(isActive);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggle,
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              painter: _OnlyLinesPainter(_controller.value),
            );
          },
        ),
      ),
    );
  }
}

class _OnlyLinesPainter extends CustomPainter {
  final double progress; // 0.0=off, 1.0=active

  _OnlyLinesPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final double w = size.width;
    final double h = size.height;

    // 颜色定义
    const Color offColor = Color(0xFF625B71);
    const Color onColor = Colors.white;
    const Color green = Color(0xFF32D74B);

    // 底部圆圈
    final Paint circlePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..color = Color.lerp(offColor, onColor, progress)!;

    final Paint fillPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Color.lerp(offColor, onColor, progress)!;

    // 画三个底部圆圈（描边）
    canvas.drawCircle(Offset(w * 4 / 24, h * 19 / 24), w * 2 / 24, circlePaint);
    canvas.drawCircle(Offset(w * 20 / 24, h * 19 / 24), w * 2 / 24, circlePaint);
    canvas.drawCircle(Offset(w * 12 / 24, h * 19 / 24), w * 2 / 24, circlePaint);

    // 画两个填充圆
    canvas.drawCircle(Offset(w * 4 / 24, h * 18 / 24), w * 2 / 24, fillPaint);
    canvas.drawCircle(Offset(w * 20 / 24, h * 18 / 24), w * 2 / 24, fillPaint);

    // 顶部线条（渐变色）
    final Paint linePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..color = Color.lerp(offColor, green, progress)!;

    final Path topPath = Path();
    // 画顶部曲线（参考SVG path）
    // M20.7764 10C20.7764 10 20.655 9.15076 17.1403 5.63604C13.6256 2.12132 7.92713 2.12132 4.41241 5.63604C3.16713 6.88131 2.36306 8.40072 2.00019 10
    topPath.moveTo(w * 20.7764 / 24, h * 10 / 24);
    topPath.cubicTo(
      w * 20.655 / 24, h * 9.15076 / 24,
      w * 17.1403 / 24, h * 5.63604 / 24,
      w * 13.6256 / 24, h * 2.12132 / 24,
    );
    topPath.cubicTo(
      w * 7.92713 / 24, h * 2.12132 / 24,
      w * 4.41241 / 24, h * 5.63604 / 24,
      w * 3.16713 / 24, h * 6.88131 / 24,
    );
    topPath.cubicTo(
      w * 2.36306 / 24, h * 8.40072 / 24,
      w * 2.00019 / 24, h * 10 / 24,
      w * 2.00019 / 24, h * 10 / 24,
    );
    canvas.drawPath(topPath, linePaint);

    // 竖线
    final Path vLine = Path();
    vLine.moveTo(w * 20.7764 / 24, h * 10 / 24);
    vLine.lineTo(w * 20.7764 / 24, h * 4 / 24);
    canvas.drawPath(vLine, linePaint);

    // 横线
    final Path hLine = Path();
    hLine.moveTo(w * 20.7764 / 24, h * 10 / 24);
    hLine.lineTo(w * 14.7764 / 24, h * 10 / 24);
    canvas.drawPath(hLine, linePaint);
  }

  @override
  bool shouldRepaint(covariant _OnlyLinesPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
} 