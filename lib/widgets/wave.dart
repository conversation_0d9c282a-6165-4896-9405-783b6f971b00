import 'package:flutter/material.dart';
import 'dart:math';

import 'package:get/get.dart';

class WaveformWidget extends StatefulWidget {
  final double barWidth;
  final double barSpacing;
  final int animationDuration;
  final int delayBetweenBars;
  final double minHeight;
  final double maxHeight;
  final int randomizeDuration;
  final bool random;
  final Color barColor;
  final double? volume; // 新增音量参数，用于录音效果

  const WaveformWidget({
    super.key,
    required this.barWidth,
    required this.barSpacing,
    required this.animationDuration,
    required this.delayBetweenBars,
    required this.minHeight,
    required this.maxHeight,
    required this.randomizeDuration,
    required this.random,
    required this.barColor,
    this.volume, // 音量值 0.0-1.0
  });

  @override
  State<WaveformWidget> createState() => _WaveformWidgetState();
}

class _WaveformWidgetState extends State<WaveformWidget> with TickerProviderStateMixin {
  List<AnimationController> _controllers = [];
  List<Animation<double>> _animations = [];
  final Random _random = Random();
  bool _isRandomized = false;
  int _barCount = 0; // 动态计算的条形数量

  @override
  void initState() {
    super.initState();
    // 初始化时不需要立即创建控制器，等build方法中计算好条形数量后再创建
  }

  void _initializeControllers() {
    // 清理旧的控制器
    if (_controllers.isNotEmpty) {
      for (var controller in _controllers) {
        controller.dispose();
      }
    }
    
    _controllers = List.generate(_barCount, (_) => _createController(widget.animationDuration));
    _initializeAnimations();
    _startAnimation();
  }

  void _initializeAnimations() {
    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();
  }

  AnimationController _createController(int duration) {
    return AnimationController(
      vsync: this,
      duration: Duration(milliseconds: duration),
    );
  }

  void _startAnimation() async {
    if (_barCount == 0) return;
    
    int midLeft = _barCount ~/ 2 - 1;
    int midRight = _barCount ~/ 2;

    while (!_isRandomized && mounted) {
      for (int i = 0; i <= midLeft; i++) {
        await Future.delayed(Duration(milliseconds: widget.delayBetweenBars));
        if (!_isRandomized && mounted) {
          _controllers[midLeft - i].forward(from: 0.0).then((_) {
            if (mounted) _controllers[midLeft - i].reverse();
          });
          _controllers[midRight + i].forward(from: 0.0).then((_) {
            if (mounted) _controllers[midRight + i].reverse();
          });
        }
      }
      await Future.delayed(Duration(milliseconds: widget.animationDuration));
    }
  }

  void _randomizeBars() {
    _isRandomized = true;
    for (var controller in _controllers) {
      controller.duration = Duration(milliseconds: widget.randomizeDuration);
    }
    _animateRandomHeights();
  }

  void _animateRandomHeights() async {
    while (_isRandomized && mounted) {
      for (var i = 0; i < _controllers.length; i++) {
        // 如果有音量参数，使用音量来影响高度
        double targetHeight = widget.volume != null 
            ? widget.volume! * 0.8 + _random.nextDouble() * 0.2 // 音量占80%，随机占20%
            : _random.nextDouble();
            
        _animations[i] = Tween<double>(
          begin: _animations[i].value,
          end: targetHeight,
        ).animate(
          CurvedAnimation(parent: _controllers[i], curve: Curves.easeInOut),
        );

        _controllers[i].forward(from: 0.0).then((_) {
          if (mounted) _controllers[i].reverse();
        });
      }
      await Future.delayed(Duration(milliseconds: widget.randomizeDuration));
    }
  }

  void randomHandle() {
    if (widget.random) {
      _randomizeBars();
    } else {
      _resetBars();
    }
  }

  void _resetBars() {
    setState(() {
      _isRandomized = false;
      _initializeAnimations();
    });
    for (var controller in _controllers) {
      controller.duration = Duration(milliseconds: widget.animationDuration);
    }
    _startAnimation();
  }

  // 根据音量更新动画
  void updateVolume(double volume) {
    if (widget.volume != null && _isRandomized) {
      for (var i = 0; i < _controllers.length; i++) {
        double targetHeight = volume * 0.8 + _random.nextDouble() * 0.2;
        _animations[i] = Tween<double>(
          begin: _animations[i].value,
          end: targetHeight,
        ).animate(
          CurvedAnimation(parent: _controllers[i], curve: Curves.easeInOut),
        );
        _controllers[i].forward(from: 0.0);
      }
    }
  }

  @override
  void didUpdateWidget(covariant WaveformWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.random != oldWidget.random) {
      randomHandle();
    }
    if (widget.volume != oldWidget.volume && widget.volume != null) {
      updateVolume(widget.volume!);
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isLandscape = context.isLandscape;
    
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据可用空间计算条形数量
        double availableSpace = isLandscape ? constraints.maxHeight : constraints.maxWidth;
        double totalBarWidth = widget.barWidth + widget.barSpacing;
        int newBarCount = (availableSpace / totalBarWidth).floor();
        
        // 确保至少有3个条形
        newBarCount = newBarCount.clamp(3, 50);
        
        // 如果条形数量发生变化，重新初始化控制器
        if (newBarCount != _barCount) {
          _barCount = newBarCount;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _initializeControllers();
            }
          });
        }
        
        return Flex(
          direction: isLandscape ? Axis.vertical : Axis.horizontal,
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(_barCount, (i) {
            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isLandscape ? 0 : widget.barSpacing / 2, 
                vertical: isLandscape ? widget.barSpacing / 2 : 0
              ),
              child: AnimatedBuilder(
                animation: _animations.isNotEmpty ? _animations[i] : const AlwaysStoppedAnimation(0.0),
                builder: (context, child) {
                  return CustomPaint(
                    size: isLandscape 
                        ? Size(widget.maxHeight, widget.barWidth) 
                        : Size(widget.barWidth, widget.maxHeight),
                    painter: BarPainter(
                      _animations.isNotEmpty ? _animations[i].value : 0.0,
                      widget.minHeight,
                      widget.maxHeight,
                      widget.barColor,
                      isLandscape,
                    ),
                  );
                },
              ),
            );
          }),
        );
      },
    );
  }
}

class BarPainter extends CustomPainter {
  final double heightFactor;
  final double minHeight;
  final double maxHeight;
  final Color barColor;
  final bool isLandscape;

  BarPainter(this.heightFactor, this.minHeight, this.maxHeight, this.barColor, this.isLandscape);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = barColor
      ..style = PaintingStyle.fill;

    final barWidth = isLandscape ? size.height : size.width;
    final barHeight = minHeight + (heightFactor * (maxHeight - minHeight));
    final rect = isLandscape
        ? Rect.fromLTWH((size.width - barHeight) / 2, 0, barHeight, barWidth)
        : Rect.fromLTWH(0, (size.height - barHeight) / 2, barWidth, barHeight);
    final radius = Radius.circular(barWidth / 2);
    canvas.drawRRect(RRect.fromRectAndRadius(rect, radius), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
