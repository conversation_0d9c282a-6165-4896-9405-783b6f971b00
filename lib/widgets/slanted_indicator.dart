import 'package:flutter/material.dart';

class SlantedIndicator extends Decoration {
  final double height;
  final double radius;
  final Color color;

  const SlantedIndicator({
    required this.height,
    required this.radius,
    required this.color,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _SlantedBoxPainter(height, radius, color);
  }
}

class _SlantedBoxPainter extends BoxPainter {
  final double height;
  final double radius;
  final Color color;

  _SlantedBoxPainter(this.height, this.radius, this.color);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final double width = configuration.size!.width;
    final double tabHeight = configuration.size!.height;

    final Path path = Path()
      ..moveTo(offset.dx + height / 2, offset.dy + tabHeight - height)
      ..lineTo(offset.dx + width + height / 2, offset.dy + tabHeight - height)
      ..lineTo(offset.dx + width, offset.dy + tabHeight)
      ..lineTo(offset.dx, offset.dy + tabHeight)
      ..close();

    canvas.drawPath(path, paint);
  }
}
