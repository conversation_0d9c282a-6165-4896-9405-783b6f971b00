{"flutter": {"platforms": {"android": {"default": {"projectId": "ls100-36631", "appId": "1:75452424013:android:dbc395fe04739bccbb6104", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "ls100-36631", "appId": "1:75452424013:ios:014f8bdabc0caae4bb6104", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "ls100-36631", "appId": "1:75452424013:ios:7286dffa8390522bbb6104", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "ls100-36631", "configurations": {"android": "1:75452424013:android:dbc395fe04739bccbb6104", "ios": "1:75452424013:ios:014f8bdabc0caae4bb6104", "macos": "1:75452424013:ios:7286dffa8390522bbb6104", "web": "1:75452424013:web:055944f6ee660c5cbb6104"}}}}}}