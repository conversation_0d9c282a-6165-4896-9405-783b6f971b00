<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <queries>
        <package android:name="com.alicloud.databox" />
    </queries>

    <application>
        <activity
            android:name=".AuthActivity"
            android:launchMode="singleTask" />
        <activity-alias
            android:name="${applicationId}.ypauth.YPAuthActivity"
            android:exported="true"
            android:targetActivity=".AuthActivity">
            <!-- targetSdk >= 33 需要适配 intent-filter  -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
            <!--   浏览器授权回调    -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="ypauth${aliyunpan-appid}" />
            </intent-filter>
        </activity-alias>
    </application>
</manifest>
