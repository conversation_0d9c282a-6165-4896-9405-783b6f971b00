import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'apple_pay_plugin_method_channel.dart';

abstract class ApplePayPluginPlatform extends PlatformInterface {
  /// Constructs a ApplePayPluginPlatform.
  ApplePayPluginPlatform() : super(token: _token);

  static final Object _token = Object();

  static ApplePayPluginPlatform _instance = MethodChannelApplePayPlugin();

  /// The default instance of [ApplePayPluginPlatform] to use.
  ///
  /// Defaults to [MethodChannelApplePayPlugin].
  static ApplePayPluginPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [ApplePayPluginPlatform] when
  /// they register themselves.
  static set instance(ApplePayPluginPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<void> purchase(String productId, String orderId) async {
    throw UnimplementedError('purchase() has not been implemented.');
  }

  /// 发起退款
  Future<void> refund(int transactionId) async {
    throw UnimplementedError('refund() has not been implemented.');
  }
}
