import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'apple_pay_plugin_platform_interface.dart';

/// An implementation of [ApplePayPluginPlatform] that uses method channels.
class MethodChannelApplePayPlugin extends ApplePayPluginPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('apple_pay_plugin');

  /// 购买商品
  @override
  Future<void> purchase(String productId, String orderId) async {
    await methodChannel.invokeMethod('purchase', {
      'productId': productId,
      'orderId': orderId,
    });
  }

  /// 发起退款
  @override
  Future<void> refund(int transactionId) async {
    await methodChannel.invokeMethod('refund', {
      'transactionId': transactionId,
    });
  }
}
