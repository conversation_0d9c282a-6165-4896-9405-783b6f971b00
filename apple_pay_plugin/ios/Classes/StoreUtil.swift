import StoreKit
import UIKit

public enum StoreError: Error {  // 错误回调枚举
    case failedVerification
    case noProduct
}

public enum StoreState: Int64 {  // 支付状态
    case start  // 开始
    case pay  // 进行苹果支付
    case verifiedServer  // 服务器校验
    case userCancelled  // 用户取消
    case pending  // 等待（家庭用户才有的状态）
    case unowned
}

class StoreUtil: NSObject {
    typealias KStateBlock = (_ state: StoreState, _ param: [String: Any]?) -> Void
    var stateBlock: KStateBlock!  // 状态回调
    // 当前支付状态
    var state = StoreState.unowned {
        didSet {
            if stateBlock != nil {
                stateBlock(state, nil)
            }
        }
    }

    var updateListenerTask: Task<Void, Error>? = nil  // 支付事件监听

    static let shared = {
        let instance = StoreUtil()
        return instance
    }()

    private override init() {  // 单例需要保证private的私有性质
        super.init()
        Task {
            updateListenerTask = listenForTransactions()
        }
    }

    // 购买商品
    func purchase(productId: String, orderId: String) async throws {
        // 获取商品
        let products = await self.requestProductsFromAppstore(productIds: [productId])
        guard let product = products?.first else { return }
        // 购买
        let result = try await self.purchase(product, orderId: orderId)
        // await MBProgressHUD.hide()
        guard let res: Product.PurchaseResult = result else { return }
        // 验证购买结果
        _ = try await self.verifyPurchase(res)
    }

    // 获取App Store中的商品
    func requestProductsFromAppstore(productIds: [String]) async -> [Product]? {
        let products = try? await Product.products(for: Set.init(productIds))
        return products
    }

    // 购买商品
    private func purchase(_ product: Product, orderId: String) async throws -> Product
        .PurchaseResult?
    {
        let uuid = Product.PurchaseOption.appAccountToken(UUID.init(uuidString: orderId) ?? UUID())
        let result = try await product.purchase(options: [uuid])
        state = StoreState.pay
        return result
    }

    // 验证商品购买结果
    func verifyPurchase(_ result: Product.PurchaseResult) async throws -> Transaction? {
        switch result {
        case .success(let verificationResult):
            //处理成功
            let transaction = try await self.transactionFinish(verificationResult)
            return transaction

        case .userCancelled:
            state = StoreState.userCancelled
            return nil
        case .pending:
            state = StoreState.pending
            return nil
        default:
            state = StoreState.unowned
            return nil
        }
    }

    // 监听 Transaction
    func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            //Iterate through any transactions which didn't come from a direct call to `purchase()`.
            for await result in Transaction.updates {
                do {
                    _ = try await self.transactionFinish(result)
                } catch {
                    //StoreKit has a receipt it can read but it failed verification. Don't deliver content to the user.
                    print("Transaction failed verification")
                }
            }
        }
    }

    // 验证 Transaction
    func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        //Check if the transaction passes StoreKit verification.
        switch result {
        case .unverified:
            //StoreKit has parsed the JWS but failed verification. Don't deliver content to the user.
            throw StoreError.failedVerification
        case .verified(let safe):
            //If the transaction is verified, unwrap and return it.
            return safe
        }
    }

    func transactionFinish(_ verificationResult: VerificationResult<Transaction>) async throws
        -> Transaction?
    {
        //处理成功
        let transaction = try checkVerified(verificationResult)
        //Deliver content to the use
        try await updatePurchasedIdentifiers(transaction)
        //Always finish a transaction.
        await transaction.finish()  //一定等上传接口成功之后，才能finish
        return transaction
    }

    // 退订
    func refunRequest(for transactionId: UInt64, scene: UIWindowScene) async {
        do {
            _ = try await Transaction.beginRefundRequest(for: transactionId, in: scene)
        } catch {
            print("iap error")
        }
    }

    // 上传凭证到自己服务器
    func updatePurchasedIdentifiers(_ transaction: Transaction?) async throws {
        //         state = StoreState.verifiedServer
        //         let apptoken = transaction?.appAccountToken?.uuidString
        //         let transactionId =  transaction?.id
        //         print("apptoken uuidstring: \(String(describing: apptoken))")
        //         print("originalID \(String(describing: transaction?.originalID))")
        //         print("productID \(String(describing: transaction?.productID))")
        //         print("productType \(String(describing: transaction?.productType))")
        //         print("offerType \(String(describing: transaction?.offerType))")
        //         print("offerPaymentModeStringRepresentation \(String(describing: transaction?.offerPaymentModeStringRepresentation))")
        //         print("reasonStringRepresentation \(String(describing: transaction?.reasonStringRepresentation))")

        // //        await MBProgressHUD.showLoding(withMessage: "123123", view: nil)
        //         let verify = MRKVerifyReceipt()
        //         try await verify.verifyReceipt(orderNo: apptoken, transactionId: transactionId)
        //        await MBProgressHUD.hide()
    }
}
