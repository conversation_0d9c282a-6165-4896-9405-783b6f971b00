import Flutter
import StoreKit
import UIKit

public class ApplePayPlugin: NSObject, FlutterPlugin {

  let store = StoreUtil.shared

  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(
      name: "apple_pay_plugin", binaryMessenger: registrar.messenger())
    let instance = ApplePayPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "purchase":
      guard let args = call.arguments as? [String: Any],
        let productId = args["productId"] as? String,
        let orderId = args["orderId"] as? String
      else {
        result(
          FlutterError(
            code: "INVALID_ARGUMENTS", message: "Invalid arguments for purchase", details: nil))
        return
      }

      Task {
        do {
          try await store.purchase(productId: productId, orderId: orderId)
          result("Purchase successful")
        } catch {
          result(
            FlutterError(
              code: "PURCHASE_FAILED", message: "Purchase failed: \(error)", details: nil))
        }
      }

    case "refund":
      guard let args = call.arguments as? [String: Any],
        let transactionId = args["transactionId"] as? UInt64,
        let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
      else {
        result(
          FlutterError(
            code: "INVALID_ARGUMENTS", message: "Invalid arguments for refund", details: nil))
        return
      }

      Task {
        await store.refunRequest(for: transactionId, scene: windowScene)
        result("Refund initiated")
      }

    default:
      result(FlutterMethodNotImplemented)
    }
  }
}
