#!/bin/bash

# 进入 android 目录
cd "$(dirname "$0")/android" || exit 1

echo "开始持续构建... (Ctrl+C 退出)"

while true; do
  echo "\n==== 执行 ./gradlew build ===="
  ./gradlew build
  if [ $? -ne 0 ]; then
    echo "\n==== ./gradlew build 失败，5秒后重试... ===="
    sleep 5
    continue
  fi
  echo "\n==== 执行 ./gradlew assembleDebug ===="
  ./gradlew assembleDebug
  if [ $? -ne 0 ]; then
    echo "\n==== ./gradlew assembleDebug 失败，5秒后重试... ===="
    sleep 5
    continue
  fi
  echo "\n==== 构建全部成功，退出循环 ===="
  break
  # 如果想要持续构建，可以去掉 break，改为 sleep 5
  # sleep 5

done 